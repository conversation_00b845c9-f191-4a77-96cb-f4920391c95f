# Generated by Django 5.2 on 2025-07-11 19:36

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0018_remove_carrating_unique_car_customer_rating_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='user',
            name='bio',
            field=models.TextField(blank=True, help_text='Brief description about yourself', max_length=500),
        ),
        migrations.AddField(
            model_name='user',
            name='city',
            field=models.CharField(blank=True, max_length=100),
        ),
        migrations.AddField(
            model_name='user',
            name='country',
            field=models.Char<PERSON>ield(default='Kenya', max_length=100),
        ),
        migrations.AddField(
            model_name='user',
            name='date_of_birth',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='email_notifications',
            field=models.BooleanField(default=True),
        ),
        migrations.Add<PERSON>ield(
            model_name='user',
            name='gender',
            field=models.Char<PERSON>ield(blank=True, choices=[('male', 'Male'), ('female', 'Female'), ('other', 'Other'), ('prefer_not_to_say', 'Prefer not to say')], max_length=20),
        ),
        migrations.AddField(
            model_name='user',
            name='last_login_ip',
            field=models.GenericIPAddressField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='marketing_emails',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='user',
            name='newsletter_subscription',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='user',
            name='preferred_language',
            field=models.CharField(choices=[('en', 'English'), ('sw', 'Swahili'), ('fr', 'French')], default='en', max_length=5),
        ),
        migrations.AddField(
            model_name='user',
            name='profile_picture',
            field=models.ImageField(blank=True, null=True, upload_to='profile_pictures/'),
        ),
        migrations.AddField(
            model_name='user',
            name='profile_visibility',
            field=models.CharField(choices=[('public', 'Public'), ('private', 'Private'), ('contacts_only', 'Contacts Only')], default='public', max_length=20),
        ),
        migrations.AddField(
            model_name='user',
            name='secondary_phone',
            field=models.CharField(blank=True, max_length=20),
        ),
        migrations.AddField(
            model_name='user',
            name='show_email',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='user',
            name='show_phone',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='user',
            name='sms_notifications',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='user',
            name='timezone',
            field=models.CharField(default='Africa/Nairobi', max_length=50),
        ),
        migrations.AddField(
            model_name='user',
            name='whatsapp_number',
            field=models.CharField(blank=True, max_length=20),
        ),
        migrations.AddField(
            model_name='vendor',
            name='accepts_installments',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='vendor',
            name='allow_direct_messages',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='vendor',
            name='auto_response_delay_minutes',
            field=models.PositiveIntegerField(default=5),
        ),
        migrations.AddField(
            model_name='vendor',
            name='auto_response_enabled',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='vendor',
            name='auto_response_message',
            field=models.TextField(blank=True, max_length=500),
        ),
        migrations.AddField(
            model_name='vendor',
            name='business_email',
            field=models.EmailField(blank=True, max_length=254),
        ),
        migrations.AddField(
            model_name='vendor',
            name='business_phone',
            field=models.CharField(blank=True, max_length=20),
        ),
        migrations.AddField(
            model_name='vendor',
            name='business_type',
            field=models.CharField(choices=[('dealership', 'Car Dealership'), ('spare_parts', 'Spare Parts Seller'), ('both', 'Both Cars and Spare Parts'), ('service_center', 'Service Center'), ('individual', 'Individual Seller')], default='dealership', max_length=20),
        ),
        migrations.AddField(
            model_name='vendor',
            name='company_logo',
            field=models.ImageField(blank=True, null=True, upload_to='vendor_logos/'),
        ),
        migrations.AddField(
            model_name='vendor',
            name='cover_image',
            field=models.ImageField(blank=True, null=True, upload_to='vendor_covers/'),
        ),
        migrations.AddField(
            model_name='vendor',
            name='facebook_url',
            field=models.URLField(blank=True),
        ),
        migrations.AddField(
            model_name='vendor',
            name='instagram_url',
            field=models.URLField(blank=True),
        ),
        migrations.AddField(
            model_name='vendor',
            name='linkedin_url',
            field=models.URLField(blank=True),
        ),
        migrations.AddField(
            model_name='vendor',
            name='minimum_deposit_percentage',
            field=models.PositiveIntegerField(default=20, help_text='Minimum deposit percentage for installments'),
        ),
        migrations.AddField(
            model_name='vendor',
            name='mpesa_business_shortcode',
            field=models.CharField(blank=True, max_length=10),
        ),
        migrations.AddField(
            model_name='vendor',
            name='number_of_employees',
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='vendor',
            name='operates_friday',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='vendor',
            name='operates_monday',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='vendor',
            name='operates_saturday',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='vendor',
            name='operates_sunday',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='vendor',
            name='operates_thursday',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='vendor',
            name='operates_tuesday',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='vendor',
            name='operates_wednesday',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='vendor',
            name='physical_address',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='vendor',
            name='profile_views',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='vendor',
            name='promotion_notifications',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='vendor',
            name='service_areas',
            field=models.TextField(blank=True, help_text='Areas where services are provided'),
        ),
        migrations.AddField(
            model_name='vendor',
            name='show_business_hours',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='vendor',
            name='specializations',
            field=models.TextField(blank=True, help_text='Comma-separated list of specializations'),
        ),
        migrations.AddField(
            model_name='vendor',
            name='swift_code',
            field=models.CharField(blank=True, max_length=20),
        ),
        migrations.AddField(
            model_name='vendor',
            name='total_inquiries',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='vendor',
            name='total_listings',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='vendor',
            name='twitter_url',
            field=models.URLField(blank=True),
        ),
        migrations.AddField(
            model_name='vendor',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='vendor',
            name='verification_documents',
            field=models.TextField(blank=True, help_text='JSON formatted verification documents'),
        ),
        migrations.AddField(
            model_name='vendor',
            name='verification_status',
            field=models.CharField(choices=[('pending', 'Pending Verification'), ('verified', 'Verified'), ('rejected', 'Rejected'), ('suspended', 'Suspended')], default='pending', max_length=20),
        ),
        migrations.AddField(
            model_name='vendor',
            name='year_established',
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='vendor',
            name='youtube_url',
            field=models.URLField(blank=True),
        ),
    ]
