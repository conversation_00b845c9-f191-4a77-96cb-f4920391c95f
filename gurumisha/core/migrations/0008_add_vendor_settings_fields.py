# Generated by Django 5.2 on 2025-07-10 14:05

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0007_alter_importrequest_status'),
    ]

    operations = [
        migrations.AddField(
            model_name='vendor',
            name='account_name',
            field=models.CharField(blank=True, max_length=200),
        ),
        migrations.AddField(
            model_name='vendor',
            name='account_number',
            field=models.CharField(blank=True, max_length=50),
        ),
        migrations.AddField(
            model_name='vendor',
            name='auto_approve_inquiries',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='vendor',
            name='bank_name',
            field=models.Char<PERSON>ield(blank=True, max_length=100),
        ),
        migrations.AddField(
            model_name='vendor',
            name='business_hours',
            field=models.TextField(blank=True, help_text='JSON formatted business hours'),
        ),
        migrations.AddField(
            model_name='vendor',
            name='business_hours_note',
            field=models.TextField(blank=True),
        ),
        migrations.AddField(
            model_name='vendor',
            name='email_notifications',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='vendor',
            name='inquiry_notifications',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='vendor',
            name='mpesa_number',
            field=models.CharField(blank=True, max_length=15),
        ),
        migrations.AddField(
            model_name='vendor',
            name='order_notifications',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='vendor',
            name='payment_methods',
            field=models.TextField(blank=True, help_text='JSON formatted payment methods'),
        ),
        migrations.AddField(
            model_name='vendor',
            name='public_profile',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='vendor',
            name='show_contact',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='vendor',
            name='sms_notifications',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='vendor',
            name='timezone',
            field=models.CharField(default='Africa/Nairobi', max_length=50),
        ),
    ]
