# Generated by Django 5.2.4 on 2025-07-11 18:39

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0017_add_hotdeal_rating_analytics_models'),
    ]

    operations = [
        migrations.RemoveConstraint(
            model_name='carrating',
            name='unique_car_customer_rating',
        ),
        migrations.RenameIndex(
            model_name='promotionanalytics',
            new_name='core_promot_metric__5f1860_idx',
            old_name='core_promotionanalytics_metric_date_idx',
        ),
        migrations.RenameIndex(
            model_name='promotionanalytics',
            new_name='core_promot_car_id_1e6dbd_idx',
            old_name='core_promotionanalytics_car_metric_idx',
        ),
        migrations.RenameIndex(
            model_name='promotionanalytics',
            new_name='core_promot_vendor__35908b_idx',
            old_name='core_promotionanalytics_vendor_metric_idx',
        ),
        migrations.RemoveField(
            model_name='car',
            name='featured_tier',
        ),
        migrations.AddField(
            model_name='car',
            name='is_certified',
            field=models.BooleanField(default=False, help_text='Mark car as certified with additional benefits'),
        ),
        migrations.AddField(
            model_name='car',
            name='is_featured',
            field=models.BooleanField(default=False, help_text='Mark car as featured'),
        ),
        migrations.AlterField(
            model_name='car',
            name='star_rating',
            field=models.PositiveIntegerField(default=0, help_text='Legacy star rating from 0-5'),
        ),
        migrations.AlterUniqueTogether(
            name='carrating',
            unique_together={('car', 'customer')},
        ),
    ]
