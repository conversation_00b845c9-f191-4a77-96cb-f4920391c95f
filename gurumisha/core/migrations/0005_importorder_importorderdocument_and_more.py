# Generated by Django 5.2 on 2025-07-09 11:45

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0004_order_payment_system'),
    ]

    operations = [
        migrations.CreateModel(
            name='ImportOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_number', models.CharField(max_length=50, unique=True)),
                ('brand', models.CharField(max_length=100)),
                ('model', models.CharField(max_length=100)),
                ('year', models.PositiveIntegerField()),
                ('color', models.CharField(blank=True, max_length=50)),
                ('engine_size', models.CharField(blank=True, max_length=50)),
                ('fuel_type', models.Char<PERSON>ield(blank=True, max_length=20)),
                ('transmission', models.Char<PERSON>ield(blank=True, max_length=20)),
                ('mileage', models.PositiveIntegerField(blank=True, help_text='Mileage in kilometers', null=True)),
                ('origin_country', models.CharField(max_length=100)),
                ('origin_city', models.CharField(blank=True, max_length=100)),
                ('status', models.CharField(choices=[('quotation_pending', 'Quotation Pending'), ('confirmed', 'Confirmed'), ('auction_won', 'Auction Won'), ('shipped', 'Shipped'), ('in_transit', 'In Transit'), ('arrived_docked', 'Arrived - Docked'), ('under_clearance', 'Under Clearance'), ('registered', 'Registered'), ('ready_for_dispatch', 'Ready for Dispatch'), ('delivered', 'Delivered'), ('cancelled', 'Cancelled')], default='quotation_pending', max_length=30)),
                ('payment_status', models.CharField(choices=[('pending', 'Pending'), ('partial', 'Partial Payment'), ('paid', 'Fully Paid'), ('refunded', 'Refunded')], default='pending', max_length=20)),
                ('quotation_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('total_cost', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('paid_amount', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('balance_due', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('auction_house', models.CharField(blank=True, max_length=200)),
                ('auction_date', models.DateField(blank=True, null=True)),
                ('winning_bid_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('chassis_number', models.CharField(blank=True, max_length=100, null=True, unique=True)),
                ('engine_number', models.CharField(blank=True, max_length=100)),
                ('bill_of_lading', models.CharField(blank=True, max_length=100)),
                ('vessel_name', models.CharField(blank=True, max_length=200)),
                ('departure_port', models.CharField(blank=True, max_length=100)),
                ('departure_date', models.DateField(blank=True, null=True)),
                ('arrival_port', models.CharField(default='Mombasa', max_length=100)),
                ('estimated_arrival_date', models.DateField(blank=True, null=True)),
                ('actual_arrival_date', models.DateField(blank=True, null=True)),
                ('customs_reference', models.CharField(blank=True, max_length=100)),
                ('duty_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('duty_paid_date', models.DateField(blank=True, null=True)),
                ('registration_number', models.CharField(blank=True, max_length=20)),
                ('registration_date', models.DateField(blank=True, null=True)),
                ('delivery_address', models.TextField(blank=True)),
                ('delivery_date', models.DateField(blank=True, null=True)),
                ('delivery_contact_person', models.CharField(blank=True, max_length=200)),
                ('delivery_contact_phone', models.CharField(blank=True, max_length=20)),
                ('special_requirements', models.TextField(blank=True)),
                ('admin_notes', models.TextField(blank=True)),
                ('customer_notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='import_orders', to=settings.AUTH_USER_MODEL)),
                ('import_request', models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='import_order', to='core.importrequest')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ImportOrderDocument',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('document_type', models.CharField(choices=[('quotation', 'Quotation'), ('invoice', 'Invoice'), ('payment_receipt', 'Payment Receipt'), ('auction_certificate', 'Auction Certificate'), ('inspection_report', 'Inspection Report'), ('bill_of_lading', 'Bill of Lading'), ('shipping_manifest', 'Shipping Manifest'), ('customs_declaration', 'Customs Declaration'), ('duty_payment_receipt', 'Duty Payment Receipt'), ('registration_certificate', 'Registration Certificate'), ('delivery_receipt', 'Delivery Receipt'), ('other', 'Other Document')], max_length=30)),
                ('title', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('document_file', models.FileField(upload_to='import_orders/documents/%Y/%m/')),
                ('file_size', models.PositiveIntegerField(blank=True, help_text='File size in bytes', null=True)),
                ('is_customer_visible', models.BooleanField(default=True, help_text='Whether customer can view this document')),
                ('is_confidential', models.BooleanField(default=False, help_text='Mark as confidential (admin only)')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('import_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='documents', to='core.importorder')),
                ('uploaded_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='uploaded_documents', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ImportOrderStatusHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('previous_status', models.CharField(blank=True, choices=[('quotation_pending', 'Quotation Pending'), ('confirmed', 'Confirmed'), ('auction_won', 'Auction Won'), ('shipped', 'Shipped'), ('in_transit', 'In Transit'), ('arrived_docked', 'Arrived - Docked'), ('under_clearance', 'Under Clearance'), ('registered', 'Registered'), ('ready_for_dispatch', 'Ready for Dispatch'), ('delivered', 'Delivered'), ('cancelled', 'Cancelled')], max_length=30)),
                ('new_status', models.CharField(choices=[('quotation_pending', 'Quotation Pending'), ('confirmed', 'Confirmed'), ('auction_won', 'Auction Won'), ('shipped', 'Shipped'), ('in_transit', 'In Transit'), ('arrived_docked', 'Arrived - Docked'), ('under_clearance', 'Under Clearance'), ('registered', 'Registered'), ('ready_for_dispatch', 'Ready for Dispatch'), ('delivered', 'Delivered'), ('cancelled', 'Cancelled')], max_length=30)),
                ('change_reason', models.TextField(blank=True, help_text='Reason for status change')),
                ('admin_notes', models.TextField(blank=True, help_text='Internal notes for this status change')),
                ('customer_notification_sent', models.BooleanField(default=False)),
                ('estimated_date', models.DateField(blank=True, help_text='Estimated date for next milestone', null=True)),
                ('actual_date', models.DateField(blank=True, help_text='Actual date when status was achieved', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('changed_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='status_changes_made', to=settings.AUTH_USER_MODEL)),
                ('import_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='status_history', to='core.importorder')),
            ],
            options={
                'verbose_name': 'Import Order Status History',
                'verbose_name_plural': 'Import Order Status Histories',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddIndex(
            model_name='importorder',
            index=models.Index(fields=['order_number'], name='core_import_order_n_3a68bc_idx'),
        ),
        migrations.AddIndex(
            model_name='importorder',
            index=models.Index(fields=['chassis_number'], name='core_import_chassis_d0b419_idx'),
        ),
        migrations.AddIndex(
            model_name='importorder',
            index=models.Index(fields=['status'], name='core_import_status_a05535_idx'),
        ),
        migrations.AddIndex(
            model_name='importorder',
            index=models.Index(fields=['customer', 'status'], name='core_import_custome_3d9f32_idx'),
        ),
        migrations.AddIndex(
            model_name='importorderdocument',
            index=models.Index(fields=['import_order', 'document_type'], name='core_import_import__dc2fac_idx'),
        ),
        migrations.AddIndex(
            model_name='importorderdocument',
            index=models.Index(fields=['document_type'], name='core_import_documen_800d58_idx'),
        ),
    ]
