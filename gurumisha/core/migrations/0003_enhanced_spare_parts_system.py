# Generated by Django 5.2 on 2025-07-09 07:48

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0002_car_listing_type'),
    ]

    operations = [
        migrations.CreateModel(
            name='Supplier',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=200)),
                ('contact_person', models.Char<PERSON>ield(blank=True, max_length=100)),
                ('email', models.EmailField(blank=True, max_length=254)),
                ('phone', models.CharField(blank=True, max_length=20)),
                ('address', models.TextField(blank=True)),
                ('website', models.URLField(blank=True)),
                ('tax_number', models.CharField(blank=True, max_length=50)),
                ('payment_terms', models.Char<PERSON><PERSON>(blank=True, help_text='e.g., Net 30 days', max_length=100)),
                ('is_active', models.BooleanField(default=True)),
                ('rating', models.DecimalField(blank=True, decimal_places=2, help_text='Supplier rating out of 5', max_digits=3, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='sparepart',
            name='barcode',
            field=models.CharField(blank=True, max_length=100, null=True, unique=True),
        ),
        migrations.AddField(
            model_name='sparepart',
            name='cost_price',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Purchase cost', max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='sparepart',
            name='dimensions',
            field=models.CharField(blank=True, help_text='L x W x H in cm', max_length=100),
        ),
        migrations.AddField(
            model_name='sparepart',
            name='discount_price',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='sparepart',
            name='is_discontinued',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='sparepart',
            name='is_featured',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='sparepart',
            name='maximum_stock',
            field=models.PositiveIntegerField(default=100, help_text='Maximum stock level'),
        ),
        migrations.AddField(
            model_name='sparepart',
            name='minimum_stock',
            field=models.PositiveIntegerField(default=5, help_text='Minimum stock level for reorder alerts'),
        ),
        migrations.AddField(
            model_name='sparepart',
            name='reorder_point',
            field=models.PositiveIntegerField(default=10, help_text='Automatic reorder trigger point'),
        ),
        migrations.AddField(
            model_name='sparepart',
            name='reorder_quantity',
            field=models.PositiveIntegerField(default=20, help_text='Quantity to reorder'),
        ),
        migrations.AddField(
            model_name='sparepart',
            name='reserved_quantity',
            field=models.PositiveIntegerField(default=0, help_text='Quantity reserved for pending orders'),
        ),
        migrations.AddField(
            model_name='sparepart',
            name='sku',
            field=models.CharField(default='TEMP-SKU', help_text='Stock Keeping Unit', max_length=100),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='sparepart',
            name='specifications',
            field=models.TextField(blank=True, help_text='Technical specifications'),
        ),
        migrations.AddField(
            model_name='sparepart',
            name='storage_conditions',
            field=models.CharField(blank=True, help_text='Special storage requirements', max_length=200),
        ),
        migrations.AddField(
            model_name='sparepart',
            name='unit',
            field=models.CharField(choices=[('piece', 'Piece'), ('set', 'Set'), ('pair', 'Pair'), ('kit', 'Kit'), ('liter', 'Liter'), ('meter', 'Meter')], default='piece', max_length=20),
        ),
        migrations.AddField(
            model_name='sparepart',
            name='warehouse_location',
            field=models.CharField(blank=True, help_text='Warehouse/shelf location', max_length=100),
        ),
        migrations.AddField(
            model_name='sparepart',
            name='weight',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Weight in kg', max_digits=8, null=True),
        ),
        migrations.AlterField(
            model_name='sparepart',
            name='price',
            field=models.DecimalField(decimal_places=2, help_text='Selling price', max_digits=10),
        ),
        migrations.CreateModel(
            name='InventoryAlert',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('alert_type', models.CharField(choices=[('low_stock', 'Low Stock'), ('out_of_stock', 'Out of Stock'), ('reorder', 'Reorder Required'), ('overstock', 'Overstock'), ('expired', 'Expired Items')], max_length=20)),
                ('status', models.CharField(choices=[('active', 'Active'), ('acknowledged', 'Acknowledged'), ('resolved', 'Resolved'), ('dismissed', 'Dismissed')], default='active', max_length=20)),
                ('message', models.TextField()),
                ('current_stock', models.PositiveIntegerField()),
                ('threshold_value', models.PositiveIntegerField(blank=True, null=True)),
                ('acknowledged_at', models.DateTimeField(blank=True, null=True)),
                ('resolved_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('acknowledged_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='acknowledged_alerts', to=settings.AUTH_USER_MODEL)),
                ('resolved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='resolved_alerts', to=settings.AUTH_USER_MODEL)),
                ('spare_part', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='alerts', to='core.sparepart')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PurchaseOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('order_number', models.CharField(max_length=50, unique=True)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('sent', 'Sent to Supplier'), ('confirmed', 'Confirmed'), ('partial', 'Partially Received'), ('completed', 'Completed'), ('cancelled', 'Cancelled')], default='draft', max_length=20)),
                ('order_date', models.DateTimeField(auto_now_add=True)),
                ('expected_delivery', models.DateField(blank=True, null=True)),
                ('actual_delivery', models.DateField(blank=True, null=True)),
                ('subtotal', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('shipping_cost', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=12)),
                ('notes', models.TextField(blank=True)),
                ('terms_conditions', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_purchase_orders', to=settings.AUTH_USER_MODEL)),
                ('vendor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='purchase_orders', to='core.vendor')),
                ('supplier', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='purchase_orders', to='core.supplier')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='PurchaseOrderItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity_ordered', models.PositiveIntegerField()),
                ('quantity_received', models.PositiveIntegerField(default=0)),
                ('unit_cost', models.DecimalField(decimal_places=2, max_digits=10)),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=12)),
                ('notes', models.TextField(blank=True)),
                ('purchase_order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='core.purchaseorder')),
                ('spare_part', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='core.sparepart')),
            ],
        ),
        migrations.CreateModel(
            name='SparePartCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='subcategories', to='core.sparepartcategory')),
            ],
            options={
                'verbose_name_plural': 'Spare Part Categories',
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='sparepart',
            name='category_new',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='parts', to='core.sparepartcategory'),
        ),
        migrations.CreateModel(
            name='SparePartImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to='spare_parts/gallery/')),
                ('caption', models.CharField(blank=True, max_length=200)),
                ('order', models.PositiveIntegerField(default=0)),
                ('spare_part', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='images', to='core.sparepart')),
            ],
            options={
                'ordering': ['order'],
            },
        ),
        migrations.CreateModel(
            name='StockMovement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('movement_type', models.CharField(choices=[('in', 'Stock In'), ('out', 'Stock Out'), ('adjustment', 'Stock Adjustment'), ('transfer', 'Stock Transfer'), ('return', 'Return'), ('damaged', 'Damaged/Lost')], max_length=20)),
                ('reason', models.CharField(choices=[('purchase', 'Purchase Order'), ('sale', 'Sale'), ('return', 'Customer Return'), ('adjustment', 'Stock Adjustment'), ('damaged', 'Damaged Goods'), ('expired', 'Expired'), ('transfer', 'Warehouse Transfer'), ('initial', 'Initial Stock')], max_length=20)),
                ('quantity', models.IntegerField(help_text='Positive for stock in, negative for stock out')),
                ('quantity_before', models.PositiveIntegerField()),
                ('quantity_after', models.PositiveIntegerField()),
                ('reference_number', models.CharField(blank=True, help_text='PO number, invoice number, etc.', max_length=100)),
                ('notes', models.TextField(blank=True)),
                ('unit_cost', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('purchase_order_item', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='core.purchaseorderitem')),
                ('spare_part', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stock_movements', to='core.sparepart')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='sparepart',
            name='supplier',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='spare_parts', to='core.supplier'),
        ),
    ]
