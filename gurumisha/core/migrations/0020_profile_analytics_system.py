# Generated by Django 5.2 on 2025-07-11 19:53

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0019_enhanced_profile_system'),
    ]

    operations = [
        migrations.CreateModel(
            name='VendorAnalytics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_profile_views', models.PositiveIntegerField(default=0)),
                ('unique_profile_views', models.PositiveIntegerField(default=0)),
                ('profile_views_this_month', models.PositiveIntegerField(default=0)),
                ('profile_views_last_month', models.PositiveIntegerField(default=0)),
                ('total_inquiries', models.PositiveIntegerField(default=0)),
                ('inquiries_this_month', models.PositiveIntegerField(default=0)),
                ('inquiry_response_rate', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('average_response_time_hours', models.PositiveIntegerField(default=24)),
                ('total_sales', models.PositiveIntegerField(default=0)),
                ('sales_this_month', models.PositiveIntegerField(default=0)),
                ('total_revenue', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('revenue_this_month', models.DecimalField(decimal_places=2, default=0.0, max_digits=12)),
                ('active_listings', models.PositiveIntegerField(default=0)),
                ('featured_listings', models.PositiveIntegerField(default=0)),
                ('sold_listings', models.PositiveIntegerField(default=0)),
                ('average_listing_views', models.PositiveIntegerField(default=0)),
                ('average_rating', models.DecimalField(decimal_places=2, default=0.0, max_digits=3)),
                ('total_ratings', models.PositiveIntegerField(default=0)),
                ('five_star_ratings', models.PositiveIntegerField(default=0)),
                ('four_star_ratings', models.PositiveIntegerField(default=0)),
                ('three_star_ratings', models.PositiveIntegerField(default=0)),
                ('two_star_ratings', models.PositiveIntegerField(default=0)),
                ('one_star_ratings', models.PositiveIntegerField(default=0)),
                ('profile_completion_score', models.PositiveIntegerField(default=0)),
                ('customer_satisfaction_score', models.PositiveIntegerField(default=0)),
                ('response_time_score', models.PositiveIntegerField(default=0)),
                ('overall_performance_score', models.PositiveIntegerField(default=0)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('vendor', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='analytics', to='core.vendor')),
            ],
        ),
        migrations.CreateModel(
            name='ProfileView',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('viewer_ip', models.GenericIPAddressField()),
                ('user_agent', models.TextField(blank=True)),
                ('referrer', models.URLField(blank=True)),
                ('session_key', models.CharField(blank=True, max_length=40)),
                ('viewed_at', models.DateTimeField(auto_now_add=True)),
                ('profile_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='profile_views_received', to=settings.AUTH_USER_MODEL)),
                ('viewer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='profile_views_made', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'indexes': [models.Index(fields=['profile_user', 'viewed_at'], name='core_profil_profile_26171d_idx'), models.Index(fields=['viewer', 'viewed_at'], name='core_profil_viewer__466162_idx'), models.Index(fields=['viewer_ip', 'viewed_at'], name='core_profil_viewer__115a0d_idx')],
            },
        ),
        migrations.CreateModel(
            name='UserActivityLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('profile_view', 'Profile View'), ('profile_update', 'Profile Update'), ('login', 'Login'), ('logout', 'Logout'), ('password_change', 'Password Change'), ('inquiry_sent', 'Inquiry Sent'), ('inquiry_received', 'Inquiry Received'), ('listing_created', 'Listing Created'), ('listing_updated', 'Listing Updated'), ('listing_viewed', 'Listing Viewed'), ('order_placed', 'Order Placed'), ('payment_made', 'Payment Made')], max_length=50)),
                ('description', models.TextField(blank=True)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True)),
                ('metadata', models.JSONField(blank=True, default=dict)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_activity_logs', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['user', 'timestamp'], name='core_userac_user_id_487384_idx'), models.Index(fields=['action', 'timestamp'], name='core_userac_action_fa4ed2_idx')],
            },
        ),
    ]
