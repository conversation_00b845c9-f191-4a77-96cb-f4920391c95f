# Generated by Django 5.2 on 2025-07-10 15:56

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('core', '0008_add_vendor_settings_fields'),
    ]

    operations = [
        migrations.CreateModel(
            name='NotificationTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('template_type', models.CharField(choices=[('email', 'Email'), ('sms', 'SMS'), ('push', 'Push Notification'), ('in_app', 'In-App Notification')], max_length=20)),
                ('subject_template', models.CharField(blank=True, max_length=200)),
                ('body_template', models.TextField()),
                ('available_variables', models.J<PERSON><PERSON>ield(default=list, help_text='List of available template variables')),
                ('is_active', models.BooleanField(default=True)),
                ('priority', models.IntegerField(default=1, help_text='1=Low, 2=Medium, 3=High, 4=Critical')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='NotificationPreference',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email_enabled', models.BooleanField(default=True)),
                ('email_order_updates', models.BooleanField(default=True)),
                ('email_import_updates', models.BooleanField(default=True)),
                ('email_inquiry_responses', models.BooleanField(default=True)),
                ('email_marketing', models.BooleanField(default=False)),
                ('email_security_alerts', models.BooleanField(default=True)),
                ('sms_enabled', models.BooleanField(default=False)),
                ('sms_order_updates', models.BooleanField(default=False)),
                ('sms_import_updates', models.BooleanField(default=False)),
                ('sms_security_alerts', models.BooleanField(default=True)),
                ('push_enabled', models.BooleanField(default=True)),
                ('push_order_updates', models.BooleanField(default=True)),
                ('push_import_updates', models.BooleanField(default=True)),
                ('push_inquiry_responses', models.BooleanField(default=True)),
                ('push_marketing', models.BooleanField(default=False)),
                ('in_app_enabled', models.BooleanField(default=True)),
                ('in_app_order_updates', models.BooleanField(default=True)),
                ('in_app_import_updates', models.BooleanField(default=True)),
                ('in_app_inquiry_responses', models.BooleanField(default=True)),
                ('in_app_system_updates', models.BooleanField(default=True)),
                ('digest_frequency', models.CharField(choices=[('immediate', 'Immediate'), ('hourly', 'Hourly'), ('daily', 'Daily'), ('weekly', 'Weekly')], default='immediate', max_length=20)),
                ('quiet_hours_enabled', models.BooleanField(default=False)),
                ('quiet_hours_start', models.TimeField(blank=True, null=True)),
                ('quiet_hours_end', models.TimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='notification_preferences', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Notification Preference',
                'verbose_name_plural': 'Notification Preferences',
            },
        ),
        migrations.CreateModel(
            name='NotificationQueue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('channel', models.CharField(choices=[('email', 'Email'), ('sms', 'SMS'), ('push', 'Push Notification'), ('in_app', 'In-App Notification')], max_length=20)),
                ('subject', models.CharField(blank=True, max_length=200)),
                ('message', models.TextField()),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('processing', 'Processing'), ('sent', 'Sent'), ('failed', 'Failed'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('priority', models.IntegerField(default=1, help_text='1=Low, 2=Medium, 3=High, 4=Critical')),
                ('scheduled_at', models.DateTimeField(blank=True, null=True)),
                ('sent_at', models.DateTimeField(blank=True, null=True)),
                ('retry_count', models.PositiveIntegerField(default=0)),
                ('max_retries', models.PositiveIntegerField(default=3)),
                ('error_message', models.TextField(blank=True)),
                ('context_data', models.JSONField(blank=True, default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notification_queue', to=settings.AUTH_USER_MODEL)),
                ('template', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='core.notificationtemplate')),
            ],
            options={
                'ordering': ['-priority', 'created_at'],
            },
        ),
        migrations.CreateModel(
            name='NotificationDeliveryLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('attempt_number', models.PositiveIntegerField()),
                ('delivery_status', models.CharField(choices=[('success', 'Success'), ('failed', 'Failed'), ('bounced', 'Bounced'), ('rejected', 'Rejected')], max_length=20)),
                ('response_code', models.CharField(blank=True, max_length=10)),
                ('response_message', models.TextField(blank=True)),
                ('provider', models.CharField(blank=True, max_length=50)),
                ('provider_message_id', models.CharField(blank=True, max_length=100)),
                ('attempted_at', models.DateTimeField(auto_now_add=True)),
                ('delivered_at', models.DateTimeField(blank=True, null=True)),
                ('notification_queue', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='delivery_logs', to='core.notificationqueue')),
            ],
            options={
                'ordering': ['-attempted_at'],
            },
        ),
        migrations.CreateModel(
            name='ActivityLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('login', 'User Login'), ('logout', 'User Logout'), ('register', 'User Registration'), ('password_change', 'Password Change'), ('profile_update', 'Profile Update'), ('settings_change', 'Settings Change'), ('avatar_upload', 'Avatar Upload'), ('car_create', 'Car Created'), ('car_update', 'Car Updated'), ('car_delete', 'Car Deleted'), ('car_view', 'Car Viewed'), ('car_approve', 'Car Approved'), ('car_reject', 'Car Rejected'), ('import_request_create', 'Import Request Created'), ('import_request_update', 'Import Request Updated'), ('import_status_change', 'Import Status Changed'), ('import_document_upload', 'Import Document Uploaded'), ('spare_part_create', 'Spare Part Created'), ('spare_part_update', 'Spare Part Updated'), ('spare_part_delete', 'Spare Part Deleted'), ('spare_part_view', 'Spare Part Viewed'), ('order_create', 'Order Created'), ('order_update', 'Order Updated'), ('order_cancel', 'Order Cancelled'), ('payment_made', 'Payment Made'), ('payment_failed', 'Payment Failed'), ('inquiry_create', 'Inquiry Created'), ('inquiry_respond', 'Inquiry Responded'), ('message_send', 'Message Sent'), ('user_approve', 'User Approved'), ('user_suspend', 'User Suspended'), ('vendor_approve', 'Vendor Approved'), ('system_setting_change', 'System Setting Changed'), ('search_performed', 'Search Performed'), ('filter_applied', 'Filter Applied'), ('page_view', 'Page Viewed'), ('file_upload', 'File Uploaded'), ('file_download', 'File Downloaded'), ('file_delete', 'File Deleted')], max_length=50)),
                ('description', models.TextField(help_text='Human-readable description of the action')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True)),
                ('session_key', models.CharField(blank=True, max_length=40)),
                ('object_id', models.PositiveIntegerField(blank=True, null=True)),
                ('extra_data', models.JSONField(blank=True, default=dict, help_text='Additional context data')),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('content_type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='activity_logs', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['user', '-timestamp'], name='core_activi_user_id_d7adda_idx'), models.Index(fields=['action', '-timestamp'], name='core_activi_action_1fc1d1_idx'), models.Index(fields=['content_type', 'object_id'], name='core_activi_content_1a7154_idx'), models.Index(fields=['-timestamp'], name='core_activi_timesta_8baae3_idx')],
            },
        ),
        migrations.CreateModel(
            name='AuditLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action_type', models.CharField(choices=[('create', 'Create'), ('read', 'Read'), ('update', 'Update'), ('delete', 'Delete'), ('login', 'Login'), ('logout', 'Logout'), ('permission_change', 'Permission Change'), ('data_export', 'Data Export'), ('data_import', 'Data Import'), ('system_config', 'System Configuration'), ('security_event', 'Security Event')], max_length=20)),
                ('table_name', models.CharField(blank=True, max_length=100)),
                ('record_id', models.CharField(blank=True, max_length=100)),
                ('field_name', models.CharField(blank=True, max_length=100)),
                ('old_value', models.TextField(blank=True)),
                ('new_value', models.TextField(blank=True)),
                ('description', models.TextField()),
                ('severity', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('critical', 'Critical')], default='low', max_length=10)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True)),
                ('request_path', models.CharField(blank=True, max_length=500)),
                ('request_method', models.CharField(blank=True, max_length=10)),
                ('extra_data', models.JSONField(blank=True, default=dict)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='audit_logs', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['user', '-timestamp'], name='core_auditl_user_id_2a1528_idx'), models.Index(fields=['action_type', '-timestamp'], name='core_auditl_action__f8907a_idx'), models.Index(fields=['table_name', 'record_id'], name='core_auditl_table_n_5422bf_idx'), models.Index(fields=['severity', '-timestamp'], name='core_auditl_severit_890175_idx'), models.Index(fields=['-timestamp'], name='core_auditl_timesta_189a84_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='notificationqueue',
            index=models.Index(fields=['status', 'priority'], name='core_notifi_status_c22334_idx'),
        ),
        migrations.AddIndex(
            model_name='notificationqueue',
            index=models.Index(fields=['recipient', '-created_at'], name='core_notifi_recipie_28a259_idx'),
        ),
        migrations.AddIndex(
            model_name='notificationqueue',
            index=models.Index(fields=['channel', 'status'], name='core_notifi_channel_2858d6_idx'),
        ),
        migrations.AddIndex(
            model_name='notificationqueue',
            index=models.Index(fields=['scheduled_at'], name='core_notifi_schedul_8b809e_idx'),
        ),
    ]
