# Generated by Django 5.2.4 on 2025-07-10 17:34

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0010_user_email_verification_sent_at_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='VerificationCode',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=10)),
                ('code_type', models.CharField(choices=[('email_verification', 'Email Verification'), ('password_reset', 'Password Reset'), ('two_factor', 'Two Factor Authentication')], default='email_verification', max_length=20)),
                ('email', models.EmailField(max_length=254)),
                ('is_used', models.BooleanField(default=False)),
                ('expires_at', models.DateTimeField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('used_at', models.DateTimeField(blank=True, null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='verification_codes', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['code', 'code_type', 'is_used'], name='core_verifi_code_900d57_idx'), models.Index(fields=['email', 'code_type'], name='core_verifi_email_1d6a5b_idx'), models.Index(fields=['expires_at'], name='core_verifi_expires_a598fc_idx')],
            },
        ),
    ]
