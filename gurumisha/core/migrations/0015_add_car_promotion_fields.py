# Generated by Django 5.2 on 2025-07-11 10:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0014_migrate_import_order_status_data'),
    ]

    operations = [
        migrations.AddField(
            model_name='car',
            name='is_hot_deal',
            field=models.BooleanField(default=False, help_text='Mark as hot deal for special promotion'),
        ),
        migrations.AddField(
            model_name='car',
            name='negotiable',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='car',
            name='star_rating',
            field=models.PositiveIntegerField(default=0, help_text='Star rating from 0-5 for featuring priority'),
        ),
    ]
