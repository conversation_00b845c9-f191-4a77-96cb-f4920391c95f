<!-- Real-time status update partial -->
<div class="flex items-center justify-between">
    <div>
        <span class="px-3 py-1 rounded-full text-sm font-medium
            {% if order.status == 'delivered' %}bg-green-100 text-green-800
            {% elif order.status == 'cancelled' %}bg-red-100 text-red-800
            {% elif order.status in 'in_transit,shipped' %}bg-blue-100 text-blue-800
            {% else %}bg-yellow-100 text-yellow-800{% endif %}">
            {{ order.get_status_display }}
        </span>
    </div>
    <div class="text-right">
        <div class="text-sm text-gray-600 mb-1">Progress: {{ progress_percentage }}%</div>
        <div class="w-32 bg-gray-200 rounded-full h-2">
            <div class="bg-harrier-red h-2 rounded-full transition-all duration-300" 
                 style="width: {{ progress_percentage }}%"></div>
        </div>
    </div>
</div>
