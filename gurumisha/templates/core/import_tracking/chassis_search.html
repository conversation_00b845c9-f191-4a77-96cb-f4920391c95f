{% extends 'base.html' %}
{% load static %}

{% block title %}Chassis Number Search - Import Tracking - <PERSON><PERSON><PERSON>{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="bg-harrier-dark py-16">
    <div class="container mx-auto px-4">
        <div class="text-center">
            <h1 class="text-4xl md:text-5xl font-heading font-bold text-white mb-4">
                CHASSIS NUMBER SEARCH
            </h1>
            <p class="text-xl text-gray-300">
                Track your import order using the vehicle chassis number
            </p>
        </div>
    </div>
</div>

<!-- Search Section -->
<section class="py-16 bg-harrier-gray">
    <div class="container mx-auto px-4">
        <div class="max-w-2xl mx-auto">
            <!-- Search Form -->
            <div class="bg-white rounded-lg shadow-xl p-8">
                <div class="text-center mb-8">
                    <div class="w-20 h-20 bg-harrier-red bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-search text-harrier-red text-3xl"></i>
                    </div>
                    <h2 class="text-2xl font-heading font-bold text-harrier-dark mb-2">Find Your Vehicle</h2>
                    <p class="text-gray-600">Enter your vehicle's chassis number to track its import progress</p>
                </div>

                <!-- Display Messages -->
                {% if messages %}
                    {% for message in messages %}
                        <div class="mb-6 p-4 rounded-lg {% if message.tags == 'error' %}bg-red-100 border border-red-400 text-red-700{% elif message.tags == 'success' %}bg-green-100 border border-green-400 text-green-700{% else %}bg-blue-100 border border-blue-400 text-blue-700{% endif %}">
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}

                <form action="{% url 'core:chassis_number_search' %}" method="get" class="space-y-6">
                    <div>
                        <label for="chassis_number" class="block text-sm font-medium text-harrier-dark mb-2">
                            Chassis Number
                        </label>
                        <input type="text" 
                               id="chassis_number"
                               name="chassis_number" 
                               placeholder="Enter chassis number (e.g., JF1GJ7D60DH123456)"
                               class="w-full px-4 py-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent text-lg"
                               required>
                        <p class="mt-2 text-sm text-gray-600">
                            The chassis number is typically 17 characters long and can be found on your vehicle documents.
                        </p>
                    </div>

                    <button type="submit" class="w-full btn-harrier-primary py-4 text-lg font-semibold">
                        <i class="fas fa-search mr-2"></i>SEARCH VEHICLE
                    </button>
                </form>

                <!-- Alternative Search Options -->
                <div class="mt-8 pt-6 border-t border-gray-200">
                    <p class="text-center text-gray-600 mb-4">Don't have your chassis number?</p>
                    <div class="flex flex-col sm:flex-row gap-3">
                        <a href="{% url 'core:import_order_tracking' %}" 
                           class="flex-1 btn-harrier-secondary text-center">
                            <i class="fas fa-list mr-2"></i>View All Orders
                        </a>
                        <a href="{% url 'core:contact_us' %}" 
                           class="flex-1 btn-harrier-outline text-center">
                            <i class="fas fa-phone mr-2"></i>Contact Support
                        </a>
                    </div>
                </div>
            </div>

            <!-- Information Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-12">
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-info-circle text-blue-600 text-xl"></i>
                        </div>
                        <h3 class="text-lg font-heading font-bold text-harrier-dark">Where to Find Chassis Number</h3>
                    </div>
                    <ul class="text-sm text-gray-600 space-y-2">
                        <li class="flex items-start">
                            <i class="fas fa-check text-green-500 mr-2 mt-1 text-xs"></i>
                            Vehicle registration documents
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-green-500 mr-2 mt-1 text-xs"></i>
                            Import quotation or invoice
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-green-500 mr-2 mt-1 text-xs"></i>
                            Auction certificate
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-green-500 mr-2 mt-1 text-xs"></i>
                            Bill of lading
                        </li>
                    </ul>
                </div>

                <div class="bg-white rounded-lg shadow-lg p-6">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-shield-alt text-green-600 text-xl"></i>
                        </div>
                        <h3 class="text-lg font-heading font-bold text-harrier-dark">Secure Tracking</h3>
                    </div>
                    <ul class="text-sm text-gray-600 space-y-2">
                        <li class="flex items-start">
                            <i class="fas fa-check text-green-500 mr-2 mt-1 text-xs"></i>
                            Real-time status updates
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-green-500 mr-2 mt-1 text-xs"></i>
                            Secure access to your data
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-green-500 mr-2 mt-1 text-xs"></i>
                            Document downloads
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check text-green-500 mr-2 mt-1 text-xs"></i>
                            Progress notifications
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Help Section -->
<section class="py-16 bg-white">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto text-center">
            <h2 class="text-3xl md:text-4xl font-heading font-bold text-harrier-dark mb-4">
                Need Assistance?
            </h2>
            <p class="text-xl text-gray-600 mb-8">
                Our import specialists are here to help you track your vehicle
            </p>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="text-center">
                    <div class="w-16 h-16 bg-harrier-red bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-phone text-harrier-red text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-heading font-bold text-harrier-dark mb-2">Call Us</h3>
                    <p class="text-gray-600 mb-4">Speak directly with our team</p>
                    <a href="tel:+254700000000" class="text-harrier-red font-medium hover:text-harrier-dark">
                        +254 700 000 000
                    </a>
                </div>
                
                <div class="text-center">
                    <div class="w-16 h-16 bg-harrier-red bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-envelope text-harrier-red text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-heading font-bold text-harrier-dark mb-2">Email Support</h3>
                    <p class="text-gray-600 mb-4">Get detailed assistance</p>
                    <a href="mailto:<EMAIL>" class="text-harrier-red font-medium hover:text-harrier-dark">
                        <EMAIL>
                    </a>
                </div>
                
                <div class="text-center">
                    <div class="w-16 h-16 bg-harrier-red bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-comments text-harrier-red text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-heading font-bold text-harrier-dark mb-2">Live Chat</h3>
                    <p class="text-gray-600 mb-4">Instant support online</p>
                    <button class="text-harrier-red font-medium hover:text-harrier-dark" onclick="openChat()">
                        Start Chat
                    </button>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
function openChat() {
    // Placeholder for chat functionality
    alert('Live chat feature coming soon! Please use phone or email for now.');
}

// Auto-format chassis number input
document.getElementById('chassis_number').addEventListener('input', function(e) {
    let value = e.target.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
    if (value.length > 17) {
        value = value.substring(0, 17);
    }
    e.target.value = value;
});
</script>
{% endblock %}
