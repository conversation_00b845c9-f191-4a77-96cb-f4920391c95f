<!-- Spare Parts Grid Partial for HTMX -->
<div id="parts-grid-container">
    <!-- Parts Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {% for part in spare_parts %}
            <div class="product-card-harrier bg-white rounded-xl shadow-lg overflow-hidden group">
                <a href="{% url 'core:spare_part_detail' part.pk %}" class="block">
                    <div class="relative">
                        {% if part.main_image %}
                            <img src="{{ part.main_image.url }}" alt="{{ part.name }}" class="w-full h-40 object-cover group-hover:scale-110 transition-transform duration-500">
                        {% else %}
                            <div class="w-full h-40 bg-gray-200 flex items-center justify-center">
                                <i class="fas fa-cog text-gray-400 text-4xl"></i>
                            </div>
                        {% endif %}
                    
                    <div class="absolute top-3 left-3">
                        <span class="bg-harrier-blue text-white px-2 py-1 rounded-full text-xs font-semibold">{{ part.condition|title }}</span>
                    </div>
                    
                    {% if part.is_in_stock %}
                        <div class="absolute top-3 right-3">
                            <span class="bg-green-500 text-white px-2 py-1 rounded-full text-xs font-semibold">In Stock</span>
                        </div>
                    {% else %}
                        <div class="absolute top-3 right-3">
                            <span class="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-semibold">Out of Stock</span>
                        </div>
                    {% endif %}
                </div>
                
                <div class="p-4">
                    <div class="mb-2">
                        <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">{{ part.category }}</span>
                    </div>
                    
                    <h3 class="text-lg font-semibold text-harrier-dark mb-2 hover:text-harrier-red transition-colors">
                        {{ part.name }}
                    </h3>

                    {% if part.part_number %}
                        <p class="text-sm text-gray-600 mb-2">Part #: {{ part.part_number }}</p>
                    {% endif %}

                    <p class="text-sm text-gray-600 mb-3">{{ part.description|truncatewords:15 }}</p>
                    
                    <!-- Compatible Brands -->
                    {% if part.compatible_brands.all %}
                        <div class="mb-3">
                            <p class="text-xs text-gray-500 mb-1">Compatible with:</p>
                            <div class="flex flex-wrap gap-1">
                                {% for brand in part.compatible_brands.all|slice:":3" %}
                                    <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">{{ brand.name }}</span>
                                {% endfor %}
                                {% if part.compatible_brands.count > 3 %}
                                    <span class="text-xs text-gray-500">+{{ part.compatible_brands.count|add:"-3" }} more</span>
                                {% endif %}
                            </div>
                        </div>
                    {% endif %}
                    
                </a>

                <div class="p-4">
                    <div class="flex justify-between items-center mb-3">
                        <div>
                            <span class="text-xl font-bold text-harrier-red">KSh {{ part.price|floatformat:0 }}</span>
                            {% if part.stock_quantity %}
                                <p class="text-xs text-gray-500">{{ part.stock_quantity }} available</p>
                            {% endif %}
                        </div>
                    </div>

                    <div class="flex space-x-2">
                        <a href="{% url 'core:spare_part_detail' part.pk %}"
                           class="flex-1 btn-harrier-primary px-3 py-2 rounded-lg transition-colors text-sm text-center">
                            View Details
                        </a>
                        {% if user.is_authenticated %}
                            <button onclick="addToCart('{{ part.id }}', 1); event.stopPropagation();"
                                    class="px-3 py-2 border-2 border-harrier-red text-harrier-red rounded-lg hover:bg-harrier-red hover:text-white transition-colors text-sm">
                                <i class="fas fa-cart-plus"></i>
                            </button>
                        {% endif %}
                    </div>
                </div>

                    <div class="mt-3 text-xs text-gray-500">
                        <span>Sold by {{ part.vendor.company_name }}</span>
                    </div>
                </div>
            </div>
        {% empty %}
            <div class="col-span-full text-center py-16">
                <i class="fas fa-cog text-gray-300 text-6xl mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-600 mb-2">No spare parts found</h3>
                <p class="text-gray-500 mb-6">Try adjusting your search criteria or browse all parts.</p>
                <button onclick="clearFilters()" class="btn-harrier-primary px-6 py-3 rounded-lg">Browse All Parts</button>
            </div>
        {% endfor %}
    </div>
    
    <!-- Pagination -->
    {% if is_paginated %}
        <div class="flex justify-center mt-12">
            <nav class="flex items-center space-x-2">
                {% if page_obj.has_previous %}
                    <button hx-get="{% url 'core:spare_parts_search' %}?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" 
                            hx-target="#parts-grid-container" 
                            hx-swap="outerHTML"
                            class="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-100">First</button>
                    <button hx-get="{% url 'core:spare_parts_search' %}?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" 
                            hx-target="#parts-grid-container" 
                            hx-swap="outerHTML"
                            class="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-100">Previous</button>
                {% endif %}
                
                <span class="px-4 py-2 bg-harrier-red text-white rounded-lg">
                    Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                </span>
                
                {% if page_obj.has_next %}
                    <button hx-get="{% url 'core:spare_parts_search' %}?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" 
                            hx-target="#parts-grid-container" 
                            hx-swap="outerHTML"
                            class="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-100">Next</button>
                    <button hx-get="{% url 'core:spare_parts_search' %}?page={{ page_obj.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" 
                            hx-target="#parts-grid-container" 
                            hx-swap="outerHTML"
                            class="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-100">Last</button>
                {% endif %}
            </nav>
        </div>
    {% endif %}
</div>
