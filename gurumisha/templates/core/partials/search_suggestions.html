<!-- Search Suggestions Partial for HTMX -->
{% if suggestions %}
    <div class="absolute top-full left-0 right-0 bg-white border border-gray-200 rounded-lg shadow-lg z-50 mt-1 max-h-64 overflow-y-auto">
        {% for suggestion in suggestions %}
            <div class="px-4 py-3 hover:bg-harrier-gray cursor-pointer border-b border-gray-100 last:border-b-0 suggestion-item"
                 onclick="selectSuggestion('{{ suggestion.name }}')">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="font-medium text-harrier-dark">{{ suggestion.name }}</div>
                        <div class="text-sm text-gray-500">
                            Category: {{ suggestion.category }}
                            {% if suggestion.part_number %}
                                | Part #: {{ suggestion.part_number }}
                            {% endif %}
                        </div>
                    </div>
                    <i class="fas fa-arrow-right text-gray-400"></i>
                </div>
            </div>
        {% endfor %}
        
        <!-- View All Results Option -->
        <div class="px-4 py-3 bg-harrier-gray border-t border-gray-200">
            <button onclick="submitSearch()" class="text-harrier-red font-medium text-sm hover:text-harrier-dark transition-colors">
                <i class="fas fa-search mr-2"></i>View all results for "{{ query }}"
            </button>
        </div>
    </div>
{% endif %}
