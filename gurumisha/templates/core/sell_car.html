{% extends 'base.html' %}
{% load static %}

{% block title %}Sell Your Car - Gurumisha{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="bg-harrier-dark py-16">
    <div class="container mx-auto px-4">
        <div class="text-center">
            <h1 class="text-4xl md:text-5xl font-heading font-bold text-white mb-4">
                SELL YOUR CAR
            </h1>
            <p class="text-xl text-gray-300">
                List your vehicle on Kenya's most trusted automotive marketplace
            </p>
        </div>
    </div>
</div>

<!-- Sell Car Form Section -->
<section class="py-16 bg-harrier-gray">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
            <!-- Progress Steps -->
            <div class="mb-12">
                <div class="flex items-center justify-center space-x-4 md:space-x-8">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-harrier-red text-white rounded-full flex items-center justify-center text-sm font-bold">1</div>
                        <span class="ml-2 text-sm font-medium text-harrier-dark">Car Details</span>
                    </div>
                    <div class="w-8 h-1 bg-gray-300"></div>
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-sm font-bold">2</div>
                        <span class="ml-2 text-sm font-medium text-gray-600">Review</span>
                    </div>
                    <div class="w-8 h-1 bg-gray-300"></div>
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-gray-300 text-gray-600 rounded-full flex items-center justify-center text-sm font-bold">3</div>
                        <span class="ml-2 text-sm font-medium text-gray-600">Publish</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-xl p-8 lg:p-12">
                <div class="mb-8">
                    <h2 class="text-3xl font-heading font-bold text-harrier-dark mb-2">List Your Vehicle</h2>
                    <p class="text-gray-600">Provide detailed information about your car to attract serious buyers</p>
                </div>

                <!-- Display Messages -->
                {% if messages %}
                    {% for message in messages %}
                        <div class="mb-6 p-4 rounded-lg {% if message.tags == 'error' %}bg-red-100 border border-red-400 text-red-700{% elif message.tags == 'success' %}bg-green-100 border border-green-400 text-green-700{% else %}bg-blue-100 border border-blue-400 text-blue-700{% endif %}">
                            {{ message }}
                        </div>
                    {% endfor %}
                {% endif %}

                <form method="post" enctype="multipart/form-data" class="sell-car-form space-y-8">
                    {% csrf_token %}
                    
                    <!-- Basic Information -->
                    <div>
                        <h3 class="text-xl font-heading font-bold text-harrier-dark mb-6">Basic Information</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="{{ form.title.id_for_label }}" class="block text-sm font-semibold text-harrier-dark mb-2">
                                    Listing Title <span class="text-harrier-red">*</span>
                                </label>
                                {{ form.title }}
                                {% if form.title.errors %}
                                    <div class="mt-1 text-sm text-red-600">{{ form.title.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div>
                                <label for="{{ form.price.id_for_label }}" class="block text-sm font-semibold text-harrier-dark mb-2">
                                    Price (KES) <span class="text-harrier-red">*</span>
                                </label>
                                {{ form.price }}
                                {% if form.price.errors %}
                                    <div class="mt-1 text-sm text-red-600">{{ form.price.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Vehicle Details -->
                    <div>
                        <h3 class="text-xl font-heading font-bold text-harrier-dark mb-6">Vehicle Details</h3>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div>
                                <label for="{{ form.brand.id_for_label }}" class="block text-sm font-semibold text-harrier-dark mb-2">
                                    Brand <span class="text-harrier-red">*</span>
                                </label>
                                {{ form.brand }}
                                {% if form.brand.errors %}
                                    <div class="mt-1 text-sm text-red-600">{{ form.brand.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div>
                                <label for="{{ form.model.id_for_label }}" class="block text-sm font-semibold text-harrier-dark mb-2">
                                    Model <span class="text-harrier-red">*</span>
                                </label>
                                {{ form.model }}
                                {% if form.model.errors %}
                                    <div class="mt-1 text-sm text-red-600">{{ form.model.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div>
                                <label for="{{ form.year.id_for_label }}" class="block text-sm font-semibold text-harrier-dark mb-2">
                                    Year <span class="text-harrier-red">*</span>
                                </label>
                                {{ form.year }}
                                {% if form.year.errors %}
                                    <div class="mt-1 text-sm text-red-600">{{ form.year.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                            <div>
                                <label for="{{ form.condition.id_for_label }}" class="block text-sm font-semibold text-harrier-dark mb-2">
                                    Condition <span class="text-harrier-red">*</span>
                                </label>
                                {{ form.condition }}
                                {% if form.condition.errors %}
                                    <div class="mt-1 text-sm text-red-600">{{ form.condition.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div>
                                <label for="{{ form.color.id_for_label }}" class="block text-sm font-semibold text-harrier-dark mb-2">
                                    Color <span class="text-harrier-red">*</span>
                                </label>
                                {{ form.color }}
                                {% if form.color.errors %}
                                    <div class="mt-1 text-sm text-red-600">{{ form.color.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Technical Specifications -->
                    <div>
                        <h3 class="text-xl font-heading font-bold text-harrier-dark mb-6">Technical Specifications</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            <div>
                                <label for="{{ form.engine_size.id_for_label }}" class="block text-sm font-semibold text-harrier-dark mb-2">
                                    Engine Size
                                </label>
                                {{ form.engine_size }}
                                {% if form.engine_size.errors %}
                                    <div class="mt-1 text-sm text-red-600">{{ form.engine_size.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div>
                                <label for="{{ form.fuel_type.id_for_label }}" class="block text-sm font-semibold text-harrier-dark mb-2">
                                    Fuel Type <span class="text-harrier-red">*</span>
                                </label>
                                {{ form.fuel_type }}
                                {% if form.fuel_type.errors %}
                                    <div class="mt-1 text-sm text-red-600">{{ form.fuel_type.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div>
                                <label for="{{ form.transmission.id_for_label }}" class="block text-sm font-semibold text-harrier-dark mb-2">
                                    Transmission <span class="text-harrier-red">*</span>
                                </label>
                                {{ form.transmission }}
                                {% if form.transmission.errors %}
                                    <div class="mt-1 text-sm text-red-600">{{ form.transmission.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div>
                                <label for="{{ form.mileage.id_for_label }}" class="block text-sm font-semibold text-harrier-dark mb-2">
                                    Mileage (KM) <span class="text-harrier-red">*</span>
                                </label>
                                {{ form.mileage }}
                                {% if form.mileage.errors %}
                                    <div class="mt-1 text-sm text-red-600">{{ form.mileage.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Description and Features -->
                    <div>
                        <h3 class="text-xl font-heading font-bold text-harrier-dark mb-6">Description & Features</h3>
                        <div class="space-y-6">
                            <div>
                                <label for="{{ form.description.id_for_label }}" class="block text-sm font-semibold text-harrier-dark mb-2">
                                    Description <span class="text-harrier-red">*</span>
                                </label>
                                {{ form.description }}
                                {% if form.description.errors %}
                                    <div class="mt-1 text-sm text-red-600">{{ form.description.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div>
                                <label for="{{ form.features.id_for_label }}" class="block text-sm font-semibold text-harrier-dark mb-2">
                                    Features
                                </label>
                                {{ form.features }}
                                <p class="mt-1 text-sm text-gray-500">
                                    List key features separated by commas (e.g., Air Conditioning, Power Steering, ABS, Airbags)
                                </p>
                                {% if form.features.errors %}
                                    <div class="mt-1 text-sm text-red-600">{{ form.features.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Main Image -->
                    <div>
                        <h3 class="text-xl font-heading font-bold text-harrier-dark mb-6">Main Photo</h3>
                        <div>
                            <label for="{{ form.main_image.id_for_label }}" class="block text-sm font-semibold text-harrier-dark mb-2">
                                Upload Main Photo <span class="text-harrier-red">*</span>
                            </label>
                            {{ form.main_image }}
                            <p class="mt-1 text-sm text-gray-500">
                                Upload a high-quality photo of your car. This will be the main image buyers see first.
                            </p>
                            {% if form.main_image.errors %}
                                <div class="mt-1 text-sm text-red-600">{{ form.main_image.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Listing Options -->
                    <div>
                        <h3 class="text-xl font-heading font-bold text-harrier-dark mb-6">Listing Options</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="{{ form.listing_type.id_for_label }}" class="block text-sm font-semibold text-harrier-dark mb-2">
                                    Listing Type <span class="text-harrier-red">*</span>
                                </label>
                                {{ form.listing_type }}
                                <p class="mt-1 text-sm text-gray-500">
                                    Choose how you want to list your car
                                </p>
                                {% if form.listing_type.errors %}
                                    <div class="mt-1 text-sm text-red-600">{{ form.listing_type.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div class="flex items-center">
                                <div class="flex items-center h-5">
                                    {{ form.negotiable }}
                                </div>
                                <div class="ml-3">
                                    <label for="{{ form.negotiable.id_for_label }}" class="text-sm font-semibold text-harrier-dark">
                                        Price is negotiable
                                    </label>
                                    <p class="text-sm text-gray-500">
                                        Check this if you're open to price negotiations
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="pt-6 border-t border-gray-200">
                        <div class="flex flex-col sm:flex-row gap-4">
                            <button type="submit" class="flex-1 btn-harrier-primary py-4 text-lg font-semibold">
                                <i class="fas fa-check mr-2"></i>SUBMIT FOR REVIEW
                            </button>
                            <a href="{% url 'core:dashboard' %}" class="flex-1 btn-harrier-secondary py-4 text-lg font-semibold text-center">
                                <i class="fas fa-times mr-2"></i>CANCEL
                            </a>
                        </div>
                        <p class="mt-4 text-sm text-gray-600 text-center">
                            Your listing will be reviewed by our team before being published. This usually takes 24-48 hours.
                        </p>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- Why Sell With Us Section -->
<section class="py-16 bg-white">
    <div class="container mx-auto px-4">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-heading font-bold text-harrier-dark mb-4">Why Sell With Gurumisha Motors?</h2>
            <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                Join thousands of satisfied sellers who have successfully sold their cars through our platform
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="text-center">
                <div class="w-16 h-16 bg-harrier-red bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-users text-harrier-red text-2xl"></i>
                </div>
                <h3 class="text-xl font-heading font-bold text-harrier-dark mb-2">Large Audience</h3>
                <p class="text-gray-600">Reach thousands of potential buyers actively looking for cars</p>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 bg-harrier-red bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-shield-alt text-harrier-red text-2xl"></i>
                </div>
                <h3 class="text-xl font-heading font-bold text-harrier-dark mb-2">Secure Platform</h3>
                <p class="text-gray-600">All buyers are verified and transactions are secure</p>
            </div>
            
            <div class="text-center">
                <div class="w-16 h-16 bg-harrier-red bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-headset text-harrier-red text-2xl"></i>
                </div>
                <h3 class="text-xl font-heading font-bold text-harrier-dark mb-2">Expert Support</h3>
                <p class="text-gray-600">Our team helps you throughout the selling process</p>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_css %}
{{ block.super }}
<style>
/* Enhanced Input Styling for Sell Car Form */
.sell-car-form input[type="text"],
.sell-car-form input[type="email"],
.sell-car-form input[type="number"],
.sell-car-form select,
.sell-car-form textarea {
    font-family: 'Inter', 'Raleway', sans-serif !important;
    font-size: 15px !important;
    font-weight: 500 !important;
    line-height: 1.5 !important;
    padding: 16px 20px !important;
    border-radius: 16px !important;
    border: 2px solid rgba(226, 232, 240, 0.8) !important;

    /* Modern glassmorphism background */
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%) !important;
    backdrop-filter: blur(12px);

    /* Subtle shadow */
    box-shadow:
        0 1px 3px rgba(0, 0, 0, 0.05),
        0 0 0 1px rgba(255, 255, 255, 0.05) inset !important;

    /* Text styling */
    color: #1e293b !important;
    letter-spacing: 0.01em;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.sell-car-form input:hover,
.sell-car-form select:hover,
.sell-car-form textarea:hover {
    border-color: rgba(220, 38, 38, 0.3) !important;
    background: linear-gradient(135deg, rgba(255, 255, 255, 1) 0%, rgba(254, 242, 242, 0.95) 100%) !important;
    box-shadow:
        0 2px 8px rgba(220, 38, 38, 0.08),
        0 0 0 1px rgba(255, 255, 255, 0.1) inset !important;
    transform: translateY(-1px);
}

.sell-car-form input:focus,
.sell-car-form select:focus,
.sell-car-form textarea:focus {
    outline: none !important;
    border-color: #dc2626 !important;
    background: linear-gradient(135deg, rgba(255, 255, 255, 1) 0%, rgba(254, 242, 242, 0.98) 100%) !important;
    box-shadow:
        0 4px 20px rgba(220, 38, 38, 0.15),
        0 0 0 4px rgba(220, 38, 38, 0.1),
        0 0 0 1px rgba(255, 255, 255, 0.2) inset !important;
    transform: translateY(-2px);
}

.sell-car-form input::placeholder,
.sell-car-form textarea::placeholder {
    color: #94a3b8 !important;
    font-weight: 400 !important;
    opacity: 0.8;
}

/* Select specific styling */
.sell-car-form select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23dc2626' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m6 8 4 4 4-4'/%3e%3c/svg%3e") !important;
    background-position: right 16px center !important;
    background-repeat: no-repeat !important;
    background-size: 16px 12px !important;
    padding-right: 48px !important;
    cursor: pointer;
}

.sell-car-form select:focus {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23b91c1c' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m6 8 4 4 4-4'/%3e%3c/svg%3e") !important;
}

/* Textarea specific styling */
.sell-car-form textarea {
    resize: vertical !important;
    min-height: 120px !important;
    line-height: 1.6 !important;
}

/* File input styling */
.sell-car-form input[type="file"] {
    padding: 16px !important;
    border: 2px dashed rgba(220, 38, 38, 0.3) !important;
    background: linear-gradient(135deg, rgba(254, 242, 242, 0.5) 0%, rgba(255, 255, 255, 0.8) 100%) !important;
    border-radius: 16px !important;
    cursor: pointer;
    transition: all 0.3s ease !important;
}

.sell-car-form input[type="file"]:hover {
    border-color: rgba(220, 38, 38, 0.5) !important;
    background: linear-gradient(135deg, rgba(254, 242, 242, 0.8) 0%, rgba(255, 255, 255, 0.9) 100%) !important;
    transform: translateY(-1px);
}

.sell-car-form input[type="file"]:focus {
    border-color: #dc2626 !important;
    box-shadow: 0 4px 20px rgba(220, 38, 38, 0.15) !important;
    outline: none !important;
}

/* Checkbox styling */
.sell-car-form input[type="checkbox"] {
    width: 20px !important;
    height: 20px !important;
    border-radius: 6px !important;
    border: 2px solid #e2e8f0 !important;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
    cursor: pointer;
    transition: all 0.2s ease !important;
    margin: 0 !important;
    padding: 0 !important;
    position: relative;
}

.sell-car-form input[type="checkbox"]:checked {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%) !important;
    border-color: #dc2626 !important;
    transform: scale(1.05);
}

.sell-car-form input[type="checkbox"]:checked::before {
    content: "✓";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.sell-car-form input[type="checkbox"]:focus {
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.2) !important;
    outline: none !important;
}

/* Enhanced labels */
.sell-car-form label {
    font-family: 'Montserrat', sans-serif !important;
    font-weight: 600 !important;
    color: #1f2937 !important;
    letter-spacing: 0.025em;
    margin-bottom: 8px !important;
}

/* Error styling */
.sell-car-form .text-red-600 {
    font-size: 13px !important;
    font-weight: 500 !important;
    margin-top: 6px !important;
    display: flex;
    align-items: center;
}

.sell-car-form .text-red-600::before {
    content: "⚠";
    margin-right: 6px;
    font-size: 14px;
}

/* Form sections */
.sell-car-form h3 {
    position: relative;
    padding-left: 20px;
}

.sell-car-form h3::before {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 24px;
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    border-radius: 2px;
}

/* Mobile optimizations */
@media (max-width: 768px) {
    .sell-car-form input,
    .sell-car-form select,
    .sell-car-form textarea {
        font-size: 16px !important; /* Prevents zoom on iOS */
        padding: 16px 14px !important;
    }
}
</style>
{% endblock %}
