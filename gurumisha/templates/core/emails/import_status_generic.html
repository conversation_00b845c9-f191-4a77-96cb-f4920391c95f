<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Import Status Update - {{ order.order_number }}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #dc2626;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #dc2626;
            margin-bottom: 10px;
        }
        .status-badge {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            text-transform: uppercase;
            font-size: 12px;
            margin: 10px 0;
        }
        .status-import-request { background-color: #fef3c7; color: #92400e; }
        .status-shipped { background-color: #dbeafe; color: #1e40af; }
        .status-delivered { background-color: #d1fae5; color: #065f46; }
        .status-cancelled { background-color: #fee2e2; color: #991b1b; }
        .progress-bar {
            width: 100%;
            height: 8px;
            background-color: #e5e7eb;
            border-radius: 4px;
            margin: 15px 0;
            overflow: hidden;
        }
        .progress-fill {
            height: 100%;
            background-color: #dc2626;
            transition: width 0.3s ease;
        }
        .order-details {
            background-color: #f9fafb;
            padding: 20px;
            border-radius: 6px;
            margin: 20px 0;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e5e7eb;
        }
        .detail-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        .detail-label {
            font-weight: 600;
            color: #374151;
        }
        .detail-value {
            color: #6b7280;
        }
        .cta-button {
            display: inline-block;
            background-color: #dc2626;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: bold;
            margin: 20px 0;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            color: #6b7280;
            font-size: 14px;
        }
        .contact-info {
            background-color: #f3f4f6;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">GURUMISHA MOTORS</div>
            <h1>Import Status Update</h1>
            <span class="status-badge status-{{ new_status }}">{{ status_display }}</span>
        </div>

        <p>Dear {{ customer.first_name|default:customer.username }},</p>

        <p>We have an update on your import order <strong>{{ order.order_number }}</strong>.</p>

        {% if change_reason %}
        <div style="background-color: #eff6ff; border-left: 4px solid #3b82f6; padding: 15px; margin: 20px 0;">
            <strong>Update Details:</strong><br>
            {{ change_reason }}
        </div>
        {% endif %}

        <div class="order-details">
            <h3 style="margin-top: 0; color: #374151;">Order Details</h3>
            <div class="detail-row">
                <span class="detail-label">Order Number:</span>
                <span class="detail-value">{{ order.order_number }}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Vehicle:</span>
                <span class="detail-value">{{ order.year }} {{ order.brand }} {{ order.model }}</span>
            </div>
            {% if order.chassis_number %}
            <div class="detail-row">
                <span class="detail-label">Chassis Number:</span>
                <span class="detail-value">{{ order.chassis_number }}</span>
            </div>
            {% endif %}
            <div class="detail-row">
                <span class="detail-label">Current Status:</span>
                <span class="detail-value">{{ status_display }}</span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Progress:</span>
                <span class="detail-value">{{ progress_percentage }}% Complete</span>
            </div>
        </div>

        <div class="progress-bar">
            <div class="progress-fill" style="width: {{ progress_percentage }}%;"></div>
        </div>

        {% if estimated_date %}
        <p><strong>Estimated Next Update:</strong> {{ estimated_date|date:"F d, Y" }}</p>
        {% endif %}

        <div style="text-align: center;">
            <a href="{{ tracking_url }}" class="cta-button">View Full Tracking Details</a>
        </div>

        <div class="contact-info">
            <h4 style="margin-top: 0;">Need Assistance?</h4>
            <p style="margin-bottom: 5px;"><strong>Phone:</strong> +254 700 000 000</p>
            <p style="margin-bottom: 5px;"><strong>Email:</strong> <EMAIL></p>
            <p style="margin-bottom: 0;"><strong>Hours:</strong> Monday - Friday, 8:00 AM - 6:00 PM EAT</p>
        </div>

        <p>Thank you for choosing Gurumisha Motors for your vehicle import needs.</p>

        <div class="footer">
            <p>This is an automated notification. Please do not reply to this email.</p>
            <p>&copy; {{ "now"|date:"Y" }} Gurumisha Motors. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
