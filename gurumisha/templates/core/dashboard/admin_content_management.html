{% extends 'base_admin_dashboard.html' %}
{% load static %}

{% block page_title %}Content Management{% endblock %}
{% block page_description %}Manage website content, blog posts, and testimonials{% endblock %}

{% block dashboard_content %}
<div class="space-y-8">
    <!-- Content Management Header -->
    <div class="flex justify-between items-center animate-fade-in-up" style="animation-delay: 0.1s;">
        <div>
            <h2 class="text-3xl font-bold text-harrier-dark font-montserrat">Content Management</h2>
            <p class="text-gray-600 mt-1 font-raleway">Manage website content, blog posts, and testimonials</p>
        </div>
        
        <div class="flex space-x-3">
            <button class="btn-admin-secondary text-sm">
                <i class="fas fa-eye mr-2"></i>Preview Site
            </button>
            <button class="btn-admin-primary text-sm">
                <i class="fas fa-plus mr-2"></i>Create Content
            </button>
        </div>
    </div>

    <!-- Content Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 animate-fade-in-up" style="animation-delay: 0.2s;">
        <div class="admin-stat-card text-center">
            <div class="admin-stat-value text-blue-600">{{ total_posts|default:0 }}</div>
            <div class="admin-stat-label">Total Posts</div>
        </div>
        <div class="admin-stat-card text-center">
            <div class="admin-stat-value text-green-600">{{ published_posts|default:0 }}</div>
            <div class="admin-stat-label">Published</div>
        </div>
        <div class="admin-stat-card text-center">
            <div class="admin-stat-value text-orange-600">{{ draft_posts|default:0 }}</div>
            <div class="admin-stat-label">Drafts</div>
        </div>
        <div class="admin-stat-card text-center">
            <div class="admin-stat-value text-purple-600">{{ total_testimonials|default:0 }}</div>
            <div class="admin-stat-label">Testimonials</div>
        </div>
    </div>

    <!-- Content Tabs -->
    <div class="animate-fade-in-up" style="animation-delay: 0.3s;">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8">
                <button class="content-tab active" data-tab="blog">
                    <i class="fas fa-newspaper mr-2"></i>Blog Posts
                </button>
                <button class="content-tab" data-tab="testimonials">
                    <i class="fas fa-quote-left mr-2"></i>Testimonials
                </button>
                <button class="content-tab" data-tab="pages">
                    <i class="fas fa-file-alt mr-2"></i>Static Pages
                </button>
                <button class="content-tab" data-tab="media">
                    <i class="fas fa-images mr-2"></i>Media Library
                </button>
            </nav>
        </div>
    </div>

    <!-- Blog Posts Tab -->
    <div id="blog-tab" class="tab-content active animate-fade-in-up" style="animation-delay: 0.4s;">
        <div class="admin-card">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-newspaper text-white"></i>
                        </div>
                        <h3 class="text-lg font-bold text-harrier-dark font-montserrat">Blog Posts</h3>
                    </div>
                    <div class="flex space-x-2">
                        <button class="btn-admin-secondary text-sm">
                            <i class="fas fa-filter mr-1"></i>Filter
                        </button>
                        <button class="btn-admin-primary text-sm">
                            <i class="fas fa-plus mr-1"></i>New Post
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="p-6">
                {% if blog_posts %}
                    <div class="space-y-4">
                        {% for post in blog_posts %}
                        <div class="content-item border border-gray-200 rounded-lg p-6 hover:border-blue-300 hover:shadow-md transition-all duration-300">
                            <div class="flex items-start justify-between">
                                <div class="flex items-start space-x-4 flex-1">
                                    {% if post.featured_image %}
                                        <img src="{{ post.featured_image.url }}" alt="{{ post.title }}" class="w-20 h-20 object-cover rounded-lg border-2 border-gray-200">
                                    {% else %}
                                        <div class="w-20 h-20 bg-gradient-to-br from-blue-200 to-blue-300 rounded-lg flex items-center justify-center">
                                            <i class="fas fa-newspaper text-blue-600 text-xl"></i>
                                        </div>
                                    {% endif %}
                                    
                                    <div class="flex-1 min-w-0">
                                        <div class="flex items-center space-x-3 mb-2">
                                            <h4 class="text-lg font-semibold text-harrier-dark font-raleway">{{ post.title }}</h4>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium font-montserrat
                                                {% if post.is_published %}bg-green-100 text-green-800
                                                {% else %}bg-orange-100 text-orange-800{% endif %}">
                                                {% if post.is_published %}Published{% else %}Draft{% endif %}
                                            </span>
                                        </div>
                                        
                                        <p class="text-sm text-gray-600 mb-2">By {{ post.author.get_full_name|default:post.author.username }}</p>
                                        <p class="text-gray-800 font-raleway line-clamp-2">{{ post.excerpt|default:post.content|truncatewords:20 }}</p>
                                        
                                        <div class="flex items-center space-x-4 mt-3 text-sm text-gray-500">
                                            <span><i class="fas fa-calendar mr-1"></i>{{ post.created_at|date:"M d, Y" }}</span>
                                            <span><i class="fas fa-eye mr-1"></i>{{ post.views|default:0 }} views</span>
                                            <span><i class="fas fa-comments mr-1"></i>{{ post.comments_count|default:0 }} comments</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="flex flex-col space-y-2 ml-4">
                                    <button class="bg-blue-100 text-blue-700 hover:bg-blue-200 px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                                        <i class="fas fa-edit mr-1"></i>Edit
                                    </button>
                                    <button class="bg-green-100 text-green-700 hover:bg-green-200 px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                                        <i class="fas fa-eye mr-1"></i>View
                                    </button>
                                    <button class="bg-red-100 text-red-700 hover:bg-red-200 px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                                        <i class="fas fa-trash mr-1"></i>Delete
                                    </button>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-12">
                        <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-newspaper text-gray-400 text-2xl"></i>
                        </div>
                        <h4 class="text-lg font-semibold text-gray-900 mb-2 font-montserrat">No blog posts yet</h4>
                        <p class="text-gray-600 font-raleway">Create your first blog post to get started.</p>
                        <button class="mt-4 btn-admin-primary">
                            <i class="fas fa-plus mr-2"></i>Create First Post
                        </button>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Testimonials Tab -->
    <div id="testimonials-tab" class="tab-content hidden">
        <div class="admin-card">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-quote-left text-white"></i>
                        </div>
                        <h3 class="text-lg font-bold text-harrier-dark font-montserrat">Customer Testimonials</h3>
                    </div>
                    <button class="btn-admin-primary text-sm">
                        <i class="fas fa-plus mr-1"></i>Add Testimonial
                    </button>
                </div>
            </div>
            
            <div class="p-6">
                {% if testimonials %}
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {% for testimonial in testimonials %}
                        <div class="testimonial-card bg-gray-50 rounded-lg p-6 hover:bg-gray-100 transition-colors duration-300">
                            <div class="flex items-start space-x-4">
                                {% if testimonial.customer_image %}
                                    <img src="{{ testimonial.customer_image.url }}" alt="{{ testimonial.customer_name }}" class="w-12 h-12 object-cover rounded-full border-2 border-gray-200">
                                {% else %}
                                    <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold">
                                        {{ testimonial.customer_name|first|upper }}
                                    </div>
                                {% endif %}
                                
                                <div class="flex-1">
                                    <div class="flex items-center justify-between mb-2">
                                        <h5 class="font-semibold text-harrier-dark font-raleway">{{ testimonial.customer_name }}</h5>
                                        <div class="flex space-x-1">
                                            <button class="text-blue-600 hover:text-blue-800 text-sm">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="text-red-600 hover:text-red-800 text-sm">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <p class="text-sm text-gray-600 mb-2">{{ testimonial.customer_location }}</p>
                                    <p class="text-gray-800 text-sm italic">"{{ testimonial.message|truncatewords:15 }}"</p>
                                    <div class="flex items-center mt-2">
                                        {% for i in "12345" %}
                                            {% if forloop.counter <= testimonial.rating %}
                                                <i class="fas fa-star text-yellow-400"></i>
                                            {% else %}
                                                <i class="far fa-star text-gray-300"></i>
                                            {% endif %}
                                        {% endfor %}
                                        <span class="ml-2 text-sm text-gray-600">{{ testimonial.rating }}/5</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-12">
                        <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-quote-left text-gray-400 text-2xl"></i>
                        </div>
                        <h4 class="text-lg font-semibold text-gray-900 mb-2 font-montserrat">No testimonials yet</h4>
                        <p class="text-gray-600 font-raleway">Add customer testimonials to build trust.</p>
                        <button class="mt-4 btn-admin-primary">
                            <i class="fas fa-plus mr-2"></i>Add First Testimonial
                        </button>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Static Pages Tab -->
    <div id="pages-tab" class="tab-content hidden">
        <div class="admin-card">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-file-alt text-white"></i>
                        </div>
                        <h3 class="text-lg font-bold text-harrier-dark font-montserrat">Static Pages</h3>
                    </div>
                    <button class="btn-admin-primary text-sm">
                        <i class="fas fa-plus mr-1"></i>New Page
                    </button>
                </div>
            </div>
            
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="page-card bg-white border border-gray-200 rounded-lg p-6 hover:border-green-300 hover:shadow-md transition-all duration-300">
                        <div class="flex items-center justify-between mb-4">
                            <h4 class="font-semibold text-harrier-dark font-raleway">About Us</h4>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Published
                            </span>
                        </div>
                        <p class="text-sm text-gray-600 mb-4">Learn about our company history and mission.</p>
                        <div class="flex space-x-2">
                            <button class="flex-1 bg-green-100 text-green-700 hover:bg-green-200 px-3 py-2 rounded-lg text-sm font-medium transition-colors">
                                <i class="fas fa-edit mr-1"></i>Edit
                            </button>
                            <button class="bg-gray-100 text-gray-700 hover:bg-gray-200 px-3 py-2 rounded-lg text-sm font-medium transition-colors">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="page-card bg-white border border-gray-200 rounded-lg p-6 hover:border-green-300 hover:shadow-md transition-all duration-300">
                        <div class="flex items-center justify-between mb-4">
                            <h4 class="font-semibold text-harrier-dark font-raleway">Contact Us</h4>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Published
                            </span>
                        </div>
                        <p class="text-sm text-gray-600 mb-4">Contact information and inquiry form.</p>
                        <div class="flex space-x-2">
                            <button class="flex-1 bg-green-100 text-green-700 hover:bg-green-200 px-3 py-2 rounded-lg text-sm font-medium transition-colors">
                                <i class="fas fa-edit mr-1"></i>Edit
                            </button>
                            <button class="bg-gray-100 text-gray-700 hover:bg-gray-200 px-3 py-2 rounded-lg text-sm font-medium transition-colors">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="page-card bg-white border border-gray-200 rounded-lg p-6 hover:border-green-300 hover:shadow-md transition-all duration-300">
                        <div class="flex items-center justify-between mb-4">
                            <h4 class="font-semibold text-harrier-dark font-raleway">Privacy Policy</h4>
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                Draft
                            </span>
                        </div>
                        <p class="text-sm text-gray-600 mb-4">Privacy policy and data protection information.</p>
                        <div class="flex space-x-2">
                            <button class="flex-1 bg-green-100 text-green-700 hover:bg-green-200 px-3 py-2 rounded-lg text-sm font-medium transition-colors">
                                <i class="fas fa-edit mr-1"></i>Edit
                            </button>
                            <button class="bg-gray-100 text-gray-700 hover:bg-gray-200 px-3 py-2 rounded-lg text-sm font-medium transition-colors">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Media Library Tab -->
    <div id="media-tab" class="tab-content hidden">
        <div class="admin-card">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-images text-white"></i>
                        </div>
                        <h3 class="text-lg font-bold text-harrier-dark font-montserrat">Media Library</h3>
                    </div>
                    <button class="btn-admin-primary text-sm">
                        <i class="fas fa-upload mr-1"></i>Upload Media
                    </button>
                </div>
            </div>
            
            <div class="p-6">
                <div class="text-center py-12">
                    <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-images text-gray-400 text-2xl"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2 font-montserrat">Media Library</h4>
                    <p class="text-gray-600 font-raleway">Upload and manage your media files here.</p>
                    <button class="mt-4 btn-admin-primary">
                        <i class="fas fa-upload mr-2"></i>Upload First File
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
{{ block.super }}
<style>
    /* Content Management specific styles */
    .content-tab {
        @apply py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300 transition-colors duration-200;
        font-family: 'Raleway', sans-serif;
    }

    .content-tab.active {
        @apply border-harrier-red text-harrier-red;
    }

    .tab-content {
        @apply transition-all duration-300;
    }

    .tab-content.hidden {
        @apply opacity-0 pointer-events-none;
    }

    .tab-content.active {
        @apply opacity-100;
    }

    /* Content item animations */
    .content-item, .testimonial-card, .page-card {
        transform: translateY(0);
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .content-item:hover, .testimonial-card:hover, .page-card:hover {
        transform: translateY(-2px);
    }

    /* Line clamp for content preview */
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    /* Mobile responsiveness */
    @media (max-width: 768px) {
        .content-item .flex {
            flex-direction: column;
            space-y: 1rem;
        }
        
        .content-item .ml-4 {
            margin-left: 0;
            margin-top: 1rem;
        }
    }
</style>

<script>
// Tab switching functionality
document.addEventListener('DOMContentLoaded', function() {
    const tabs = document.querySelectorAll('.content-tab');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const targetTab = this.dataset.tab;
            
            // Remove active class from all tabs and contents
            tabs.forEach(t => t.classList.remove('active'));
            tabContents.forEach(tc => {
                tc.classList.remove('active');
                tc.classList.add('hidden');
            });
            
            // Add active class to clicked tab and corresponding content
            this.classList.add('active');
            const targetContent = document.getElementById(targetTab + '-tab');
            if (targetContent) {
                targetContent.classList.remove('hidden');
                targetContent.classList.add('active');
            }
        });
    });
});
</script>
{% endblock %}
