{% extends 'base_dashboard.html' %}
{% load static %}

{% block dashboard_title %}My Addresses{% endblock %}
{% block page_title %}My Addresses{% endblock %}
{% block page_description %}Manage your delivery and billing addresses{% endblock %}

{% block breadcrumb_items %}
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">Addresses</span>
        </div>
    </li>
{% endblock %}

{% block dashboard_content %}
    <!-- Add New Address Button -->
    <div class="mb-6 flex justify-end">
        <button type="button" class="btn-harrier-primary" onclick="openAddressModal()">
            <i class="fas fa-plus mr-2"></i>Add New Address
        </button>
    </div>

    <!-- Addresses Grid -->
    {% if addresses %}
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {% for address in addresses %}
                <div class="dashboard-card hover:shadow-lg transition-shadow">
                    <div class="p-6">
                        <div class="flex items-start justify-between mb-4">
                            <div>
                                <h3 class="text-lg font-semibold text-harrier-dark">{{ address.label }}</h3>
                                {% if address.is_default %}
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-harrier-red text-white mt-1">
                                        <i class="fas fa-star mr-1"></i>Default
                                    </span>
                                {% endif %}
                            </div>
                            <div class="flex items-center space-x-2">
                                <button type="button" class="text-harrier-red hover:text-harrier-dark" onclick="editAddress({{ address.id }})">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="text-red-600 hover:text-red-800" onclick="deleteAddress({{ address.id }})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="space-y-2 text-sm text-gray-600">
                            <p class="font-medium text-harrier-dark">{{ address.full_name }}</p>
                            <p>{{ address.street_address }}</p>
                            {% if address.apartment %}
                                <p>{{ address.apartment }}</p>
                            {% endif %}
                            <p>{{ address.city }}, {{ address.state }}</p>
                            <p>{{ address.postal_code }}</p>
                            <p>{{ address.country }}</p>
                            {% if address.phone %}
                                <p class="flex items-center mt-3">
                                    <i class="fas fa-phone mr-2"></i>{{ address.phone }}
                                </p>
                            {% endif %}
                        </div>
                        
                        <div class="mt-4 pt-4 border-t border-gray-200">
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-600">
                                    Type: {{ address.get_address_type_display }}
                                </span>
                                {% if not address.is_default %}
                                    <button type="button" class="text-harrier-red hover:text-harrier-dark text-sm font-medium" onclick="setDefaultAddress({{ address.id }})">
                                        Set as Default
                                    </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <!-- Empty State -->
        <div class="dashboard-card">
            <div class="p-12 text-center">
                <div class="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-map-marker-alt text-4xl text-gray-400"></i>
                </div>
                <h3 class="text-xl font-semibold text-harrier-dark mb-2">No Addresses Added</h3>
                <p class="text-gray-600 mb-6">Add your delivery and billing addresses to make checkout faster.</p>
                <button type="button" class="btn-harrier-primary" onclick="openAddressModal()">
                    <i class="fas fa-plus mr-2"></i>Add Your First Address
                </button>
            </div>
        </div>
    {% endif %}
{% endblock %}

<!-- Address Modal -->
{% block extra_content %}
<div id="addressModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" onclick="closeAddressModal()"></div>
        
        <div class="inline-block w-full max-w-md p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-semibold text-harrier-dark" id="modalTitle">Add New Address</h3>
                <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeAddressModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form id="addressForm" method="post">
                {% csrf_token %}
                <input type="hidden" id="addressId" name="address_id">
                
                <div class="space-y-4">
                    <div>
                        <label for="label" class="block text-sm font-medium text-gray-700 mb-1">Address Label</label>
                        <input type="text" id="label" name="label" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-harrier-red focus:border-harrier-red" placeholder="e.g., Home, Office">
                    </div>
                    
                    <div>
                        <label for="full_name" class="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                        <input type="text" id="full_name" name="full_name" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-harrier-red focus:border-harrier-red">
                    </div>
                    
                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                        <input type="tel" id="phone" name="phone" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-harrier-red focus:border-harrier-red">
                    </div>
                    
                    <div>
                        <label for="street_address" class="block text-sm font-medium text-gray-700 mb-1">Street Address</label>
                        <input type="text" id="street_address" name="street_address" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-harrier-red focus:border-harrier-red">
                    </div>
                    
                    <div>
                        <label for="apartment" class="block text-sm font-medium text-gray-700 mb-1">Apartment, Suite, etc. (Optional)</label>
                        <input type="text" id="apartment" name="apartment" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-harrier-red focus:border-harrier-red">
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label for="city" class="block text-sm font-medium text-gray-700 mb-1">City</label>
                            <input type="text" id="city" name="city" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-harrier-red focus:border-harrier-red">
                        </div>
                        <div>
                            <label for="state" class="block text-sm font-medium text-gray-700 mb-1">State/County</label>
                            <input type="text" id="state" name="state" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-harrier-red focus:border-harrier-red">
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label for="postal_code" class="block text-sm font-medium text-gray-700 mb-1">Postal Code</label>
                            <input type="text" id="postal_code" name="postal_code" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-harrier-red focus:border-harrier-red">
                        </div>
                        <div>
                            <label for="country" class="block text-sm font-medium text-gray-700 mb-1">Country</label>
                            <select id="country" name="country" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-harrier-red focus:border-harrier-red">
                                <option value="Kenya">Kenya</option>
                                <option value="Uganda">Uganda</option>
                                <option value="Tanzania">Tanzania</option>
                                <option value="Rwanda">Rwanda</option>
                            </select>
                        </div>
                    </div>
                    
                    <div>
                        <label for="address_type" class="block text-sm font-medium text-gray-700 mb-1">Address Type</label>
                        <select id="address_type" name="address_type" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-harrier-red focus:border-harrier-red">
                            <option value="shipping">Shipping Address</option>
                            <option value="billing">Billing Address</option>
                            <option value="both">Both Shipping & Billing</option>
                        </select>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="is_default" name="is_default" class="h-4 w-4 text-harrier-red focus:ring-harrier-red border-gray-300 rounded">
                        <label for="is_default" class="ml-2 block text-sm text-gray-700">Set as default address</label>
                    </div>
                </div>
                
                <div class="flex items-center justify-end space-x-3 mt-6 pt-6 border-t border-gray-200">
                    <button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50" onclick="closeAddressModal()">
                        Cancel
                    </button>
                    <button type="submit" class="btn-harrier-primary">
                        <span id="submitText">Save Address</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function openAddressModal(addressId = null) {
    const modal = document.getElementById('addressModal');
    const form = document.getElementById('addressForm');
    const title = document.getElementById('modalTitle');
    const submitText = document.getElementById('submitText');
    
    if (addressId) {
        // Edit mode
        title.textContent = 'Edit Address';
        submitText.textContent = 'Update Address';
        document.getElementById('addressId').value = addressId;
        
        // Load address data (this would typically be an AJAX call)
        // For now, we'll just show the modal
    } else {
        // Add mode
        title.textContent = 'Add New Address';
        submitText.textContent = 'Save Address';
        form.reset();
        document.getElementById('addressId').value = '';
    }
    
    modal.classList.remove('hidden');
}

function closeAddressModal() {
    const modal = document.getElementById('addressModal');
    modal.classList.add('hidden');
}

function editAddress(addressId) {
    openAddressModal(addressId);
}

function deleteAddress(addressId) {
    if (confirm('Are you sure you want to delete this address?')) {
        // This would typically make an AJAX request to delete the address
        console.log('Deleting address:', addressId);
    }
}

function setDefaultAddress(addressId) {
    // This would typically make an AJAX request to set the default address
    console.log('Setting default address:', addressId);
}

// Form submission
document.getElementById('addressForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // This would typically submit the form via AJAX
    console.log('Submitting address form');
    
    // For demo purposes, just close the modal
    closeAddressModal();
});

// Close modal on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeAddressModal();
    }
});
</script>
{% endblock %}
