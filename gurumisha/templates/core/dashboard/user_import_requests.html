{% extends 'base_dashboard.html' %}
{% load static %}

{% block dashboard_title %}Import Requests{% endblock %}
{% block page_title %}Import Requests{% endblock %}
{% block page_description %}Manage your car import requests and track their progress{% endblock %}

{% block breadcrumb_items %}
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">Import Requests</span>
        </div>
    </li>
{% endblock %}

{% block dashboard_content %}
    <!-- Import Request Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                    <i class="fas fa-list text-blue-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-2xl font-bold text-harrier-dark">{{ total_requests }}</p>
                    <p class="text-gray-600 text-sm font-medium">Total Requests</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center">
                    <i class="fas fa-clock text-yellow-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-2xl font-bold text-yellow-600">{{ pending_requests }}</p>
                    <p class="text-gray-600 text-sm font-medium">Pending</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                    <i class="fas fa-cog text-blue-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-2xl font-bold text-blue-600">{{ in_progress_requests }}</p>
                    <p class="text-gray-600 text-sm font-medium">In Progress</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                    <i class="fas fa-check-circle text-green-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-2xl font-bold text-green-600">{{ completed_requests }}</p>
                    <p class="text-gray-600 text-sm font-medium">Completed</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-6 mb-8">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <div class="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
                <!-- Status Filter -->
                <div class="relative">
                    <select name="status" class="appearance-none bg-gray-50 border border-gray-300 rounded-lg px-4 py-3 pr-8 focus:ring-2 focus:ring-harrier-red focus:border-harrier-red transition-all duration-200" 
                            hx-get="{% url 'core:user_import_requests' %}" 
                            hx-trigger="change" 
                            hx-target="#import-requests-list"
                            hx-include="[name='search']">
                        <option value="">All Status</option>
                        {% for status_code, status_display in status_choices %}
                            <option value="{{ status_code }}" {% if status_filter == status_code %}selected{% endif %}>
                                {{ status_display }}
                            </option>
                        {% endfor %}
                    </select>
                    <i class="fas fa-chevron-down absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none"></i>
                </div>
                
                <!-- Date Filter -->
                <div class="relative">
                    <select name="date_range" class="appearance-none bg-gray-50 border border-gray-300 rounded-lg px-4 py-3 pr-8 focus:ring-2 focus:ring-harrier-red focus:border-harrier-red transition-all duration-200">
                        <option value="">All Time</option>
                        <option value="7">Last 7 Days</option>
                        <option value="30">Last 30 Days</option>
                        <option value="90">Last 3 Months</option>
                        <option value="365">Last Year</option>
                    </select>
                    <i class="fas fa-chevron-down absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none"></i>
                </div>
            </div>
            
            <div class="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
                <!-- Search -->
                <div class="relative">
                    <input type="text" 
                           name="search" 
                           value="{{ search_query }}"
                           placeholder="Search by brand, model, or country..." 
                           class="bg-gray-50 border border-gray-300 rounded-lg px-4 py-3 pl-10 focus:ring-2 focus:ring-harrier-red focus:border-harrier-red transition-all duration-200 w-full sm:w-80"
                           hx-get="{% url 'core:user_import_requests' %}" 
                           hx-trigger="keyup changed delay:500ms" 
                           hx-target="#import-requests-list"
                           hx-include="[name='status']">
                    <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                </div>
                
                <!-- Add New Request Button -->
                <a href="{% url 'core:import_request' %}" 
                   class="bg-gradient-to-r from-harrier-red to-harrier-dark hover:from-harrier-dark hover:to-harrier-red text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 flex items-center justify-center whitespace-nowrap">
                    <i class="fas fa-plus mr-2"></i>
                    New Request
                </a>
            </div>
        </div>
    </div>

    <!-- Import Requests List -->
    <div id="import-requests-list">
        {% if import_requests %}
            <div class="space-y-6">
                {% for request in import_requests %}
                    <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden hover:shadow-xl transition-all duration-300 group">
                        <div class="p-6">
                            <div class="flex items-start justify-between mb-4">
                                <div class="flex-1">
                                    <div class="flex items-center justify-between mb-2">
                                        <h3 class="text-xl font-bold text-harrier-dark group-hover:text-harrier-red transition-colors">
                                            {{ request.year }} {{ request.brand }} {{ request.model }}
                                        </h3>
                                        <span class="px-3 py-1 text-sm font-bold rounded-full
                                            {% if request.status == 'completed' %}bg-green-100 text-green-800 border border-green-200
                                            {% elif request.status == 'cancelled' %}bg-red-100 text-red-800 border border-red-200
                                            {% elif request.status in 'processing,fee_paid' %}bg-blue-100 text-blue-800 border border-blue-200
                                            {% elif request.status == 'on_quotation' %}bg-purple-100 text-purple-800 border border-purple-200
                                            {% else %}bg-yellow-100 text-yellow-800 border border-yellow-200{% endif %}">
                                            {{ request.get_status_display }}
                                        </span>
                                    </div>
                                    
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                                        <div class="flex items-center text-gray-600">
                                            <i class="fas fa-globe mr-2 text-blue-500"></i>
                                            <span class="text-sm">{{ request.origin_country }}</span>
                                        </div>
                                        <div class="flex items-center text-gray-600">
                                            <i class="fas fa-palette mr-2 text-purple-500"></i>
                                            <span class="text-sm">{{ request.preferred_color|default:"Any Color" }}</span>
                                        </div>
                                        <div class="flex items-center text-gray-600">
                                            <i class="fas fa-calendar mr-2 text-green-500"></i>
                                            <span class="text-sm">{{ request.created_at|date:"M d, Y" }}</span>
                                        </div>
                                    </div>
                                    
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-4">
                                            <div class="text-sm">
                                                <span class="text-gray-600">Budget:</span>
                                                <span class="font-bold text-harrier-red">
                                                    KSh {{ request.budget_min|floatformat:0 }} - {{ request.budget_max|floatformat:0 }}
                                                </span>
                                            </div>
                                        </div>
                                        
                                        <div class="flex items-center space-x-3">
                                            {% if request.status == 'completed' and request.import_order %}
                                                <a href="{% url 'core:import_order_detail' request.import_order.order_number %}" 
                                                   class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center">
                                                    <i class="fas fa-truck mr-2"></i>
                                                    Track Order
                                                </a>
                                            {% endif %}
                                            
                                            <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center">
                                                <i class="fas fa-eye mr-2"></i>
                                                View Details
                                            </button>
                                            
                                            {% if request.status == 'pending' %}
                                                <button class="bg-harrier-red hover:bg-harrier-dark text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center">
                                                    <i class="fas fa-edit mr-2"></i>
                                                    Edit
                                                </button>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
            
            <!-- Pagination -->
            <div class="mt-8 flex items-center justify-center">
                <nav class="flex items-center space-x-2">
                    <button class="px-4 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gray-50 text-gray-700 transition-all duration-200">
                        <i class="fas fa-chevron-left mr-1"></i>Previous
                    </button>
                    <button class="px-4 py-2 text-sm bg-harrier-red text-white rounded-lg">1</button>
                    <button class="px-4 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gray-50 text-gray-700 transition-all duration-200">2</button>
                    <button class="px-4 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gray-50 text-gray-700 transition-all duration-200">3</button>
                    <button class="px-4 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gray-50 text-gray-700 transition-all duration-200">
                        Next<i class="fas fa-chevron-right ml-1"></i>
                    </button>
                </nav>
            </div>
        {% else %}
            <!-- Empty State -->
            <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-12 text-center">
                <div class="w-24 h-24 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-ship text-4xl text-blue-500"></i>
                </div>
                <h3 class="text-2xl font-bold text-harrier-dark mb-4">No Import Requests Yet</h3>
                <p class="text-gray-600 mb-8 max-w-md mx-auto">
                    Start your car import journey by creating your first import request. We'll help you find and import your dream car.
                </p>
                <a href="{% url 'core:import_request' %}" 
                   class="bg-gradient-to-r from-harrier-red to-harrier-dark hover:from-harrier-dark hover:to-harrier-red text-white px-8 py-4 rounded-xl font-bold text-lg transition-all duration-200 inline-flex items-center">
                    <i class="fas fa-plus mr-3"></i>
                    Create Import Request
                </a>
            </div>
        {% endif %}
    </div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Enhanced filtering and search functionality
    const statusFilter = document.querySelector('select[name="status"]');
    const dateFilter = document.querySelector('select[name="date_range"]');
    const searchInput = document.querySelector('input[name="search"]');
    
    // Add loading states for HTMX requests
    document.addEventListener('htmx:beforeRequest', function(event) {
        const target = event.target;
        if (target.matches('select[name="status"], input[name="search"]')) {
            document.getElementById('import-requests-list').classList.add('opacity-50');
        }
    });
    
    document.addEventListener('htmx:afterRequest', function(event) {
        document.getElementById('import-requests-list').classList.remove('opacity-50');
    });
    
    // Smooth animations for cards
    const cards = document.querySelectorAll('.group');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('animate-fade-in-up');
    });
});
</script>

<style>
@keyframes fade-in-up {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in-up {
    animation: fade-in-up 0.6s ease-out forwards;
}
</style>
{% endblock %}
