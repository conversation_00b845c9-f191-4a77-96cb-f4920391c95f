{% extends 'base_admin_dashboard.html' %}
{% load static %}

{% block page_title %}Tracking Management{% endblock %}
{% block page_description %}Manage 7-stage import car order tracking workflow{% endblock %}

{% block dashboard_content %}
<div class="space-y-8">
    <!-- Tracking Management Header -->
    <div class="flex justify-between items-center animate-fade-in-up" style="animation-delay: 0.1s;">
        <div>
            <h2 class="text-3xl font-bold text-harrier-dark font-montserrat">Tracking Management</h2>
            <p class="text-gray-600 mt-1 font-raleway">Manage the 7-stage import car order tracking workflow</p>
        </div>
        
        <div class="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3">
            <!-- Filter by Stage Dropdown -->
            <div class="relative" x-data="{ open: false }">
                <button @click="open = !open"
                        class="btn-admin-secondary text-sm flex items-center justify-between min-w-48"
                        :class="{ 'bg-harrier-red text-white': open }">
                    <span class="flex items-center">
                        <i class="fas fa-filter mr-2"></i>
                        <span id="filter-text">
                            {% if current_filter %}
                                {% for stage in workflow_stages %}
                                    {% if stage.key == current_filter %}{{ stage.name }}{% endif %}
                                {% endfor %}
                            {% else %}
                                All Stages
                            {% endif %}
                        </span>
                    </span>
                    <i class="fas fa-chevron-down ml-2 transition-transform duration-200"
                       :class="{ 'rotate-180': open }"></i>
                </button>

                <div x-show="open"
                     x-transition:enter="transition ease-out duration-200"
                     x-transition:enter-start="opacity-0 scale-95"
                     x-transition:enter-end="opacity-100 scale-100"
                     x-transition:leave="transition ease-in duration-150"
                     x-transition:leave-start="opacity-100 scale-100"
                     x-transition:leave-end="opacity-0 scale-95"
                     @click.away="open = false"
                     class="absolute right-0 mt-2 w-64 bg-white rounded-xl shadow-lg border border-gray-200 z-50 overflow-hidden">

                    <!-- All Stages Option -->
                    <button class="w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors duration-200 flex items-center border-b border-gray-100"
                            hx-get="{% url 'core:admin_tracking_management_table_partial' %}"
                            hx-target="#tracking-management-table"
                            hx-swap="innerHTML"
                            @click="open = false; document.getElementById('filter-text').textContent = 'All Stages'">
                        <div class="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-list text-gray-600"></i>
                        </div>
                        <div>
                            <div class="font-semibold text-gray-900 font-montserrat">All Stages</div>
                            <div class="text-sm text-gray-500">Show all import orders</div>
                        </div>
                    </button>

                    <!-- Individual Stage Options -->
                    {% for stage in workflow_stages %}
                    <button class="w-full px-4 py-3 text-left hover:bg-gray-50 transition-colors duration-200 flex items-center {% if not forloop.last %}border-b border-gray-100{% endif %}"
                            hx-get="{% url 'core:admin_tracking_management_table_partial' %}?status={{ stage.key }}"
                            hx-target="#tracking-management-table"
                            hx-swap="innerHTML"
                            @click="open = false; document.getElementById('filter-text').textContent = '{{ stage.name }}'">
                        <div class="w-8 h-8 bg-{{ stage.color }}-100 rounded-lg flex items-center justify-center mr-3">
                            <i class="{{ stage.icon }} text-{{ stage.color }}-600"></i>
                        </div>
                        <div>
                            <div class="font-semibold text-gray-900 font-montserrat">{{ stage.name }}</div>
                            <div class="text-sm text-gray-500">Stage {{ forloop.counter }}</div>
                        </div>
                    </button>
                    {% endfor %}
                </div>
            </div>

            <!-- New Import Order Button -->
            <button class="btn-admin-primary text-sm"
                    hx-get="{% url 'core:admin_import_order_add_modal' %}"
                    hx-target="body"
                    hx-swap="beforeend">
                <i class="fas fa-plus mr-2"></i>New Import Order
            </button>
        </div>
    </div>

    <!-- Tracking Stats -->
    <div class="grid grid-cols-1 md:grid-cols-5 gap-6 animate-fade-in-up" style="animation-delay: 0.2s;">
        <div class="admin-stat-card text-center">
            <div class="admin-stat-value text-harrier-red">{{ total_orders|default:0 }}</div>
            <div class="admin-stat-label">Total Orders</div>
        </div>
        <div class="admin-stat-card text-center">
            <div class="admin-stat-value text-blue-600">{{ import_request_orders|default:0 }}</div>
            <div class="admin-stat-label">Import Requests</div>
        </div>
        <div class="admin-stat-card text-center">
            <div class="admin-stat-value text-orange-600">{{ in_transit_orders|default:0 }}</div>
            <div class="admin-stat-label">In Transit</div>
        </div>
        <div class="admin-stat-card text-center">
            <div class="admin-stat-value text-teal-600">{{ arrived_orders|default:0 }}</div>
            <div class="admin-stat-label">Arrived</div>
        </div>
        <div class="admin-stat-card text-center">
            <div class="admin-stat-value text-green-600">{{ completed_orders|default:0 }}</div>
            <div class="admin-stat-label">Delivered</div>
        </div>
    </div>

    <!-- Enhanced 7-Stage Workflow Overview -->
    <div class="admin-card animate-fade-in-up overflow-hidden" style="animation-delay: 0.3s;">
        <div class="bg-gradient-to-r from-harrier-red via-harrier-dark to-harrier-blue p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-white bg-opacity-20 rounded-xl flex items-center justify-center mr-4 backdrop-blur-sm">
                        <i class="fas fa-route text-white text-xl"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold text-white font-montserrat">7-Stage Import Workflow</h3>
                        <p class="text-white text-opacity-90 text-sm font-raleway">Complete tracking from confirmation to delivery</p>
                    </div>
                </div>
                <div class="text-white text-opacity-90">
                    <i class="fas fa-info-circle text-lg"></i>
                </div>
            </div>
        </div>

        <div class="p-6 bg-gradient-to-br from-gray-50 to-white">
            <!-- Progress Flow Visualization -->
            <div class="relative mb-8">
                <div class="absolute top-8 left-0 right-0 h-1 bg-gray-200 rounded-full"></div>
                <div class="absolute top-8 left-0 h-1 bg-gradient-to-r from-harrier-red to-harrier-blue rounded-full" style="width: 100%;"></div>

                <div class="relative grid grid-cols-3 md:grid-cols-5 lg:grid-cols-9 gap-2">
                    {% for stage in workflow_stages %}
                    <div class="workflow-stage-enhanced text-center group cursor-pointer"
                         data-stage="{{ stage.key }}"
                         onclick="filterByStage('{{ stage.key }}', '{{ stage.name }}')">
                        <!-- Stage Icon Circle -->
                        <div class="relative mx-auto mb-4">
                            <div class="w-16 h-16 bg-gradient-to-br from-{{ stage.color }}-400 to-{{ stage.color }}-600 rounded-full flex items-center justify-center shadow-lg transform transition-all duration-300 group-hover:scale-110 group-hover:shadow-xl">
                                <i class="{{ stage.icon }} text-white text-xl"></i>
                            </div>
                            <!-- Stage Number Badge -->
                            <div class="absolute -top-2 -right-2 w-6 h-6 bg-white rounded-full flex items-center justify-center shadow-md border-2 border-{{ stage.color }}-200">
                                <span class="text-xs font-bold text-{{ stage.color }}-600">{{ forloop.counter }}</span>
                            </div>
                        </div>

                        <!-- Stage Info -->
                        <div class="space-y-1">
                            <h4 class="font-semibold text-gray-900 text-sm font-montserrat group-hover:text-{{ stage.color }}-600 transition-colors duration-200">
                                {{ stage.name }}
                            </h4>
                            <p class="text-xs text-gray-500 font-raleway">Stage {{ forloop.counter }}</p>

                            <!-- Stage Status Indicator -->
                            <div class="flex justify-center mt-2">
                                <div class="w-2 h-2 bg-{{ stage.color }}-400 rounded-full animate-pulse"></div>
                            </div>
                        </div>

                        <!-- Hover Effect Overlay -->
                        <div class="absolute inset-0 bg-gradient-to-t from-{{ stage.color }}-50 to-transparent rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Workflow Statistics -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
                <div class="bg-white rounded-xl p-4 border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-clock text-blue-600"></i>
                        </div>
                        <div>
                            <div class="text-lg font-bold text-gray-900 font-montserrat">{{ in_transit_orders }}</div>
                            <div class="text-sm text-gray-600 font-raleway">In Progress</div>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-xl p-4 border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-check-circle text-green-600"></i>
                        </div>
                        <div>
                            <div class="text-lg font-bold text-gray-900 font-montserrat">{{ completed_orders }}</div>
                            <div class="text-sm text-gray-600 font-raleway">Completed</div>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-xl p-4 border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-ship text-yellow-600"></i>
                        </div>
                        <div>
                            <div class="text-lg font-bold text-gray-900 font-montserrat">{{ arrived_orders }}</div>
                            <div class="text-sm text-gray-600 font-raleway">Arrived</div>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-xl p-4 border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-percentage text-purple-600"></i>
                        </div>
                        <div>
                            <div class="text-lg font-bold text-gray-900 font-montserrat">
                                {% if total_orders > 0 %}
                                    {{ completed_orders|floatformat:0 }}%
                                {% else %}
                                    0%
                                {% endif %}
                            </div>
                            <div class="text-sm text-gray-600 font-raleway">Success Rate</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Import Orders Tracking Table -->
    <div class="admin-card animate-fade-in-up" style="animation-delay: 0.4s;">
        <div class="p-6 border-b border-gray-200">
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-list-alt text-white"></i>
                        </div>
                        <h3 class="text-lg font-bold text-harrier-dark font-montserrat">Import Orders Tracking</h3>
                    </div>

                    <!-- Pagination Info -->
                    {% if is_paginated %}
                        <div class="flex items-center space-x-2">
                            <span class="px-3 py-1 bg-harrier-red bg-opacity-10 text-harrier-red rounded-full text-sm font-medium">
                                {{ paginator.count }} total orders
                            </span>
                            <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                                Page {{ page_obj.number }} of {{ paginator.num_pages }}
                            </span>
                        </div>
                    {% else %}
                        <span class="px-3 py-1 bg-harrier-red bg-opacity-10 text-harrier-red rounded-full text-sm font-medium">
                            {{ import_orders|length }} orders
                        </span>
                    {% endif %}
                </div>
                <div class="flex space-x-2">
                    <!-- Export Dropdown -->
                    <div class="relative" x-data="{ open: false }">
                        <button @click="open = !open" type="button" class="text-sm text-gray-600 hover:text-harrier-red transition-colors duration-200 px-3 py-2 rounded-lg hover:bg-gray-50">
                            <i class="fas fa-download mr-1"></i>Export
                            <i class="fas fa-chevron-down ml-1 text-xs"></i>
                        </button>
                        <div x-show="open" @click.away="open = false" x-transition class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                            <div class="py-1">
                                <a href="{% url 'core:admin_tracking_management_export_csv' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}"
                                   class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200">
                                    <i class="fas fa-file-csv mr-2 text-green-600"></i>Export as CSV
                                </a>
                                <a href="{% url 'core:admin_tracking_management_export_excel' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}"
                                   class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200">
                                    <i class="fas fa-file-excel mr-2 text-green-600"></i>Export as Excel
                                </a>
                            </div>
                        </div>
                    </div>

                    <button class="text-sm text-gray-600 hover:text-harrier-red transition-colors duration-200 px-3 py-2 rounded-lg hover:bg-gray-50"
                            hx-get="{% url 'core:admin_tracking_management_table_partial' %}?page={{ page_obj.number|default:1 }}{% if current_filter %}&status={{ current_filter }}{% endif %}{% if current_search %}&search={{ current_search }}{% endif %}{% if current_date_from %}&date_from={{ current_date_from }}{% endif %}{% if current_date_to %}&date_to={{ current_date_to }}{% endif %}"
                            hx-target="#tracking-management-table"
                            hx-swap="outerHTML"
                            hx-indicator="#refresh-indicator"
                            title="Refresh table data">
                        <i class="fas fa-sync mr-1" id="refresh-indicator"></i>Refresh
                    </button>
                </div>
            </div>

            <!-- Search and Filters -->
            <div class="flex flex-col lg:flex-row gap-4">
                <!-- Search Bar -->
                <div class="flex-1">
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                        <input type="text"
                               name="search"
                               id="tracking-search"
                               value="{{ current_search }}"
                               placeholder="Search by order number, customer, brand, model, chassis number..."
                               class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-harrier-red focus:border-harrier-red sm:text-sm transition-colors duration-200"
                               hx-get="{% url 'core:admin_tracking_management_table_partial' %}?page=1"
                               hx-target="#tracking-management-table"
                               hx-swap="outerHTML"
                               hx-trigger="keyup changed delay:500ms"
                               hx-include="[name='status'], [name='date_from'], [name='date_to']"
                               hx-indicator="#search-indicator">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <i class="fas fa-spinner fa-spin text-gray-400 hidden" id="search-indicator"></i>
                        </div>
                    </div>
                </div>

                <!-- Date Range Filters -->
                <div class="flex gap-2">
                    <input type="date"
                           name="date_from"
                           value="{{ current_date_from }}"
                           class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-harrier-red focus:border-harrier-red text-sm"
                           hx-get="{% url 'core:admin_tracking_management_table_partial' %}?page=1"
                           hx-target="#tracking-management-table"
                           hx-swap="outerHTML"
                           hx-trigger="change"
                           hx-include="[name='search'], [name='status'], [name='date_to']"
                           placeholder="From date">
                    <input type="date"
                           name="date_to"
                           value="{{ current_date_to }}"
                           class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-harrier-red focus:border-harrier-red text-sm"
                           hx-get="{% url 'core:admin_tracking_management_table_partial' %}?page=1"
                           hx-target="#tracking-management-table"
                           hx-swap="outerHTML"
                           hx-trigger="change"
                           hx-include="[name='search'], [name='status'], [name='date_from']"
                           placeholder="To date">
                </div>

                <!-- Clear Filters -->
                {% if current_search or current_date_from or current_date_to %}
                <button type="button"
                        class="px-4 py-2 text-sm text-gray-600 hover:text-harrier-red border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                        onclick="clearFilters()">
                    <i class="fas fa-times mr-1"></i>Clear
                </button>
                {% endif %}
            </div>
        </div>
        
        <div class="overflow-x-auto">
            <div id="tracking-management-table">
                {% include 'core/dashboard/partials/admin_tracking_management_table.html' %}
            </div>
        </div>
    </div>
</div>

<!-- Status Update Modal -->
<div id="status-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-xl shadow-xl max-w-md w-full mx-4">
        <div class="bg-gradient-to-r from-harrier-red to-harrier-dark p-6 rounded-t-xl">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-white font-montserrat">Update Tracking Status</h3>
                <button onclick="closeStatusModal()" class="text-white hover:text-gray-200 transition-colors">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>

        <form id="status-form" class="p-6">
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2 font-raleway">New Status</label>
                    <select id="status-select" name="status" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-harrier-red focus:border-harrier-red">
                        {% for stage in workflow_stages %}
                        <option value="{{ stage.key }}">{{ stage.name }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2 font-raleway">Notes (Optional)</label>
                    <textarea name="notes" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-harrier-red focus:border-harrier-red" placeholder="Add any relevant notes about this status update..."></textarea>
                </div>
            </div>

            <div class="flex space-x-3 mt-6">
                <button type="button" onclick="closeStatusModal()" class="flex-1 btn-admin-secondary">
                    Cancel
                </button>
                <button type="submit" class="flex-1 btn-admin-primary">
                    Update Status
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_css %}
{{ block.super }}
<style>
    /* Tracking Management specific styles */
    .workflow-stage {
        transform: translateY(0);
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .workflow-stage:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    /* Progress bar animations */
    .bg-gradient-to-r {
        background: linear-gradient(90deg, #DC2626, #EF4444);
        animation: progressGlow 2s ease-in-out infinite alternate;
    }

    @keyframes progressGlow {
        0% {
            box-shadow: 0 0 5px rgba(220, 38, 38, 0.5);
        }
        100% {
            box-shadow: 0 0 15px rgba(220, 38, 38, 0.8);
        }
    }

    /* Table row hover effects */
    tbody tr:hover {
        background-color: #f9fafb;
        transform: translateX(2px);
        transition: all 0.2s ease;
    }

    /* Status badge hover effects */
    .inline-flex {
        transition: all 0.2s ease;
    }

    .inline-flex:hover {
        transform: scale(1.05);
    }

    /* Modal animations */
    #status-modal {
        animation: fadeIn 0.3s ease-out;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
        }
        to {
            opacity: 1;
        }
    }

    /* Filter Dropdown Styles */
    .filter-dropdown-option:hover {
        background: linear-gradient(135deg, rgba(220, 38, 38, 0.05) 0%, rgba(239, 68, 68, 0.05) 100%);
        transform: translateX(2px);
    }

    /* HTMX Loading Indicators */
    .htmx-request #refresh-indicator {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    /* Enhanced Workflow Stage Styles */
    .workflow-stage-enhanced {
        position: relative;
        padding: 1rem;
        border-radius: 1rem;
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .workflow-stage-enhanced:hover {
        transform: translateY(-8px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        background: rgba(255, 255, 255, 0.95);
    }

    .workflow-stage-enhanced:active {
        transform: translateY(-4px);
    }

    /* Progress Flow Animation */
    .workflow-stage-enhanced .w-16 {
        position: relative;
        overflow: hidden;
    }

    .workflow-stage-enhanced .w-16::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        transition: left 0.5s ease;
    }

    .workflow-stage-enhanced:hover .w-16::before {
        left: 100%;
    }

    /* Active Stage Indicator */
    .workflow-stage-enhanced.active-stage {
        background: rgba(220, 38, 38, 0.1);
        border-color: rgba(220, 38, 38, 0.3);
        transform: translateY(-4px);
        box-shadow: 0 12px 24px rgba(220, 38, 38, 0.2);
    }

    .workflow-stage-enhanced.active-stage .w-16 {
        box-shadow: 0 8px 16px rgba(220, 38, 38, 0.3);
    }

    /* Enhanced Button Hover Effects */
    .btn-admin-secondary:hover,
    .btn-admin-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    /* Mobile responsiveness */
    @media (max-width: 768px) {
        .workflow-stage {
            padding: 0.75rem;
        }

        .admin-stat-card {
            padding: 1rem;
        }

        .filter-dropdown {
            position: static !important;
            width: 100% !important;
            margin-top: 0.5rem !important;
        }
    }
</style>

<script>
let currentOrderId = null;

function openStatusModal(orderId, currentStatus) {
    currentOrderId = orderId;
    document.getElementById('status-select').value = currentStatus;
    document.getElementById('status-modal').classList.remove('hidden');
}

function closeStatusModal() {
    currentOrderId = null;
    document.getElementById('status-modal').classList.add('hidden');
    document.getElementById('status-form').reset();
}

// Handle status form submission - with null checks
document.addEventListener('DOMContentLoaded', function() {
    // Wait for Alpine.js to be ready
    if (window.Alpine) {
        Alpine.start();
    }
    const statusForm = document.getElementById('status-form');
    const statusModal = document.getElementById('status-modal');

    if (statusForm) {
        statusForm.addEventListener('submit', function(e) {
            e.preventDefault();

            if (!currentOrderId) return;

            const formData = new FormData(this);
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');

            if (!csrfToken) {
                console.error('CSRF token not found');
                if (window.showError) {
                    showError('Security token not found. Please refresh the page.');
                } else {
                    alert('Security token not found. Please refresh the page.');
                }
                return;
            }

            fetch(`/dashboard/admin/tracking/update-status/${currentOrderId}/`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': csrfToken.value
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message using toast manager if available
                    if (window.showSuccess) {
                        showSuccess('Status updated successfully!');
                    } else {
                        alert('Status updated successfully!');
                    }
                    // Reload the page to show updated status
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    if (window.showError) {
                        showError('Error: ' + data.error);
                    } else {
                        alert('Error: ' + data.error);
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                if (window.showError) {
                    showError('An error occurred while updating the status.');
                } else {
                    alert('An error occurred while updating the status.');
                }
            });

            closeStatusModal();
        });
    }

    // Close modal on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeStatusModal();
        }
    });

    // Close modal on backdrop click
    if (statusModal) {
        statusModal.addEventListener('click', function(e) {
            if (e.target === this) {
                closeStatusModal();
            }
        });
    }
});

// Filter by workflow stage function
function filterByStage(stageKey, stageName) {
    // Update filter text
    document.getElementById('filter-text').textContent = stageName;

    // Make HTMX request to filter table, reset to page 1
    const url = stageKey === 'all'
        ? '/dashboard/admin/tracking-management/table/?page=1'
        : `/dashboard/admin/tracking-management/table/?page=1&status=${stageKey}`;

    htmx.ajax('GET', url, {
        target: '#tracking-management-table',
        swap: 'outerHTML'
    });

    // Add visual feedback to clicked stage
    document.querySelectorAll('.workflow-stage-enhanced').forEach(stage => {
        stage.classList.remove('active-stage');
    });

    if (stageKey !== 'all') {
        document.querySelector(`[data-stage="${stageKey}"]`).classList.add('active-stage');
    }
}

// Clear all filters function
function clearFilters() {
    // Clear search input
    const searchInput = document.getElementById('tracking-search');
    if (searchInput) {
        searchInput.value = '';
    }

    // Clear date inputs
    const dateFromInput = document.querySelector('[name="date_from"]');
    const dateToInput = document.querySelector('[name="date_to"]');
    if (dateFromInput) dateFromInput.value = '';
    if (dateToInput) dateToInput.value = '';

    // Reload table without filters, reset to page 1
    htmx.ajax('GET', '/dashboard/admin/tracking-management/table/?page=1', {
        target: '#tracking-management-table',
        swap: 'outerHTML'
    });

    // Reload page to clear URL parameters
    setTimeout(() => {
        window.location.href = '/dashboard/admin/tracking-management/';
    }, 500);
}

// Enhanced search functionality
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('tracking-search');
    if (searchInput) {
        // Add search icon animation
        searchInput.addEventListener('focus', function() {
            this.parentElement.querySelector('.fa-search').classList.add('text-harrier-red');
        });

        searchInput.addEventListener('blur', function() {
            this.parentElement.querySelector('.fa-search').classList.remove('text-harrier-red');
        });

        // Show/hide search indicator
        document.addEventListener('htmx:beforeRequest', function(event) {
            if (event.target === searchInput) {
                document.getElementById('search-indicator').classList.remove('hidden');
            }
        });

        document.addEventListener('htmx:afterRequest', function(event) {
            if (event.target === searchInput) {
                document.getElementById('search-indicator').classList.add('hidden');
            }
        });
    }

    // Enhanced pagination experience
    initializePaginationEnhancements();
});

// Initialize pagination enhancements
function initializePaginationEnhancements() {
    // Add loading states to pagination buttons
    document.addEventListener('htmx:beforeRequest', function(event) {
        if (event.target.classList.contains('pagination-btn') ||
            event.target.classList.contains('pagination-btn-mobile')) {
            event.target.style.opacity = '0.6';
            event.target.style.pointerEvents = 'none';
        }
    });

    document.addEventListener('htmx:afterRequest', function(event) {
        // Re-enable pagination buttons after request
        document.querySelectorAll('.pagination-btn, .pagination-btn-mobile').forEach(btn => {
            btn.style.opacity = '1';
            btn.style.pointerEvents = 'auto';
        });

        // Scroll to top of table after pagination
        if (event.target.id === 'tracking-management-table') {
            const tableContainer = document.getElementById('tracking-management-table');
            if (tableContainer) {
                tableContainer.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        }
    });

    // Keyboard navigation for pagination
    document.addEventListener('keydown', function(event) {
        if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
            return; // Don't interfere with form inputs
        }

        const currentPage = parseInt(document.querySelector('.pagination-btn-active')?.textContent || '1');
        const totalPages = parseInt(document.querySelector('.pagination-btn:last-of-type')?.textContent || '1');

        if (event.key === 'ArrowLeft' && currentPage > 1) {
            event.preventDefault();
            const prevBtn = document.querySelector('.pagination-btn-nav[title="Previous page"]');
            if (prevBtn) prevBtn.click();
        } else if (event.key === 'ArrowRight' && currentPage < totalPages) {
            event.preventDefault();
            const nextBtn = document.querySelector('.pagination-btn-nav[title="Next page"]');
            if (nextBtn) nextBtn.click();
        }
    });
}
</script>
{% endblock %}

