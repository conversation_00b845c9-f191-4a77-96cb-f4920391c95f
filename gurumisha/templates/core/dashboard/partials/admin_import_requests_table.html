{% load static %}

<div class="overflow-x-auto" id="import-requests-table">
    <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider font-montserrat">
                    Order ID
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider font-montserrat">
                    Customer
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider font-montserrat">
                    Vehicle Details
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider font-montserrat">
                    Status
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider font-montserrat">
                    Progress
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider font-montserrat">
                    Actions
                </th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% if import_requests %}
                {% for request in import_requests %}
                <tr class="hover:bg-gray-50 transition-colors duration-200">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-harrier-dark font-raleway">#{{ request.id|stringformat:"05d" }}</div>
                        <div class="text-sm text-gray-500">{{ request.created_at|date:"M d, Y" }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="w-10 h-10 bg-harrier-red rounded-full flex items-center justify-center text-white font-semibold text-sm mr-3">
                                {{ request.customer.first_name|first|default:request.customer.username|first|upper }}
                            </div>
                            <div>
                                <div class="text-sm font-medium text-gray-900 font-raleway">{{ request.customer.get_full_name|default:request.customer.username }}</div>
                                <div class="text-sm text-gray-500">{{ request.customer.email }}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900 font-raleway">{{ request.year }} {{ request.brand }} {{ request.model }}</div>
                        <div class="text-sm text-gray-500">{{ request.origin_country }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium font-montserrat transition-all duration-200 hover:scale-105
                            {% if request.status == 'pending' %}bg-yellow-100 text-yellow-800 border border-yellow-200
                            {% elif request.status == 'on_quotation' %}bg-orange-100 text-orange-800 border border-orange-200
                            {% elif request.status == 'processing' %}bg-blue-100 text-blue-800 border border-blue-200
                            {% elif request.status == 'fee_paid' %}bg-purple-100 text-purple-800 border border-purple-200
                            {% elif request.status == 'shipped' %}bg-indigo-100 text-indigo-800 border border-indigo-200
                            {% elif request.status == 'arrived' %}bg-teal-100 text-teal-800 border border-teal-200
                            {% elif request.status == 'completed' %}bg-green-100 text-green-800 border border-green-200
                            {% elif request.status == 'cancelled' %}bg-red-100 text-red-800 border border-red-200
                            {% else %}bg-gray-100 text-gray-800 border border-gray-200{% endif %}">
                            <div class="w-2 h-2 rounded-full mr-2
                                {% if request.status == 'pending' %}bg-yellow-500
                                {% elif request.status == 'on_quotation' %}bg-orange-500
                                {% elif request.status == 'processing' %}bg-blue-500
                                {% elif request.status == 'fee_paid' %}bg-purple-500

                                {% elif request.status == 'completed' %}bg-green-500
                                {% elif request.status == 'cancelled' %}bg-red-500
                                {% else %}bg-gray-500{% endif %}"></div>
                            {{ request.get_status_display }}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="w-full bg-gray-200 rounded-full h-3 shadow-inner">
                            <div class="bg-gradient-to-r from-harrier-red to-harrier-dark h-3 rounded-full transition-all duration-500 ease-out"
                                 style="width: {% if request.status == 'pending' %}20%{% elif request.status == 'on_quotation' %}40%{% elif request.status == 'processing' %}60%{% elif request.status == 'fee_paid' %}80%{% elif request.status == 'completed' %}100%{% elif request.status == 'cancelled' %}0%{% else %}0%{% endif %}"></div>
                        </div>
                        <div class="text-xs text-gray-600 mt-1 font-medium">
                            {% if request.status == 'pending' %}20%{% elif request.status == 'on_quotation' %}40%{% elif request.status == 'processing' %}60%{% elif request.status == 'fee_paid' %}80%{% elif request.status == 'completed' %}100%{% elif request.status == 'cancelled' %}0%{% else %}0%{% endif %} Complete
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div class="flex space-x-2">
                            <!-- View Button -->
                            <button class="action-btn action-btn-view group"
                                    hx-get="{% url 'core:admin_import_request_view_modal' request.id %}"
                                    hx-target="body"
                                    hx-swap="beforeend"
                                    title="View Details">
                                <i class="fas fa-eye group-hover:scale-110 transition-transform duration-200"></i>
                            </button>

                            <!-- Edit Button -->
                            <button class="action-btn action-btn-edit group"
                                    hx-get="{% url 'core:admin_import_request_edit_modal' request.id %}"
                                    hx-target="body"
                                    hx-swap="beforeend"
                                    title="Edit Request">
                                <i class="fas fa-edit group-hover:scale-110 transition-transform duration-200"></i>
                            </button>

                            <!-- Status Update Button -->
                            <button class="action-btn action-btn-status group"
                                    hx-get="{% url 'core:admin_import_request_status_modal' request.id %}"
                                    hx-target="body"
                                    hx-swap="beforeend"
                                    title="Update Status">
                                <i class="fas fa-sync group-hover:rotate-180 transition-transform duration-300"></i>
                            </button>

                            <!-- Track Button (only enabled when completed and not already tracked) -->
                            {% if request.import_order %}
                            <button class="action-btn bg-green-600 text-white"
                                    disabled
                                    title="Already being tracked - Order #{{ request.import_order.order_number }}">
                                <i class="fas fa-check"></i>
                            </button>
                            {% elif request.status == 'completed' %}
                            <button class="action-btn action-btn-track group"
                                    hx-post="{% url 'core:admin_import_request_track' request.id %}"
                                    hx-headers='{"X-CSRFToken": "{{ csrf_token }}"}'
                                    hx-confirm="Move this request to tracking management?"
                                    hx-target="#import-requests-table"
                                    hx-swap="outerHTML"
                                    title="Start Tracking">
                                <i class="fas fa-route group-hover:scale-110 transition-transform duration-200"></i>
                            </button>
                            {% else %}
                            <button class="action-btn action-btn-track-disabled"
                                    disabled
                                    title="Available when status is Completed">
                                <i class="fas fa-route"></i>
                            </button>
                            {% endif %}

                            <!-- Delete Button -->
                            <button class="action-btn action-btn-delete group"
                                    hx-get="{% url 'core:admin_import_request_delete_modal' request.id %}"
                                    hx-target="body"
                                    hx-swap="beforeend"
                                    title="Delete Request">
                                <i class="fas fa-trash group-hover:scale-110 transition-transform duration-200"></i>
                            </button>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="6" class="px-6 py-12 text-center">
                        <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-ship text-gray-400 text-2xl"></i>
                        </div>
                        <h4 class="text-lg font-semibold text-gray-900 mb-2 font-montserrat">No import requests yet</h4>
                        <p class="text-gray-600 font-raleway">Import requests will appear here when customers submit them.</p>
                    </td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>
