<!-- Add New Modal Content -->
<div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" id="add-new-modal">
    <div class="bg-white rounded-xl shadow-xl max-w-md w-full mx-4 transform transition-all duration-300 scale-100">
        <!-- Modal Header -->
        <div class="bg-gradient-to-r from-harrier-red to-harrier-dark p-6 rounded-t-xl">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-white font-montserrat">Add New</h3>
                <button onclick="closeAddNewModal()" class="text-white hover:text-gray-200 transition-colors">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <p class="text-gray-200 text-sm mt-1">Choose what you'd like to add</p>
        </div>
        
        <!-- Modal Content -->
        <div class="p-6">
            <div class="grid grid-cols-2 gap-4">
                <!-- Add User -->
                <a href="#" class="add-new-option group">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-3 group-hover:bg-blue-200 transition-colors">
                        <i class="fas fa-user-plus text-blue-600 text-xl"></i>
                    </div>
                    <h4 class="font-semibold text-gray-900 text-sm">User</h4>
                    <p class="text-xs text-gray-600 mt-1">Add new user account</p>
                </a>
                
                <!-- Add Vendor -->
                <a href="#" class="add-new-option group">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-3 group-hover:bg-green-200 transition-colors">
                        <i class="fas fa-store text-green-600 text-xl"></i>
                    </div>
                    <h4 class="font-semibold text-gray-900 text-sm">Vendor</h4>
                    <p class="text-xs text-gray-600 mt-1">Add new vendor</p>
                </a>
                
                <!-- Add Car -->
                <a href="#" class="add-new-option group">
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-3 group-hover:bg-red-200 transition-colors">
                        <i class="fas fa-car text-red-600 text-xl"></i>
                    </div>
                    <h4 class="font-semibold text-gray-900 text-sm">Car Listing</h4>
                    <p class="text-xs text-gray-600 mt-1">Add new car</p>
                </a>
                
                <!-- Add Spare Part -->
                <a href="#" class="add-new-option group">
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mb-3 group-hover:bg-yellow-200 transition-colors">
                        <i class="fas fa-cogs text-yellow-600 text-xl"></i>
                    </div>
                    <h4 class="font-semibold text-gray-900 text-sm">Spare Part</h4>
                    <p class="text-xs text-gray-600 mt-1">Add spare part</p>
                </a>
                
                <!-- Add Import Request -->
                <a href="#" class="add-new-option group">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-3 group-hover:bg-purple-200 transition-colors">
                        <i class="fas fa-ship text-purple-600 text-xl"></i>
                    </div>
                    <h4 class="font-semibold text-gray-900 text-sm">Import Request</h4>
                    <p class="text-xs text-gray-600 mt-1">New import order</p>
                </a>
                
                <!-- Add Blog Post -->
                <a href="#" class="add-new-option group">
                    <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mb-3 group-hover:bg-indigo-200 transition-colors">
                        <i class="fas fa-newspaper text-indigo-600 text-xl"></i>
                    </div>
                    <h4 class="font-semibold text-gray-900 text-sm">Blog Post</h4>
                    <p class="text-xs text-gray-600 mt-1">Create content</p>
                </a>
            </div>
        </div>
        
        <!-- Modal Footer -->
        <div class="bg-gray-50 px-6 py-4 rounded-b-xl">
            <button onclick="closeAddNewModal()" class="w-full btn-admin-secondary text-sm">
                Cancel
            </button>
        </div>
    </div>
</div>

<style>
.add-new-option {
    @apply p-4 rounded-lg border border-gray-200 hover:border-gray-300 transition-all duration-200 text-center block;
}

.add-new-option:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

#add-new-modal {
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}
</style>

<script>
function closeAddNewModal() {
    const modal = document.getElementById('add-new-modal');
    if (modal) {
        modal.style.animation = 'fadeOut 0.3s ease-out';
        setTimeout(() => {
            modal.remove();
        }, 300);
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

// Close modal on escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeAddNewModal();
    }
});

// Close modal on backdrop click
document.getElementById('add-new-modal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeAddNewModal();
    }
});
</script>
