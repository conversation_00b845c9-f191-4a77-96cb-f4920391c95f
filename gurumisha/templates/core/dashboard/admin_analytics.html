{% extends 'base_admin_dashboard.html' %}
{% load static %}
{% load promotion_tags %}

{% block dashboard_title %}Analytics & Reports{% endblock %}
{% block page_title %}Analytics & Reports{% endblock %}
{% block page_description %}System analytics and performance reports{% endblock %}



{% block breadcrumb_items %}
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">Analytics</span>
        </div>
    </li>
{% endblock %}

{% block dashboard_content %}
    <!-- Key Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="admin-stat-card text-center">
            <div class="admin-stat-value text-blue-600">{{ total_users|default:0 }}</div>
            <div class="admin-stat-label">Total Users</div>
            <div class="text-xs text-gray-500 mt-1">
                <i class="fas fa-arrow-up text-green-500"></i> +12% this month
            </div>
        </div>
        <div class="admin-stat-card text-center">
            <div class="admin-stat-value text-green-600">{{ total_cars|default:0 }}</div>
            <div class="admin-stat-label">Car Listings</div>
            <div class="text-xs text-gray-500 mt-1">
                <i class="fas fa-arrow-up text-green-500"></i> +8% this month
            </div>
        </div>
        <div class="admin-stat-card text-center">
            <div class="admin-stat-value text-purple-600">{{ total_vendors|default:0 }}</div>
            <div class="admin-stat-label">Active Vendors</div>
            <div class="text-xs text-gray-500 mt-1">
                <i class="fas fa-arrow-up text-green-500"></i> +5% this month
            </div>
        </div>
        <div class="admin-stat-card text-center">
            <div class="admin-stat-value text-orange-600">{{ total_inquiries|default:0 }}</div>
            <div class="admin-stat-label">Total Inquiries</div>
            <div class="text-xs text-gray-500 mt-1">
                <i class="fas fa-arrow-up text-green-500"></i> +15% this month
            </div>
        </div>
    </div>

    <!-- Promotion Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="admin-stat-card text-center bg-gradient-to-br from-purple-500 to-purple-600 text-white">
            <div class="admin-stat-value">{{ featured_cars_count|default:0 }}</div>
            <div class="admin-stat-label">Featured Cars</div>
            <div class="text-xs text-purple-100 mt-1">
                <i class="fas fa-crown mr-1"></i>Active promotions
            </div>
        </div>
        <div class="admin-stat-card text-center bg-gradient-to-br from-red-500 to-red-600 text-white">
            <div class="admin-stat-value">{{ active_hot_deals|default:0 }}</div>
            <div class="admin-stat-label">Hot Deals</div>
            <div class="text-xs text-red-100 mt-1">
                <i class="fas fa-fire mr-1"></i>Active deals
            </div>
        </div>
        <div class="admin-stat-card text-center bg-gradient-to-br from-yellow-500 to-yellow-600 text-white">
            <div class="admin-stat-value">{{ avg_rating|floatformat:1 }}</div>
            <div class="admin-stat-label">Avg Rating</div>
            <div class="text-xs text-yellow-100 mt-1">
                <i class="fas fa-star mr-1"></i>Out of 5.0
            </div>
        </div>
        <div class="admin-stat-card text-center bg-gradient-to-br from-green-500 to-green-600 text-white">
            <div class="admin-stat-value">{{ conversion_funnel.featured_ctr|floatformat:1 }}%</div>
            <div class="admin-stat-label">Featured CTR</div>
            <div class="text-xs text-green-100 mt-1">
                <i class="fas fa-chart-line mr-1"></i>Click-through rate
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Monthly Growth Chart -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-heading font-bold text-harrier-dark">Monthly Growth</h3>
                <div class="flex items-center space-x-2">
                    <select class="text-sm border border-gray-300 rounded px-2 py-1">
                        <option>Last 6 months</option>
                        <option>Last 12 months</option>
                    </select>
                </div>
            </div>
            <div class="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                <div class="text-center text-gray-500">
                    <i class="fas fa-chart-line text-4xl mb-2"></i>
                    <p>Chart visualization would go here</p>
                    <p class="text-sm">Integration with Chart.js or similar library</p>
                </div>
            </div>
        </div>

        <!-- User Activity Chart -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-heading font-bold text-harrier-dark">User Activity</h3>
                <div class="flex items-center space-x-2">
                    <select class="text-sm border border-gray-300 rounded px-2 py-1">
                        <option>This week</option>
                        <option>This month</option>
                    </select>
                </div>
            </div>
            <div class="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                <div class="text-center text-gray-500">
                    <i class="fas fa-chart-bar text-4xl mb-2"></i>
                    <p>Activity chart would go here</p>
                    <p class="text-sm">Daily/Weekly user activity patterns</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Promotion Analytics Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Featured Cars Performance -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-heading font-bold text-harrier-dark">Featured Cars Performance</h3>
                <div class="flex space-x-2">
                    <button onclick="updateChart('featured')" class="text-sm bg-purple-100 text-purple-700 px-3 py-1 rounded hover:bg-purple-200">
                        <i class="fas fa-sync-alt mr-1"></i>Refresh
                    </button>
                </div>
            </div>
            <div class="space-y-4">
                {% for tier, data in featured_performance.items %}
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center">
                            {% tier_badge tier size="small" %}
                            <div class="ml-3">
                                <div class="font-medium text-gray-900">{{ tier|title }} Tier</div>
                                <div class="text-sm text-gray-500">{{ data.views }} views • {{ data.clicks }} clicks</div>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-lg font-bold text-purple-600">{{ data.ctr|floatformat:1 }}%</div>
                            <div class="text-xs text-gray-500">CTR</div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        </div>

        <!-- Hot Deals Analytics -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-lg font-heading font-bold text-harrier-dark">Hot Deals Performance</h3>
                <div class="flex space-x-2">
                    <button onclick="updateChart('hot_deals')" class="text-sm bg-red-100 text-red-700 px-3 py-1 rounded hover:bg-red-200">
                        <i class="fas fa-sync-alt mr-1"></i>Refresh
                    </button>
                </div>
            </div>
            <div class="space-y-4">
                {% for deal_data in hot_deals_performance|slice:":5" %}
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex-1">
                            <div class="font-medium text-gray-900 truncate">{{ deal_data.deal.title }}</div>
                            <div class="text-sm text-gray-500">
                                {{ deal_data.views }} views • {{ deal_data.clicks }} clicks
                                {% if deal_data.is_active %}
                                    <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                        Active
                                    </span>
                                {% else %}
                                    <span class="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                                        Expired
                                    </span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="text-right ml-4">
                            <div class="text-lg font-bold text-red-600">{{ deal_data.ctr|floatformat:1 }}%</div>
                            <div class="text-xs text-gray-500">CTR</div>
                        </div>
                    </div>
                {% empty %}
                    <div class="text-center py-8 text-gray-500">
                        <i class="fas fa-fire text-3xl mb-2"></i>
                        <p>No hot deals data available</p>
                    </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Rating Distribution and Conversion Funnel -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Rating Distribution -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-heading font-bold text-harrier-dark mb-6">Rating Distribution</h3>
            <div class="space-y-3">
                {% for rating, count in rating_distribution.items %}
                    {% if count > 0 %}
                        <div class="flex items-center">
                            <div class="w-16 text-sm font-medium text-gray-700">{{ rating }} ★</div>
                            <div class="flex-1 mx-3">
                                <div class="bg-gray-200 rounded-full h-2">
                                    <div class="bg-yellow-400 h-2 rounded-full transition-all duration-300"
                                         style="width: {% widthratio count 100 100 %}%"></div>
                                </div>
                            </div>
                            <div class="w-12 text-right text-sm text-gray-600">{{ count }}</div>
                        </div>
                    {% endif %}
                {% endfor %}
            </div>
        </div>

        <!-- Conversion Funnel -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-heading font-bold text-harrier-dark mb-6">Conversion Funnel</h3>
            <div class="space-y-4">
                <div class="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-eye text-purple-600 mr-3"></i>
                        <span class="font-medium">Featured Views</span>
                    </div>
                    <span class="text-lg font-bold text-purple-600">{{ conversion_funnel.featured_views }}</span>
                </div>
                <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-mouse-pointer text-blue-600 mr-3"></i>
                        <span class="font-medium">Featured Clicks</span>
                    </div>
                    <span class="text-lg font-bold text-blue-600">{{ conversion_funnel.featured_clicks }}</span>
                </div>
                <div class="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-fire text-red-600 mr-3"></i>
                        <span class="font-medium">Hot Deal Views</span>
                    </div>
                    <span class="text-lg font-bold text-red-600">{{ conversion_funnel.hot_deal_views }}</span>
                </div>
                <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-envelope text-green-600 mr-3"></i>
                        <span class="font-medium">Inquiries</span>
                    </div>
                    <span class="text-lg font-bold text-green-600">{{ conversion_funnel.inquiries }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Brands and Performance -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Top Car Brands -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-heading font-bold text-harrier-dark mb-6">Top Car Brands</h3>
            <div class="space-y-4">
                {% for brand in top_brands %}
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-harrier-red rounded-full flex items-center justify-center text-white font-semibold text-sm mr-3">
                                {{ brand.name|first|upper }}
                            </div>
                            <div>
                                <div class="font-medium text-harrier-dark">{{ brand.name }}</div>
                                <div class="text-sm text-gray-500">{{ brand.car_count }} listing{{ brand.car_count|pluralize }}</div>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm font-medium text-harrier-dark">{{ brand.car_count }}</div>
                            <div class="w-20 bg-gray-200 rounded-full h-2">
                                <div class="bg-harrier-red h-2 rounded-full" style="width: {% widthratio brand.car_count 50 100 %}%"></div>
                            </div>
                        </div>
                    </div>
                {% empty %}
                    <div class="text-center py-8 text-gray-500">
                        <i class="fas fa-car text-3xl mb-2"></i>
                        <p>No brand data available</p>
                    </div>
                {% endfor %}
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-heading font-bold text-harrier-dark mb-6">Recent System Activity</h3>
            <div class="space-y-4">
                <div class="flex items-center justify-between py-3 border-b border-gray-100">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-user-plus text-green-600 text-sm"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-harrier-dark">New user registration</p>
                            <p class="text-xs text-gray-600">Customer account created</p>
                        </div>
                    </div>
                    <span class="text-xs text-gray-500">2h ago</span>
                </div>
                
                <div class="flex items-center justify-between py-3 border-b border-gray-100">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-car text-blue-600 text-sm"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-harrier-dark">Car listing approved</p>
                            <p class="text-xs text-gray-600">2020 Toyota Camry approved</p>
                        </div>
                    </div>
                    <span class="text-xs text-gray-500">4h ago</span>
                </div>
                
                <div class="flex items-center justify-between py-3 border-b border-gray-100">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-store text-orange-600 text-sm"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-harrier-dark">Vendor approved</p>
                            <p class="text-xs text-gray-600">ABC Motors verified</p>
                        </div>
                    </div>
                    <span class="text-xs text-gray-500">6h ago</span>
                </div>
                
                <div class="flex items-center justify-between py-3">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-ship text-purple-600 text-sm"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-harrier-dark">Import request</p>
                            <p class="text-xs text-gray-600">2023 Honda Civic requested</p>
                        </div>
                    </div>
                    <span class="text-xs text-gray-500">8h ago</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Export and Reports -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-heading font-bold text-harrier-dark">Export Reports</h3>
            <div class="flex items-center space-x-2">
                <button class="btn-harrier-secondary">
                    <i class="fas fa-download mr-2"></i>Export Data
                </button>
                <button class="btn-harrier-primary">
                    <i class="fas fa-file-pdf mr-2"></i>Generate Report
                </button>
            </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="border border-gray-200 rounded-lg p-4 text-center hover:border-harrier-red transition-colors cursor-pointer">
                <i class="fas fa-users text-2xl text-blue-600 mb-2"></i>
                <h4 class="font-medium text-harrier-dark">User Report</h4>
                <p class="text-sm text-gray-600">Export user data and statistics</p>
            </div>
            
            <div class="border border-gray-200 rounded-lg p-4 text-center hover:border-harrier-red transition-colors cursor-pointer">
                <i class="fas fa-car text-2xl text-green-600 mb-2"></i>
                <h4 class="font-medium text-harrier-dark">Listings Report</h4>
                <p class="text-sm text-gray-600">Export car listings and performance</p>
            </div>
            
            <div class="border border-gray-200 rounded-lg p-4 text-center hover:border-harrier-red transition-colors cursor-pointer">
                <i class="fas fa-chart-line text-2xl text-purple-600 mb-2"></i>
                <h4 class="font-medium text-harrier-dark">Analytics Report</h4>
                <p class="text-sm text-gray-600">Export comprehensive analytics</p>
            </div>
        </div>
    </div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Promotion Analytics JavaScript
class PromotionAnalytics {
    constructor() {
        this.charts = {};
        this.init();
    }

    init() {
        this.initCharts();
        this.bindEvents();
    }

    initCharts() {
        // Featured Performance Chart
        const featuredCtx = document.getElementById('featuredPerformanceChart');
        if (featuredCtx) {
            this.charts.featured = new Chart(featuredCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Platinum', 'Gold', 'Silver', 'Bronze'],
                    datasets: [{
                        data: [
                            {{ featured_performance.platinum.views|default:0 }},
                            {{ featured_performance.gold.views|default:0 }},
                            {{ featured_performance.silver.views|default:0 }},
                            {{ featured_performance.bronze.views|default:0 }}
                        ],
                        backgroundColor: ['#9333ea', '#eab308', '#6b7280', '#d97706'],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // Rating Distribution Chart
        const ratingCtx = document.getElementById('ratingDistributionChart');
        if (ratingCtx) {
            this.charts.rating = new Chart(ratingCtx, {
                type: 'bar',
                data: {
                    labels: [{% for rating, count in rating_distribution.items %}'{{ rating }}★'{% if not forloop.last %},{% endif %}{% endfor %}],
                    datasets: [{
                        label: 'Number of Ratings',
                        data: [{% for rating, count in rating_distribution.items %}{{ count }}{% if not forloop.last %},{% endif %}{% endfor %}],
                        backgroundColor: '#fbbf24',
                        borderColor: '#f59e0b',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // Daily Metrics Chart
        const dailyCtx = document.getElementById('dailyMetricsChart');
        if (dailyCtx) {
            this.charts.daily = new Chart(dailyCtx, {
                type: 'line',
                data: {
                    labels: [{% for day in daily_metrics %}'{{ day.date|date:"M d" }}'{% if not forloop.last %},{% endif %}{% endfor %}],
                    datasets: [{
                        label: 'Featured Views',
                        data: [{% for day in daily_metrics %}{{ day.featured_views }}{% if not forloop.last %},{% endif %}{% endfor %}],
                        borderColor: '#9333ea',
                        backgroundColor: 'rgba(147, 51, 234, 0.1)',
                        tension: 0.4
                    }, {
                        label: 'Featured Clicks',
                        data: [{% for day in daily_metrics %}{{ day.featured_clicks }}{% if not forloop.last %},{% endif %}{% endfor %}],
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4
                    }, {
                        label: 'Hot Deal Views',
                        data: [{% for day in daily_metrics %}{{ day.hot_deal_views }}{% if not forloop.last %},{% endif %}{% endfor %}],
                        borderColor: '#ef4444',
                        backgroundColor: 'rgba(239, 68, 68, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }
    }

    bindEvents() {
        // Date range change
        const dateRange = document.getElementById('dateRange');
        if (dateRange) {
            dateRange.addEventListener('change', () => {
                this.updateAnalytics();
            });
        }
    }

    updateAnalytics() {
        const days = document.getElementById('dateRange').value;
        window.location.href = `?days=${days}`;
    }

    updateChart(type) {
        const days = document.getElementById('dateRange')?.value || 30;

        fetch(`{% url 'core:promotion_analytics_api' %}?metric=${type}&days=${days}`)
            .then(response => response.json())
            .then(data => {
                // Update chart based on type
                if (type === 'featured' && this.charts.featured) {
                    // Update featured chart data
                    this.charts.featured.data.datasets[0].data = [
                        data.platinum?.views || 0,
                        data.gold?.views || 0,
                        data.silver?.views || 0,
                        data.bronze?.views || 0
                    ];
                    this.charts.featured.update();
                }

                this.showToast('Chart updated successfully', 'success');
            })
            .catch(error => {
                console.error('Error updating chart:', error);
                this.showToast('Error updating chart', 'error');
            });
    }

    exportReport() {
        const days = document.getElementById('dateRange')?.value || 30;

        // Create form for export
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{% url "core:admin_analytics" %}';

        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]');
        if (csrfToken) {
            form.appendChild(csrfToken.cloneNode());
        }

        const daysInput = document.createElement('input');
        daysInput.type = 'hidden';
        daysInput.name = 'days';
        daysInput.value = days;
        form.appendChild(daysInput);

        const exportInput = document.createElement('input');
        exportInput.type = 'hidden';
        exportInput.name = 'export';
        exportInput.value = 'true';
        form.appendChild(exportInput);

        document.body.appendChild(form);
        form.submit();
        document.body.removeChild(form);
    }

    showToast(message, type = 'info') {
        // Use existing toast system if available
        if (window.showToast) {
            window.showToast(message, type);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }
}

// Initialize analytics
document.addEventListener('DOMContentLoaded', () => {
    window.promotionAnalytics = new PromotionAnalytics();
});

// Global functions for button clicks
function updateChart(type) {
    if (window.promotionAnalytics) {
        window.promotionAnalytics.updateChart(type);
    }
}

function exportReport() {
    if (window.promotionAnalytics) {
        window.promotionAnalytics.exportReport();
    }
}

function updateAnalytics() {
    if (window.promotionAnalytics) {
        window.promotionAnalytics.updateAnalytics();
    }
}
</script>
{% endblock %}
