{% extends 'base_dashboard.html' %}
{% load static %}

{% block dashboard_title %}Settings{% endblock %}
{% block page_title %}Settings{% endblock %}
{% block page_description %}Manage your account preferences and settings{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/profile-forms.css' %}">
{% endblock %}

{% block breadcrumb_items %}
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">Settings</span>
        </div>
    </li>
{% endblock %}

{% block dashboard_content %}
    <!-- Settings Header -->
    <div class="mb-8 bg-gradient-to-r from-harrier-red via-harrier-dark to-harrier-blue rounded-2xl p-8 text-white relative overflow-hidden animate-fade-in-up">
        <div class="absolute inset-0 bg-black/20"></div>
        <div class="absolute top-0 right-0 w-64 h-64 bg-white/5 rounded-full -translate-y-32 translate-x-32"></div>
        <div class="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24"></div>
        <div class="relative z-10">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-3xl font-bold mb-2 font-montserrat">Account Settings</h2>
                    <p class="text-blue-100 text-lg font-raleway">Customize your experience and preferences</p>
                </div>
                <div class="hidden md:block">
                    <div class="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center backdrop-blur-sm">
                        <i class="fas fa-cog text-white text-2xl"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Navigation Tabs -->
    <div class="mb-8 animate-fade-in-up" style="animation-delay: 0.1s;">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                <button type="button" class="profile-tab active" data-tab="notifications">
                    <i class="fas fa-bell mr-2"></i>Notifications
                </button>
                <button type="button" class="profile-tab" data-tab="privacy">
                    <i class="fas fa-user-shield mr-2"></i>Privacy
                </button>
                <button type="button" class="profile-tab" data-tab="account">
                    <i class="fas fa-user-cog mr-2"></i>Account
                </button>
                {% if user.role == 'vendor' %}
                <button type="button" class="profile-tab" data-tab="business">
                    <i class="fas fa-building mr-2"></i>Business
                </button>
                <button type="button" class="profile-tab" data-tab="hours">
                    <i class="fas fa-clock mr-2"></i>Business Hours
                </button>
                {% endif %}
            </nav>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-4 gap-8 animate-fade-in-up" style="animation-delay: 0.2s;">
        <!-- Main Settings Form -->
        <div class="lg:col-span-3">
            <!-- Notifications Tab -->
            <div class="tab-content active" id="notifications-tab">
                <form method="post" id="notificationsForm" class="space-y-8">
                    {% csrf_token %}
                    <input type="hidden" name="form_type" value="preferences">
                    
                    <div class="form-section form-fade-in">
                        <div class="form-section-header">
                            <div class="form-section-icon bg-gradient-to-br from-blue-500 to-blue-600">
                                <i class="fas fa-bell text-white text-sm"></i>
                            </div>
                            <div>
                                <h3 class="form-section-title">Notification Preferences</h3>
                                <p class="form-section-subtitle">Choose how you want to be notified</p>
                            </div>
                        </div>
                        
                        <div class="space-y-6">
                            <!-- Email Notifications -->
                            <div class="bg-gray-50 rounded-xl p-6">
                                <h4 class="font-bold text-harrier-dark mb-4 flex items-center">
                                    <i class="fas fa-envelope mr-2 text-blue-500"></i>Email Notifications
                                </h4>
                                <div class="space-y-4">
                                    <div class="form-group-inline">
                                        {{ user_prefs_form.email_notifications }}
                                        <label for="{{ user_prefs_form.email_notifications.id_for_label }}" class="form-label mb-0">
                                            General Email Notifications
                                        </label>
                                    </div>
                                    <p class="text-sm text-gray-600 ml-9">Receive important updates and alerts via email</p>
                                    
                                    <div class="form-group-inline">
                                        {{ user_prefs_form.marketing_emails }}
                                        <label for="{{ user_prefs_form.marketing_emails.id_for_label }}" class="form-label mb-0">
                                            Marketing Emails
                                        </label>
                                    </div>
                                    <p class="text-sm text-gray-600 ml-9">Receive promotional offers and product updates</p>
                                    
                                    <div class="form-group-inline">
                                        {{ user_prefs_form.newsletter_subscription }}
                                        <label for="{{ user_prefs_form.newsletter_subscription.id_for_label }}" class="form-label mb-0">
                                            Newsletter Subscription
                                        </label>
                                    </div>
                                    <p class="text-sm text-gray-600 ml-9">Subscribe to our monthly newsletter</p>
                                </div>
                            </div>
                            
                            <!-- SMS Notifications -->
                            <div class="bg-gray-50 rounded-xl p-6">
                                <h4 class="font-bold text-harrier-dark mb-4 flex items-center">
                                    <i class="fas fa-sms mr-2 text-green-500"></i>SMS Notifications
                                </h4>
                                <div class="space-y-4">
                                    <div class="form-group-inline">
                                        {{ user_prefs_form.sms_notifications }}
                                        <label for="{{ user_prefs_form.sms_notifications.id_for_label }}" class="form-label mb-0">
                                            SMS Notifications
                                        </label>
                                    </div>
                                    <p class="text-sm text-gray-600 ml-9">Receive urgent notifications via SMS</p>
                                </div>
                            </div>
                            
                            {% if vendor and vendor_prefs_form %}
                            <!-- Vendor Notifications -->
                            <div class="bg-blue-50 rounded-xl p-6">
                                <h4 class="font-bold text-harrier-dark mb-4 flex items-center">
                                    <i class="fas fa-building mr-2 text-orange-500"></i>Business Notifications
                                </h4>
                                <div class="space-y-4">
                                    <div class="form-group-inline">
                                        {{ vendor_prefs_form.inquiry_notifications }}
                                        <label for="{{ vendor_prefs_form.inquiry_notifications.id_for_label }}" class="form-label mb-0">
                                            Customer Inquiries
                                        </label>
                                    </div>
                                    <p class="text-sm text-gray-600 ml-9">Get notified when customers inquire about your listings</p>
                                    
                                    <div class="form-group-inline">
                                        {{ vendor_prefs_form.order_notifications }}
                                        <label for="{{ vendor_prefs_form.order_notifications.id_for_label }}" class="form-label mb-0">
                                            Order Updates
                                        </label>
                                    </div>
                                    <p class="text-sm text-gray-600 ml-9">Receive notifications about new orders and status changes</p>
                                    
                                    <div class="form-group-inline">
                                        {{ vendor_prefs_form.promotion_notifications }}
                                        <label for="{{ vendor_prefs_form.promotion_notifications.id_for_label }}" class="form-label mb-0">
                                            Promotion Opportunities
                                        </label>
                                    </div>
                                    <p class="text-sm text-gray-600 ml-9">Learn about new promotion and advertising opportunities</p>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="flex justify-end">
                        <button type="submit" class="form-button-primary">
                            <i class="fas fa-save mr-2"></i>Save Notification Settings
                        </button>
                    </div>
                </form>
            </div>

            <!-- Privacy Tab -->
            <div class="tab-content" id="privacy-tab">
                <form method="post" id="privacyForm" class="space-y-8">
                    {% csrf_token %}
                    <input type="hidden" name="form_type" value="preferences">
                    
                    <div class="form-section form-fade-in">
                        <div class="form-section-header">
                            <div class="form-section-icon bg-gradient-to-br from-green-500 to-green-600">
                                <i class="fas fa-user-shield text-white text-sm"></i>
                            </div>
                            <div>
                                <h3 class="form-section-title">Privacy Settings</h3>
                                <p class="form-section-subtitle">Control who can see your information</p>
                            </div>
                        </div>
                        
                        <div class="space-y-6">
                            <!-- Profile Visibility -->
                            <div class="bg-gray-50 rounded-xl p-6">
                                <h4 class="font-bold text-harrier-dark mb-4">Profile Visibility</h4>
                                <div class="form-group">
                                    <label for="{{ user_prefs_form.profile_visibility.id_for_label }}" class="form-label">
                                        Who can view your profile
                                    </label>
                                    {{ user_prefs_form.profile_visibility }}
                                    <p class="text-xs text-gray-500 mt-1">Control who can see your profile information</p>
                                </div>
                            </div>
                            
                            <!-- Contact Information Visibility -->
                            <div class="bg-gray-50 rounded-xl p-6">
                                <h4 class="font-bold text-harrier-dark mb-4">Contact Information</h4>
                                <div class="space-y-4">
                                    <div class="form-group-inline">
                                        {{ user_prefs_form.show_email }}
                                        <label for="{{ user_prefs_form.show_email.id_for_label }}" class="form-label mb-0">
                                            Show Email Address
                                        </label>
                                    </div>
                                    <p class="text-sm text-gray-600 ml-9">Allow others to see your email address</p>
                                    
                                    <div class="form-group-inline">
                                        {{ user_prefs_form.show_phone }}
                                        <label for="{{ user_prefs_form.show_phone.id_for_label }}" class="form-label mb-0">
                                            Show Phone Number
                                        </label>
                                    </div>
                                    <p class="text-sm text-gray-600 ml-9">Allow others to see your phone number</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex justify-end">
                        <button type="submit" class="form-button-primary">
                            <i class="fas fa-save mr-2"></i>Save Privacy Settings
                        </button>
                    </div>
                </form>
            </div>

            <!-- Account Tab -->
            <div class="tab-content" id="account-tab">
                <div class="form-section form-fade-in">
                    <div class="form-section-header">
                        <div class="form-section-icon bg-gradient-to-br from-purple-500 to-purple-600">
                            <i class="fas fa-user-cog text-white text-sm"></i>
                        </div>
                        <div>
                            <h3 class="form-section-title">Account Settings</h3>
                            <p class="form-section-subtitle">Manage your account preferences</p>
                        </div>
                    </div>

                    <div class="space-y-6">
                        <!-- Language & Region -->
                        <div class="bg-gray-50 rounded-xl p-6">
                            <h4 class="font-bold text-harrier-dark mb-4">Language & Region</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div class="form-group">
                                    <label for="{{ user_prefs_form.preferred_language.id_for_label }}" class="form-label">
                                        Preferred Language
                                    </label>
                                    {{ user_prefs_form.preferred_language }}
                                </div>
                                <div class="form-group">
                                    <label for="{{ user_prefs_form.timezone.id_for_label }}" class="form-label">
                                        Timezone
                                    </label>
                                    {{ user_prefs_form.timezone }}
                                </div>
                            </div>
                        </div>

                        <!-- Security Actions -->
                        <div class="bg-gray-50 rounded-xl p-6">
                            <h4 class="font-bold text-harrier-dark mb-4">Security</h4>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between p-4 bg-white rounded-lg border">
                                    <div>
                                        <h5 class="font-medium text-harrier-dark">Password</h5>
                                        <p class="text-sm text-gray-600">Last changed: {{ user.last_login|date:"M d, Y"|default:"Never" }}</p>
                                    </div>
                                    <button type="button" class="form-button-secondary" onclick="openPasswordModal()">
                                        <i class="fas fa-key mr-2"></i>Change Password
                                    </button>
                                </div>

                                <div class="flex items-center justify-between p-4 bg-white rounded-lg border">
                                    <div>
                                        <h5 class="font-medium text-harrier-dark">Two-Factor Authentication</h5>
                                        <p class="text-sm text-gray-600">Add extra security to your account</p>
                                    </div>
                                    <button type="button" class="form-button-secondary">
                                        <i class="fas fa-mobile-alt mr-2"></i>Setup 2FA
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {% if user.role == 'vendor' and vendor_prefs_form %}
            <!-- Business Tab -->
            <div class="tab-content" id="business-tab">
                <form method="post" id="businessForm" class="space-y-8">
                    {% csrf_token %}
                    <input type="hidden" name="form_type" value="preferences">

                    <div class="form-section form-fade-in">
                        <div class="form-section-header">
                            <div class="form-section-icon bg-gradient-to-br from-orange-500 to-orange-600">
                                <i class="fas fa-building text-white text-sm"></i>
                            </div>
                            <div>
                                <h3 class="form-section-title">Business Settings</h3>
                                <p class="form-section-subtitle">Configure your business preferences</p>
                            </div>
                        </div>

                        <div class="space-y-6">
                            <!-- Profile Settings -->
                            <div class="bg-gray-50 rounded-xl p-6">
                                <h4 class="font-bold text-harrier-dark mb-4">Profile Settings</h4>
                                <div class="space-y-4">
                                    <div class="form-group-inline">
                                        {{ vendor_prefs_form.public_profile }}
                                        <label for="{{ vendor_prefs_form.public_profile.id_for_label }}" class="form-label mb-0">
                                            Public Profile
                                        </label>
                                    </div>
                                    <p class="text-sm text-gray-600 ml-9">Make your business profile visible to the public</p>

                                    <div class="form-group-inline">
                                        {{ vendor_prefs_form.show_contact }}
                                        <label for="{{ vendor_prefs_form.show_contact.id_for_label }}" class="form-label mb-0">
                                            Show Contact Information
                                        </label>
                                    </div>
                                    <p class="text-sm text-gray-600 ml-9">Display your contact details on your profile</p>

                                    <div class="form-group-inline">
                                        {{ vendor_prefs_form.show_business_hours }}
                                        <label for="{{ vendor_prefs_form.show_business_hours.id_for_label }}" class="form-label mb-0">
                                            Show Business Hours
                                        </label>
                                    </div>
                                    <p class="text-sm text-gray-600 ml-9">Display your operating hours to customers</p>
                                </div>
                            </div>

                            <!-- Customer Interaction -->
                            <div class="bg-gray-50 rounded-xl p-6">
                                <h4 class="font-bold text-harrier-dark mb-4">Customer Interaction</h4>
                                <div class="space-y-4">
                                    <div class="form-group-inline">
                                        {{ vendor_prefs_form.allow_direct_messages }}
                                        <label for="{{ vendor_prefs_form.allow_direct_messages.id_for_label }}" class="form-label mb-0">
                                            Allow Direct Messages
                                        </label>
                                    </div>
                                    <p class="text-sm text-gray-600 ml-9">Let customers send you direct messages</p>

                                    <div class="form-group-inline">
                                        {{ vendor_prefs_form.auto_approve_inquiries }}
                                        <label for="{{ vendor_prefs_form.auto_approve_inquiries.id_for_label }}" class="form-label mb-0">
                                            Auto-approve Inquiries
                                        </label>
                                    </div>
                                    <p class="text-sm text-gray-600 ml-9">Automatically approve customer inquiries</p>
                                </div>
                            </div>

                            <!-- Auto-Response Settings -->
                            <div class="bg-gray-50 rounded-xl p-6">
                                <h4 class="font-bold text-harrier-dark mb-4">Auto-Response</h4>
                                <div class="space-y-4">
                                    <div class="form-group-inline">
                                        {{ vendor_prefs_form.auto_response_enabled }}
                                        <label for="{{ vendor_prefs_form.auto_response_enabled.id_for_label }}" class="form-label mb-0">
                                            Enable Auto-Response
                                        </label>
                                    </div>
                                    <p class="text-sm text-gray-600 ml-9">Automatically respond to customer inquiries</p>

                                    <div class="form-group" id="autoResponseSettings" style="display: none;">
                                        <label for="{{ vendor_prefs_form.auto_response_message.id_for_label }}" class="form-label">
                                            Auto-Response Message
                                        </label>
                                        {{ vendor_prefs_form.auto_response_message }}
                                        <div class="character-counter">
                                            <span id="autoResponseCount">0</span>/500 characters
                                        </div>

                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                                            <div class="form-group">
                                                <label for="{{ vendor_prefs_form.auto_response_delay_minutes.id_for_label }}" class="form-label">
                                                    Response Delay (minutes)
                                                </label>
                                                {{ vendor_prefs_form.auto_response_delay_minutes }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end">
                        <button type="submit" class="form-button-primary">
                            <i class="fas fa-save mr-2"></i>Save Business Settings
                        </button>
                    </div>
                </form>
            </div>

            <!-- Business Hours Tab -->
            <div class="tab-content" id="hours-tab">
                {% if business_hours_form %}
                <form method="post" id="businessHoursForm" class="space-y-8">
                    {% csrf_token %}
                    <input type="hidden" name="form_type" value="business_hours">

                    <div class="form-section form-fade-in">
                        <div class="form-section-header">
                            <div class="form-section-icon bg-gradient-to-br from-indigo-500 to-indigo-600">
                                <i class="fas fa-clock text-white text-sm"></i>
                            </div>
                            <div>
                                <h3 class="form-section-title">Business Hours</h3>
                                <p class="form-section-subtitle">Set your operating hours for each day</p>
                            </div>
                        </div>

                        <div class="space-y-4">
                            {% for day_code, day_name in business_hours_form.DAYS_OF_WEEK %}
                            <div class="bg-gray-50 rounded-xl p-6">
                                <div class="flex items-center justify-between mb-4">
                                    <h4 class="font-bold text-harrier-dark">{{ day_name }}</h4>
                                    <div class="form-group-inline">
                                        {{ business_hours_form|get_item:"operates_"|add:day_code }}
                                        <label class="form-label mb-0">Open</label>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4" id="hours-{{ day_code }}" style="display: none;">
                                    <div class="form-group">
                                        <label class="form-label">Opening Time</label>
                                        {{ business_hours_form|get_item:day_code|add:"_open" }}
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">Closing Time</label>
                                        {{ business_hours_form|get_item:day_code|add:"_close" }}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                    <div class="flex justify-end">
                        <button type="submit" class="form-button-primary">
                            <i class="fas fa-save mr-2"></i>Save Business Hours
                        </button>
                    </div>
                </form>
                {% endif %}
            </div>
            {% endif %}
        </div>

        <!-- Settings Sidebar -->
        <div class="space-y-6">
            <!-- Quick Settings -->
            <div class="dashboard-card">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-heading font-bold text-harrier-dark">Quick Settings</h3>
                </div>
                <div class="p-6 space-y-4">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Language</span>
                        <span class="text-sm font-medium text-harrier-dark">{{ user.get_preferred_language_display }}</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Timezone</span>
                        <span class="text-sm font-medium text-harrier-dark">{{ user.timezone }}</span>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">Profile Visibility</span>
                        <span class="text-sm font-medium text-harrier-dark">{{ user.get_profile_visibility_display }}</span>
                    </div>
                </div>
            </div>

            <!-- Account Actions -->
            <div class="dashboard-card">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-heading font-bold text-harrier-dark">Account Actions</h3>
                </div>
                <div class="p-6 space-y-3">
                    <button type="button" class="w-full flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors text-left" onclick="exportAccountData()">
                        <div class="flex items-center">
                            <i class="fas fa-download text-blue-500 mr-3"></i>
                            <span class="text-sm font-medium text-harrier-dark">Export Data</span>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </button>
                    
                    <button type="button" class="w-full flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors text-left" onclick="switchTab('account')">
                        <div class="flex items-center">
                            <i class="fas fa-key text-purple-500 mr-3"></i>
                            <span class="text-sm font-medium text-harrier-dark">Change Password</span>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </button>
                    
                    <button type="button" class="w-full flex items-center justify-between p-3 rounded-lg hover:bg-red-50 transition-colors text-left" onclick="confirmAccountDeletion()">
                        <div class="flex items-center">
                            <i class="fas fa-trash text-red-500 mr-3"></i>
                            <span class="text-sm font-medium text-red-600">Delete Account</span>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block extra_js %}
<script>
// Settings page specific JavaScript
document.addEventListener('DOMContentLoaded', function() {
    initializeTabs();
    initializeSettingsForms();
});

function initializeSettingsForms() {
    // Handle form submissions with AJAX
    const forms = document.querySelectorAll('form[id$="Form"]');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            submitSettingsForm(this);
        });
    });

    // Initialize auto-response toggle
    const autoResponseToggle = document.querySelector('input[name="auto_response_enabled"]');
    const autoResponseSettings = document.getElementById('autoResponseSettings');

    if (autoResponseToggle && autoResponseSettings) {
        function toggleAutoResponse() {
            if (autoResponseToggle.checked) {
                autoResponseSettings.style.display = 'block';
                autoResponseSettings.classList.add('form-fade-in');
            } else {
                autoResponseSettings.style.display = 'none';
            }
        }

        autoResponseToggle.addEventListener('change', toggleAutoResponse);
        toggleAutoResponse(); // Initial state
    }

    // Initialize business hours toggles
    const dayToggles = document.querySelectorAll('input[data-day]');
    dayToggles.forEach(toggle => {
        const day = toggle.dataset.day;
        const hoursSection = document.getElementById(`hours-${day}`);

        if (hoursSection) {
            function toggleHours() {
                if (toggle.checked) {
                    hoursSection.style.display = 'grid';
                    hoursSection.classList.add('form-fade-in');
                } else {
                    hoursSection.style.display = 'none';
                }
            }

            toggle.addEventListener('change', toggleHours);
            toggleHours(); // Initial state
        }
    });

    // Character counter for auto-response message
    const autoResponseTextarea = document.querySelector('textarea[name="auto_response_message"]');
    const autoResponseCounter = document.getElementById('autoResponseCount');

    if (autoResponseTextarea && autoResponseCounter) {
        function updateCounter() {
            const count = autoResponseTextarea.value.length;
            autoResponseCounter.textContent = count;

            const counterContainer = autoResponseCounter.parentElement;
            counterContainer.classList.remove('success', 'warning', 'error');

            if (count <= 400) {
                counterContainer.classList.add('success');
            } else if (count <= 500) {
                counterContainer.classList.add('warning');
            } else {
                counterContainer.classList.add('error');
            }
        }

        autoResponseTextarea.addEventListener('input', updateCounter);
        updateCounter(); // Initial count
    }
}

function submitSettingsForm(form) {
    const formData = new FormData(form);
    const submitButton = form.querySelector('button[type="submit"]');
    
    // Show loading state
    const originalText = submitButton.innerHTML;
    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Saving...';
    submitButton.disabled = true;
    
    fetch(window.location.href, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(data.message, 'success');
        } else {
            showToast(data.message || 'Error saving settings', 'error');
        }
    })
    .catch(error => {
        showToast('Network error. Please try again.', 'error');
    })
    .finally(() => {
        submitButton.innerHTML = originalText;
        submitButton.disabled = false;
    });
}

function exportAccountData() {
    showToast('Preparing account data export...', 'info');
    // Implementation for data export
}

function confirmAccountDeletion() {
    if (confirm('⚠️ Are you absolutely sure you want to delete your account?\n\nThis action cannot be undone.')) {
        showToast('Account deletion process initiated.', 'warning');
    }
}
</script>
{% endblock %}
