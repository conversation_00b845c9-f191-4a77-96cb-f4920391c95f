{% extends 'base_admin_dashboard.html' %}
{% load static %}

{% block page_title %}Spare Shop Management{% endblock %}
{% block page_description %}Manage spare parts inventory, suppliers, and M-Pesa integration{% endblock %}

{% block dashboard_content %}
<div class="space-y-8">
    <!-- Spare Shop Header -->
    <div class="flex justify-between items-center animate-fade-in-up" style="animation-delay: 0.1s;">
        <div>
            <h2 class="text-3xl font-bold text-harrier-dark font-montserrat">Spare Shop Management</h2>
            <p class="text-gray-600 mt-1 font-raleway">Manage spare parts inventory, suppliers, and M-Pesa integration</p>
        </div>
        
        <div class="flex space-x-3">
            <button class="btn-admin-secondary text-sm">
                <i class="fas fa-filter mr-2"></i>Filter Parts
            </button>
            <button class="btn-admin-primary text-sm">
                <i class="fas fa-plus mr-2"></i>Add Spare Part
            </button>
        </div>
    </div>

    <!-- Inventory Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 animate-fade-in-up" style="animation-delay: 0.2s;">
        <div class="admin-stat-card text-center">
            <div class="admin-stat-value text-amber-600">{{ total_parts|default:0 }}</div>
            <div class="admin-stat-label">Total Parts</div>
        </div>
        <div class="admin-stat-card text-center">
            <div class="admin-stat-value text-green-600">{{ in_stock_parts|default:0 }}</div>
            <div class="admin-stat-label">In Stock</div>
        </div>
        <div class="admin-stat-card text-center">
            <div class="admin-stat-value text-orange-600">{{ low_stock_parts|default:0 }}</div>
            <div class="admin-stat-label">Low Stock</div>
        </div>
        <div class="admin-stat-card text-center">
            <div class="admin-stat-value text-red-600">{{ out_of_stock_parts|default:0 }}</div>
            <div class="admin-stat-label">Out of Stock</div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 animate-fade-in-up" style="animation-delay: 0.3s;">
        <div class="quick-action-card">
            <div class="flex items-center">
                <div class="w-14 h-14 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center">
                    <i class="fas fa-mobile-alt text-white text-xl"></i>
                </div>
                <div class="ml-4">
                    <h4 class="font-semibold text-harrier-dark font-raleway">M-Pesa Integration</h4>
                    <p class="text-sm text-gray-600">Configure payment settings</p>
                </div>
            </div>
        </div>

        <div class="quick-action-card">
            <div class="flex items-center">
                <div class="w-14 h-14 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                    <i class="fas fa-truck text-white text-xl"></i>
                </div>
                <div class="ml-4">
                    <h4 class="font-semibold text-harrier-dark font-raleway">Supplier Management</h4>
                    <p class="text-sm text-gray-600">Manage supplier relationships</p>
                </div>
            </div>
        </div>

        <div class="quick-action-card">
            <div class="flex items-center">
                <div class="w-14 h-14 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center">
                    <i class="fas fa-barcode text-white text-xl"></i>
                </div>
                <div class="ml-4">
                    <h4 class="font-semibold text-harrier-dark font-raleway">Barcode/SKU Tracking</h4>
                    <p class="text-sm text-gray-600">Track inventory with barcodes</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Spare Parts Inventory -->
    <div class="admin-card animate-fade-in-up" style="animation-delay: 0.4s;">
        <div class="p-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-gradient-to-br from-amber-500 to-amber-600 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-cogs text-white"></i>
                    </div>
                    <h3 class="text-lg font-bold text-harrier-dark font-montserrat">Spare Parts Inventory</h3>
                </div>
                <div class="flex space-x-2">
                    <select class="text-sm border border-gray-300 rounded-lg px-3 py-1 focus:ring-harrier-red focus:border-harrier-red">
                        <option value="">All Categories</option>
                        {% for category in categories %}
                        <option value="{{ category.id }}" {% if current_category == category.id|stringformat:"s" %}selected{% endif %}>
                            {{ category.name }}
                        </option>
                        {% endfor %}
                    </select>
                    <button class="text-sm text-gray-600 hover:text-harrier-red transition-colors">
                        <i class="fas fa-download mr-1"></i>Export
                    </button>
                </div>
            </div>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider font-montserrat">
                            Part Details
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider font-montserrat">
                            Category
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider font-montserrat">
                            Stock Status
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider font-montserrat">
                            Price
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider font-montserrat">
                            Supplier
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider font-montserrat">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% if spare_parts %}
                        {% for part in spare_parts %}
                        <tr class="hover:bg-gray-50 transition-colors duration-200">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    {% if part.image %}
                                        <img src="{{ part.image.url }}" alt="{{ part.name }}" class="w-12 h-12 object-cover rounded-lg border-2 border-gray-200 mr-4">
                                    {% else %}
                                        <div class="w-12 h-12 bg-gradient-to-br from-amber-200 to-amber-300 rounded-lg flex items-center justify-center mr-4">
                                            <i class="fas fa-cogs text-amber-600 text-lg"></i>
                                        </div>
                                    {% endif %}
                                    <div>
                                        <div class="text-sm font-medium text-harrier-dark font-raleway">{{ part.name }}</div>
                                        <div class="text-sm text-gray-500">{{ part.part_number }}</div>
                                        {% if part.sku %}
                                        <div class="text-xs text-gray-600">
                                            <i class="fas fa-barcode mr-1"></i>{{ part.sku }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 font-montserrat">
                                    {{ part.category_new.name|default:part.category|default:"Uncategorized" }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    {% if part.stock_quantity > 10 %}
                                        <div class="w-3 h-3 bg-green-400 rounded-full mr-2"></div>
                                        <span class="text-sm text-green-800 font-medium">In Stock</span>
                                    {% elif part.stock_quantity > 0 %}
                                        <div class="w-3 h-3 bg-orange-400 rounded-full mr-2"></div>
                                        <span class="text-sm text-orange-800 font-medium">Low Stock</span>
                                    {% else %}
                                        <div class="w-3 h-3 bg-red-400 rounded-full mr-2"></div>
                                        <span class="text-sm text-red-800 font-medium">Out of Stock</span>
                                    {% endif %}
                                </div>
                                <div class="text-xs text-gray-500 mt-1">{{ part.stock_quantity }} units</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-semibold text-harrier-dark">KSh {{ part.price|floatformat:0 }}</div>
                                {% if part.cost_price %}
                                <div class="text-xs text-gray-500">Cost: KSh {{ part.cost_price|floatformat:0 }}</div>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% if part.supplier %}
                                <div class="text-sm text-gray-900 font-raleway">{{ part.supplier.name }}</div>
                                <div class="text-xs text-gray-500">{{ part.supplier.contact_email }}</div>
                                {% else %}
                                <span class="text-sm text-gray-500">No supplier</span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <button class="text-blue-600 hover:text-blue-800 transition-colors" title="Edit Part">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="text-green-600 hover:text-green-800 transition-colors" title="Restock">
                                        <i class="fas fa-plus-circle"></i>
                                    </button>
                                    <button class="text-purple-600 hover:text-purple-800 transition-colors" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="text-red-600 hover:text-red-800 transition-colors" title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    {% else %}
                        <tr>
                            <td colspan="6" class="px-6 py-12 text-center">
                                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-cogs text-gray-400 text-2xl"></i>
                                </div>
                                <h4 class="text-lg font-semibold text-gray-900 mb-2 font-montserrat">No spare parts yet</h4>
                                <p class="text-gray-600 font-raleway">Add spare parts to your inventory to get started.</p>
                                <button class="mt-4 btn-admin-primary">
                                    <i class="fas fa-plus mr-2"></i>Add First Spare Part
                                </button>
                            </td>
                        </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Recent Orders -->
    <div class="admin-card animate-fade-in-up" style="animation-delay: 0.5s;">
        <div class="p-6 border-b border-gray-200">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-shopping-cart text-white"></i>
                </div>
                <h3 class="text-lg font-bold text-harrier-dark font-montserrat">Recent Spare Parts Orders</h3>
            </div>
        </div>
        
        <div class="p-6">
            {% if recent_orders %}
                <div class="space-y-4">
                    {% for order in recent_orders %}
                    <div class="order-card border border-gray-200 rounded-lg p-4 hover:border-green-300 hover:shadow-md transition-all duration-300">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center text-white font-semibold">
                                    {{ order.customer.first_name|first|default:order.customer.username|first|upper }}
                                </div>
                                <div>
                                    <h4 class="font-semibold text-harrier-dark font-raleway">Order #{{ order.id|stringformat:"05d" }}</h4>
                                    <p class="text-sm text-gray-600">{{ order.customer.get_full_name|default:order.customer.username }}</p>
                                    <p class="text-xs text-gray-500">{{ order.created_at|timesince }} ago</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="text-lg font-semibold text-harrier-red">KSh {{ order.total_amount|floatformat:0 }}</div>
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                    {% if order.status == 'pending' %}bg-yellow-100 text-yellow-800
                                    {% elif order.status == 'processing' %}bg-blue-100 text-blue-800
                                    {% elif order.status == 'completed' %}bg-green-100 text-green-800
                                    {% else %}bg-gray-100 text-gray-800{% endif %}">
                                    {{ order.get_status_display }}
                                </span>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-8">
                    <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-shopping-cart text-gray-400 text-2xl"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2 font-montserrat">No recent orders</h4>
                    <p class="text-gray-600 font-raleway">Spare parts orders will appear here when customers make purchases.</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
{{ block.super }}
<style>
    /* Spare Shop specific styles */
    .quick-action-card {
        @apply bg-white rounded-xl p-6 border border-gray-200 hover:border-amber-300 hover:shadow-lg transition-all duration-300;
        transform: translateY(0);
    }

    .quick-action-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 10px 25px rgba(245, 158, 11, 0.1);
    }

    /* Stock status indicators */
    .w-3.h-3 {
        animation: pulse 2s infinite;
    }

    /* Order card animations */
    .order-card {
        transform: translateY(0);
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .order-card:hover {
        transform: translateY(-2px);
    }

    /* Table row hover effects */
    tbody tr:hover {
        background-color: #f9fafb;
        transform: translateX(2px);
        transition: all 0.2s ease;
    }

    /* Mobile responsiveness */
    @media (max-width: 768px) {
        .quick-action-card {
            padding: 1rem;
        }
        
        .admin-stat-card {
            padding: 1rem;
        }
    }
</style>
{% endblock %}
