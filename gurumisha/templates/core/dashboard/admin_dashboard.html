{% extends 'base_admin_dashboard.html' %}
{% load static %}

{% block dashboard_title %}Admin Dashboard{% endblock %}
{% block page_title %}System Administration{% endblock %}
{% block page_description %}Manage users, content, and system settings{% endblock %}

{% comment %} {% block sidebar_nav %}
    <li>
        <a href="{% url 'core:dashboard' %}" class="dashboard-nav-link {% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}">
            <i class="fas fa-tachometer-alt"></i>
            <span>Dashboard</span>
        </a>
    </li>
    <li>
        <a href="{% url 'core:admin_users' %}" class="dashboard-nav-link">
            <i class="fas fa-users"></i>
            <span>User Management</span>
        </a>
    </li>
    <li>
        <a href="{% url 'core:admin_vendors' %}" class="dashboard-nav-link">
            <i class="fas fa-store"></i>
            <span>Vendor Management</span>
        </a>
    </li>
    <li>
        <a href="{% url 'core:admin_listings' %}" class="dashboard-nav-link">
            <i class="fas fa-car"></i>
            <span>Car Listings</span>
        </a>
    </li>
    <li>
        <a href="#" class="dashboard-nav-link">
            <i class="fas fa-ship"></i>
            <span>Import Requests</span>
        </a>
    </li>
    <li>
        <a href="{% url 'core:admin_spare_parts' %}" class="dashboard-nav-link">
            <i class="fas fa-cogs"></i>
            <span>Spare Parts</span>
        </a>
    </li>
    <li>
        <a href="#" class="dashboard-nav-link">
            <i class="fas fa-envelope"></i>
            <span>Inquiries</span>
        </a>
    </li>
    <li>
        <a href="#" class="dashboard-nav-link">
            <i class="fas fa-newspaper"></i>
            <span>Content Management</span>
        </a>
    </li>
    <li>
        <a href="{% url 'core:admin_analytics' %}" class="dashboard-nav-link">
            <i class="fas fa-chart-bar"></i>
            <span>Analytics</span>
        </a>
    </li>
    <li>
        <a href="#" class="dashboard-nav-link">
            <i class="fas fa-cog"></i>
            <span>System Settings</span>
        </a>
    </li>
{% endblock %} {% endcomment %}

{% block sidebar_stats %}
    <div class="mt-6 bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <h3 class="text-sm font-semibold text-harrier-dark mb-4 uppercase tracking-wide">System Stats</h3>
        <div class="space-y-4">
            <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600">Total Users</span>
                <span class="text-lg font-bold text-harrier-red">{{ total_users|default:0 }}</span>
            </div>
            <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600">Active Vendors</span>
                <span class="text-lg font-bold text-harrier-red">{{ active_vendors|default:0 }}</span>
            </div>
            <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600">Pending Approvals</span>
                <span class="text-lg font-bold text-orange-600">{{ pending_approvals|default:0 }}</span>
            </div>
            <div class="flex items-center justify-between">
                <span class="text-sm text-gray-600">Total Listings</span>
                <span class="text-lg font-bold text-harrier-red">{{ total_listings|default:0 }}</span>
            </div>
        </div>
    </div>

    <!-- System Health -->
    <div class="mt-4 bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <h3 class="text-sm font-semibold text-harrier-dark mb-3 uppercase tracking-wide">System Health</h3>
        <div class="flex items-center">
            <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
            <span class="text-sm text-green-700 font-medium">All Systems Operational</span>
        </div>
    </div>
{% endblock %}

{% block dashboard_content %}
<div class="space-y-8">
    <!-- Welcome Section with Entrance Animation -->
    <div class="welcome-section animate-fade-in-up" style="animation-delay: 0.1s;">
        <div class="bg-gradient-to-r from-harrier-red to-harrier-dark rounded-xl p-8 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-3xl font-bold font-montserrat mb-2">Welcome back, {{ user.get_full_name|default:user.username }}!</h2>
                    <p class="text-gray-200 font-raleway">Here's what's happening with your automotive marketplace today.</p>
                </div>
                <div class="hidden md:block">
                    <div class="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm">
                        <i class="fas fa-tachometer-alt text-white text-3xl"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- System Overview Stats with Lazy Loading -->
    <div class="lazy-content-container animate-fade-in-up"
         style="animation-delay: 0.2s;"
         hx-get="{% url 'core:admin_quick_actions' %}"
         hx-trigger="revealed"
         hx-swap="outerHTML">
        <!-- Loading Skeleton for Stats -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
            {% for i in "1234" %}
            <div class="admin-stat-card text-center animate-pulse">
                <div class="h-8 bg-gray-200 rounded mb-2"></div>
                <div class="h-4 bg-gray-200 rounded w-3/4 mx-auto"></div>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Quick Actions Grid -->
    <div class="animate-fade-in-up" style="animation-delay: 0.3s;">
        <h3 class="text-xl font-bold text-harrier-dark mb-6 font-montserrat">Quick Actions</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <a href="{% url 'core:admin_users' %}" class="quick-action-card group">
                <div class="flex items-center">
                    <div class="flex-shrink-0 w-14 h-14 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-users text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <h4 class="font-semibold text-harrier-dark font-raleway">Manage Users</h4>
                        <p class="text-sm text-gray-600">{{ total_users|default:0 }} total users</p>
                    </div>
                </div>
            </a>

            <a href="{% url 'core:admin_listings' %}" class="quick-action-card group">
                <div class="flex items-center">
                    <div class="flex-shrink-0 w-14 h-14 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-check-circle text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <h4 class="font-semibold text-harrier-dark font-raleway">Pending Approvals</h4>
                        <p class="text-sm text-gray-600">{{ pending_approvals|default:0 }} items</p>
                    </div>
                </div>
            </a>

            <a href="{% url 'core:admin_analytics' %}" class="quick-action-card group">
                <div class="flex items-center">
                    <div class="flex-shrink-0 w-14 h-14 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-chart-line text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <h4 class="font-semibold text-harrier-dark font-raleway">Analytics</h4>
                        <p class="text-sm text-gray-600">View reports</p>
                    </div>
                </div>
            </a>

            <a href="#" class="quick-action-card group">
                <div class="flex items-center">
                    <div class="flex-shrink-0 w-14 h-14 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-cog text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <h4 class="font-semibold text-harrier-dark font-raleway">Settings</h4>
                        <p class="text-sm text-gray-600">System config</p>
                    </div>
                </div>
            </a>
        </div>
    </div>

    <!-- Main Dashboard Content Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Recent User Registrations -->
        <div class="admin-card animate-fade-in-up" style="animation-delay: 0.4s;">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-users text-white"></i>
                        </div>
                        <h3 class="text-lg font-bold text-harrier-dark font-montserrat">Recent User Registrations</h3>
                    </div>
                    <a href="{% url 'core:admin_users' %}" class="text-sm text-harrier-red hover:text-harrier-dark font-medium transition-colors">View All</a>
                </div>
            </div>
            <div class="p-6">
                {% if recent_users %}
                    <div class="space-y-4">
                        {% for user in recent_users %}
                            <div class="user-card border border-gray-200 rounded-lg p-4 hover:border-harrier-red hover:shadow-md transition-all duration-300">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="w-12 h-12 bg-gradient-to-br from-harrier-red to-harrier-dark rounded-full flex items-center justify-center text-white font-semibold text-sm mr-4">
                                            {{ user.first_name|first|default:user.username|first|upper }}
                                        </div>
                                        <div>
                                            <h4 class="font-semibold text-harrier-dark font-raleway">{{ user.first_name|default:user.username }} {{ user.last_name }}</h4>
                                            <p class="text-sm text-gray-600">{{ user.email }}</p>
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium mt-1 font-montserrat
                                                {% if user.role == 'vendor' %}bg-blue-100 text-blue-800
                                                {% elif user.role == 'admin' %}bg-purple-100 text-purple-800
                                                {% else %}bg-gray-100 text-gray-800{% endif %}">
                                                {{ user.get_role_display }}
                                            </span>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-sm text-gray-600 font-raleway">{{ user.date_joined|date:"M d" }}</p>
                                        <div class="flex space-x-2 mt-2">
                                            <button class="text-harrier-red hover:text-harrier-dark text-sm font-medium transition-colors">
                                                View
                                            </button>
                                            <span class="text-gray-300">|</span>
                                            <button class="text-harrier-red hover:text-harrier-dark text-sm font-medium transition-colors">
                                                Edit
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-12">
                        <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-users text-gray-400 text-2xl"></i>
                        </div>
                        <h4 class="text-lg font-semibold text-gray-900 mb-2 font-montserrat">No recent registrations</h4>
                        <p class="text-gray-600 font-raleway">New user registrations will appear here.</p>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Pending Approvals -->
        <div class="admin-card animate-fade-in-up" style="animation-delay: 0.5s;">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-check-circle text-white"></i>
                        </div>
                        <h3 class="text-lg font-bold text-harrier-dark font-montserrat">Pending Approvals</h3>
                    </div>
                    <a href="{% url 'core:admin_listings' %}" class="text-sm text-harrier-red hover:text-harrier-dark font-medium transition-colors">View All</a>
                </div>
            </div>
            <div class="p-6">
                {% if pending_cars %}
                    <div class="space-y-4">
                        {% for car in pending_cars %}
                            <div class="approval-card border border-gray-200 rounded-lg p-4 hover:border-harrier-red hover:shadow-md transition-all duration-300">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        {% if car.main_image %}
                                            <img src="{{ car.main_image.url }}" alt="{{ car.title }}" class="w-16 h-16 object-cover rounded-lg mr-4 border-2 border-gray-200">
                                        {% else %}
                                            <div class="w-16 h-16 bg-gradient-to-br from-gray-200 to-gray-300 rounded-lg mr-4 flex items-center justify-center">
                                                <i class="fas fa-car text-gray-500 text-xl"></i>
                                            </div>
                                        {% endif %}
                                        <div>
                                            <h4 class="font-semibold text-harrier-dark font-raleway">{{ car.title }}</h4>
                                            <p class="text-sm text-gray-600">By: {{ car.vendor.company_name }}</p>
                                            <p class="text-sm font-semibold text-harrier-red">KSh {{ car.price|floatformat:0 }}</p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-sm text-gray-600 font-raleway">{{ car.created_at|date:"M d" }}</p>
                                        <div class="flex space-x-2 mt-2">
                                            <button class="bg-green-100 text-green-700 hover:bg-green-200 px-3 py-1 rounded-full text-sm font-medium transition-colors">
                                                <i class="fas fa-check mr-1"></i>Approve
                                            </button>
                                            <button class="bg-red-100 text-red-700 hover:bg-red-200 px-3 py-1 rounded-full text-sm font-medium transition-colors">
                                                <i class="fas fa-times mr-1"></i>Reject
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-12">
                        <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-check-circle text-gray-400 text-2xl"></i>
                        </div>
                        <h4 class="text-lg font-semibold text-gray-900 mb-2 font-montserrat">All caught up!</h4>
                        <p class="text-gray-600 font-raleway">No pending approvals at the moment.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- System Activity -->
    <div class="mt-8 admin-card animate-fade-in-up" style="animation-delay: 0.6s;">
        <div class="p-6 border-b border-gray-200">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-history text-white"></i>
                </div>
                <h3 class="text-lg font-bold text-harrier-dark font-montserrat">Recent System Activity</h3>
            </div>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                <div class="activity-item flex items-center justify-between py-4 border-b border-gray-100 hover:bg-gray-50 rounded-lg px-4 transition-colors duration-200">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-user-plus text-white text-sm"></i>
                        </div>
                        <div>
                            <p class="text-sm font-semibold text-harrier-dark font-raleway">New user registration</p>
                            <p class="text-xs text-gray-600">John Doe registered as a customer</p>
                        </div>
                    </div>
                    <span class="text-xs text-gray-500 font-montserrat">2 hours ago</span>
                </div>

                <div class="activity-item flex items-center justify-between py-4 border-b border-gray-100 hover:bg-gray-50 rounded-lg px-4 transition-colors duration-200">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-car text-white text-sm"></i>
                        </div>
                        <div>
                            <p class="text-sm font-semibold text-harrier-dark font-raleway">New car listing submitted</p>
                            <p class="text-xs text-gray-600">2020 Toyota Camry pending approval</p>
                        </div>
                    </div>
                    <span class="text-xs text-gray-500 font-montserrat">4 hours ago</span>
                </div>

                <div class="activity-item flex items-center justify-between py-4 border-b border-gray-100 hover:bg-gray-50 rounded-lg px-4 transition-colors duration-200">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-store text-white text-sm"></i>
                        </div>
                        <div>
                            <p class="text-sm font-semibold text-harrier-dark font-raleway">Vendor application approved</p>
                            <p class="text-xs text-gray-600">ABC Motors is now a verified vendor</p>
                        </div>
                    </div>
                    <span class="text-xs text-gray-500 font-montserrat">6 hours ago</span>
                </div>

                <div class="activity-item flex items-center justify-between py-4 hover:bg-gray-50 rounded-lg px-4 transition-colors duration-200">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-ship text-white text-sm"></i>
                        </div>
                        <div>
                            <p class="text-sm font-semibold text-harrier-dark font-raleway">Import request submitted</p>
                            <p class="text-xs text-gray-600">Customer requested 2023 Honda Civic import</p>
                        </div>
                    </div>
                    <span class="text-xs text-gray-500 font-montserrat">8 hours ago</span>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
{{ block.super }}
<style>
    /* Enhanced Dashboard Animations with Harrier Design */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .animate-fade-in-up {
        animation: fadeInUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
    }

    /* Welcome Section Styling */
    .welcome-section {
        position: relative;
        overflow: hidden;
    }

    .welcome-section::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 200px;
        height: 200px;
        background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
        border-radius: 50%;
        transform: translate(50%, -50%);
    }

    /* Quick Action Cards */
    .quick-action-card {
        @apply bg-white rounded-xl p-6 border border-gray-200 hover:border-harrier-red hover:shadow-lg transition-all duration-300;
        transform: translateY(0);
    }

    .quick-action-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 10px 25px rgba(220, 38, 38, 0.1);
    }

    /* User and Approval Cards */
    .user-card, .approval-card {
        transform: translateY(0);
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .user-card:hover, .approval-card:hover {
        transform: translateY(-2px);
    }

    /* Activity Items */
    .activity-item {
        transform: translateX(0);
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .activity-item:hover {
        transform: translateX(4px);
    }

    /* Loading Skeleton Animation */
    @keyframes shimmer {
        0% {
            background-position: -200px 0;
        }
        100% {
            background-position: calc(200px + 100%) 0;
        }
    }

    .animate-pulse {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200px 100%;
        animation: shimmer 1.5s infinite;
    }

    /* Enhanced Stat Cards */
    .admin-stat-card {
        @apply bg-white rounded-xl p-6 border border-gray-200 hover:border-harrier-red transition-all duration-300;
        transform: translateY(0);
    }

    .admin-stat-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .admin-stat-value {
        @apply text-3xl font-bold text-harrier-dark mb-2;
        font-family: 'Montserrat', sans-serif;
    }

    .admin-stat-label {
        @apply text-sm text-gray-600 uppercase tracking-wide;
        font-family: 'Raleway', sans-serif;
        letter-spacing: 0.05em;
    }

    /* Mobile Optimizations */
    @media (max-width: 768px) {
        .welcome-section {
            padding: 1.5rem;
        }

        .quick-action-card {
            padding: 1rem;
        }

        .admin-stat-card {
            padding: 1rem;
        }

        .admin-stat-value {
            font-size: 1.5rem;
        }
    }

    /* Touch Optimizations */
    @media (hover: none) and (pointer: coarse) {
        .quick-action-card:active {
            transform: scale(0.98);
        }

        .user-card:active, .approval-card:active {
            transform: scale(0.99);
        }

        .activity-item:active {
            background-color: #f9fafb;
        }
    }

    /* Accessibility Enhancements */
    @media (prefers-reduced-motion: reduce) {
        .animate-fade-in-up {
            animation: none;
        }

        .quick-action-card, .user-card, .approval-card, .activity-item {
            transition: none;
        }
    }
</style>
{% endblock %}
