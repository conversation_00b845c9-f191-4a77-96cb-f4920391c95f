{% extends 'core/dashboard/base_dashboard.html' %}
{% load static %}

{% block title %}My Orders - Dashboard{% endblock %}

{% block dashboard_title %}My Orders{% endblock %}

{% block dashboard_content %}
    <!-- Order Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="stat-card text-center">
            <div class="stat-value">{{ total_orders }}</div>
            <div class="stat-label">Total Orders</div>
        </div>
        <div class="stat-card text-center">
            <div class="stat-value text-green-600">KSh {{ total_spent|floatformat:0 }}</div>
            <div class="stat-label">Total Spent</div>
        </div>
        <div class="stat-card text-center">
            <div class="stat-value text-yellow-600">{{ pending_orders }}</div>
            <div class="stat-label">Pending Orders</div>
        </div>
        <div class="stat-card text-center">
            <div class="stat-value text-blue-600">{{ completed_orders }}</div>
            <div class="stat-label">Completed Orders</div>
        </div>
    </div>

    <!-- Orders List -->
    <div class="dashboard-card">
        <div class="dashboard-card-header">
            <h2 class="dashboard-card-title">Recent Orders</h2>
            <a href="{% url 'core:orders' %}" class="btn-harrier-primary px-4 py-2 rounded-lg text-sm">
                View All Orders
            </a>
        </div>
        
        <div class="dashboard-card-content">
            {% if orders %}
                <div class="space-y-6">
                    {% for order in orders %}
                        <div class="border border-gray-200 rounded-xl p-6 hover:shadow-md transition-shadow">
                            <!-- Order Header -->
                            <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                                <div>
                                    <h3 class="text-lg font-semibold text-harrier-dark">
                                        Order #{{ order.order_number }}
                                    </h3>
                                    <p class="text-sm text-gray-600">
                                        Placed on {{ order.created_at|date:"M d, Y" }} at {{ order.created_at|time:"H:i" }}
                                    </p>
                                </div>
                                <div class="flex items-center space-x-4 mt-2 md:mt-0">
                                    <span class="px-3 py-1 rounded-full text-sm font-medium
                                        {% if order.status == 'pending' %}bg-yellow-100 text-yellow-800
                                        {% elif order.status == 'paid' %}bg-blue-100 text-blue-800
                                        {% elif order.status == 'processing' %}bg-purple-100 text-purple-800
                                        {% elif order.status == 'shipped' %}bg-indigo-100 text-indigo-800
                                        {% elif order.status == 'delivered' %}bg-green-100 text-green-800
                                        {% elif order.status == 'cancelled' %}bg-red-100 text-red-800
                                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                                        {{ order.get_status_display }}
                                    </span>
                                    <span class="text-lg font-bold text-harrier-red">
                                        KSh {{ order.total_amount|floatformat:0 }}
                                    </span>
                                </div>
                            </div>
                            
                            <!-- Order Items -->
                            <div class="space-y-3 mb-4">
                                {% for item in order.items.all|slice:":3" %}
                                    <div class="flex items-center space-x-3">
                                        <div class="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
                                            <i class="fas fa-cog text-gray-400"></i>
                                        </div>
                                        <div class="flex-1">
                                            <p class="font-medium text-harrier-dark">{{ item.part_name }}</p>
                                            <p class="text-sm text-gray-600">
                                                Qty: {{ item.quantity }} × KSh {{ item.unit_price|floatformat:0 }}
                                            </p>
                                        </div>
                                        <p class="font-semibold text-harrier-red">
                                            KSh {{ item.total_price|floatformat:0 }}
                                        </p>
                                    </div>
                                {% endfor %}
                                
                                {% if order.items.count > 3 %}
                                    <p class="text-sm text-gray-500 text-center">
                                        +{{ order.items.count|add:"-3" }} more item{{ order.items.count|add:"-3"|pluralize }}
                                    </p>
                                {% endif %}
                            </div>
                            
                            <!-- Order Actions -->
                            <div class="flex flex-col sm:flex-row gap-3 pt-4 border-t border-gray-200">
                                <a href="{% url 'core:order_detail' order.id %}" 
                                   class="btn-harrier-primary px-4 py-2 rounded-lg text-sm text-center">
                                    View Details
                                </a>
                                
                                {% if order.can_be_cancelled %}
                                    <button onclick="cancelOrder({{ order.id }})"
                                            class="border-2 border-red-500 text-red-500 px-4 py-2 rounded-lg text-sm hover:bg-red-500 hover:text-white transition-colors">
                                        Cancel Order
                                    </button>
                                {% endif %}
                                
                                {% if order.status == 'delivered' %}
                                    <button class="border-2 border-harrier-blue text-harrier-blue px-4 py-2 rounded-lg text-sm hover:bg-harrier-blue hover:text-white transition-colors">
                                        Reorder
                                    </button>
                                {% endif %}
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <!-- Empty State -->
                <div class="text-center py-12">
                    <i class="fas fa-shopping-bag text-gray-300 text-6xl mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-600 mb-2">No orders yet</h3>
                    <p class="text-gray-500 mb-6">You haven't placed any orders for spare parts yet.</p>
                    <a href="{% url 'core:spare_parts' %}" 
                       class="btn-harrier-primary px-6 py-3 rounded-xl font-semibold">
                        Browse Spare Parts
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
{% endblock %}

{% block extra_js %}
<script>
    function cancelOrder(orderId) {
        if (!confirm('Are you sure you want to cancel this order?')) {
            return;
        }
        
        fetch(`/orders/${orderId}/cancel/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': '{{ csrf_token }}',
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage(data.message, 'success');
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showMessage(data.message, 'error');
            }
        })
        .catch(error => {
            showMessage('An error occurred while cancelling the order', 'error');
        });
    }
    
    function showMessage(message, type) {
        const toast = document.createElement('div');
        toast.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg text-white font-semibold ${type === 'success' ? 'bg-green-500' : 'bg-red-500'}`;
        toast.textContent = message;
        
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.remove();
        }, 3000);
    }
</script>
{% endblock %}
