{% extends 'base_admin_dashboard.html' %}
{% load static %}

{% block page_title %}System Settings{% endblock %}
{% block page_description %}Configure system-wide settings and preferences{% endblock %}

{% block dashboard_content %}
<div class="space-y-8">
    <!-- Settings Header -->
    <div class="flex justify-between items-center animate-fade-in-up" style="animation-delay: 0.1s;">
        <div>
            <h2 class="text-3xl font-bold text-harrier-dark font-montserrat">System Settings</h2>
            <p class="text-gray-600 mt-1 font-raleway">Configure system-wide settings and preferences</p>
        </div>
        
        <div class="flex space-x-3">
            <button class="btn-admin-secondary text-sm">
                <i class="fas fa-download mr-2"></i>Export Config
            </button>
            <button class="btn-admin-primary text-sm">
                <i class="fas fa-save mr-2"></i>Save Changes
            </button>
        </div>
    </div>

    <!-- Settings Categories -->
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- Settings Navigation -->
        <div class="lg:col-span-1 animate-fade-in-up" style="animation-delay: 0.2s;">
            <div class="admin-card p-6">
                <h3 class="text-lg font-bold text-harrier-dark mb-4 font-montserrat">Categories</h3>
                <nav class="space-y-2">
                    <a href="#general" class="settings-nav-link active">
                        <i class="fas fa-cog mr-3"></i>General Settings
                    </a>
                    <a href="#email" class="settings-nav-link">
                        <i class="fas fa-envelope mr-3"></i>Email Configuration
                    </a>
                    <a href="#payment" class="settings-nav-link">
                        <i class="fas fa-credit-card mr-3"></i>Payment Settings
                    </a>
                    <a href="#notifications" class="settings-nav-link">
                        <i class="fas fa-bell mr-3"></i>Notifications
                    </a>
                    <a href="#security" class="settings-nav-link">
                        <i class="fas fa-shield-alt mr-3"></i>Security
                    </a>
                    <a href="#maintenance" class="settings-nav-link">
                        <i class="fas fa-tools mr-3"></i>Maintenance
                    </a>
                </nav>
            </div>
        </div>

        <!-- Settings Content -->
        <div class="lg:col-span-3 space-y-6">
            <!-- General Settings -->
            <div id="general" class="admin-card animate-fade-in-up" style="animation-delay: 0.3s;">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-gradient-to-br from-gray-500 to-gray-600 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-cog text-white"></i>
                        </div>
                        <h3 class="text-lg font-bold text-harrier-dark font-montserrat">General Settings</h3>
                    </div>
                </div>
                
                <div class="p-6 space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2 font-raleway">Site Name</label>
                            <input type="text" value="Gurumisha Motors" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-harrier-red focus:border-harrier-red">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2 font-raleway">Site URL</label>
                            <input type="url" value="https://gurumisha.com" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-harrier-red focus:border-harrier-red">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2 font-raleway">Admin Email</label>
                            <input type="email" value="<EMAIL>" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-harrier-red focus:border-harrier-red">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2 font-raleway">Contact Phone</label>
                            <input type="tel" value="+254 700 000 000" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-harrier-red focus:border-harrier-red">
                        </div>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2 font-raleway">Site Description</label>
                        <textarea rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-harrier-red focus:border-harrier-red">Kenya's premier automotive marketplace for buying, selling, and importing vehicles.</textarea>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2 font-raleway">Default Currency</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-harrier-red focus:border-harrier-red">
                                <option value="KES" selected>Kenyan Shilling (KES)</option>
                                <option value="USD">US Dollar (USD)</option>
                                <option value="EUR">Euro (EUR)</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2 font-raleway">Timezone</label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-harrier-red focus:border-harrier-red">
                                <option value="Africa/Nairobi" selected>Africa/Nairobi</option>
                                <option value="UTC">UTC</option>
                                <option value="America/New_York">America/New_York</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Email Configuration -->
            <div id="email" class="admin-card animate-fade-in-up" style="animation-delay: 0.4s;">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-envelope text-white"></i>
                        </div>
                        <h3 class="text-lg font-bold text-harrier-dark font-montserrat">Email Configuration</h3>
                    </div>
                </div>
                
                <div class="p-6 space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2 font-raleway">SMTP Host</label>
                            <input type="text" value="smtp.gmail.com" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-harrier-red focus:border-harrier-red">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2 font-raleway">SMTP Port</label>
                            <input type="number" value="587" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-harrier-red focus:border-harrier-red">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2 font-raleway">SMTP Username</label>
                            <input type="email" value="<EMAIL>" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-harrier-red focus:border-harrier-red">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2 font-raleway">SMTP Password</label>
                            <input type="password" value="••••••••" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-harrier-red focus:border-harrier-red">
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <label class="flex items-center">
                            <input type="checkbox" checked class="rounded border-gray-300 text-harrier-red focus:ring-harrier-red">
                            <span class="ml-2 text-sm text-gray-700 font-raleway">Use TLS Encryption</span>
                        </label>
                        <button class="bg-blue-100 text-blue-700 hover:bg-blue-200 px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                            <i class="fas fa-paper-plane mr-1"></i>Test Email
                        </button>
                    </div>
                </div>
            </div>

            <!-- Payment Settings -->
            <div id="payment" class="admin-card animate-fade-in-up" style="animation-delay: 0.5s;">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-credit-card text-white"></i>
                        </div>
                        <h3 class="text-lg font-bold text-harrier-dark font-montserrat">Payment Settings</h3>
                    </div>
                </div>
                
                <div class="p-6 space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2 font-raleway">M-Pesa Consumer Key</label>
                            <input type="text" value="••••••••••••••••" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-harrier-red focus:border-harrier-red">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2 font-raleway">M-Pesa Consumer Secret</label>
                            <input type="password" value="••••••••••••••••" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-harrier-red focus:border-harrier-red">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2 font-raleway">Business Short Code</label>
                            <input type="text" value="174379" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-harrier-red focus:border-harrier-red">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2 font-raleway">Passkey</label>
                            <input type="password" value="••••••••••••••••" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-harrier-red focus:border-harrier-red">
                        </div>
                    </div>
                    
                    <div class="space-y-4">
                        <label class="flex items-center">
                            <input type="checkbox" checked class="rounded border-gray-300 text-harrier-red focus:ring-harrier-red">
                            <span class="ml-2 text-sm text-gray-700 font-raleway">Enable M-Pesa Payments</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="rounded border-gray-300 text-harrier-red focus:ring-harrier-red">
                            <span class="ml-2 text-sm text-gray-700 font-raleway">Enable PayPal Payments</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="rounded border-gray-300 text-harrier-red focus:ring-harrier-red">
                            <span class="ml-2 text-sm text-gray-700 font-raleway">Enable Bank Transfer</span>
                        </label>
                    </div>
                </div>
            </div>

            <!-- System Status -->
            <div class="admin-card animate-fade-in-up" style="animation-delay: 0.6s;">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-server text-white"></i>
                        </div>
                        <h3 class="text-lg font-bold text-harrier-dark font-montserrat">System Status</h3>
                    </div>
                </div>
                
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="text-center">
                            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-database text-green-600 text-xl"></i>
                            </div>
                            <h4 class="font-semibold text-gray-900 font-raleway">Database</h4>
                            <p class="text-sm text-green-600 font-medium">Online</p>
                        </div>
                        <div class="text-center">
                            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-envelope text-green-600 text-xl"></i>
                            </div>
                            <h4 class="font-semibold text-gray-900 font-raleway">Email Service</h4>
                            <p class="text-sm text-green-600 font-medium">Connected</p>
                        </div>
                        <div class="text-center">
                            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-mobile-alt text-green-600 text-xl"></i>
                            </div>
                            <h4 class="font-semibold text-gray-900 font-raleway">M-Pesa API</h4>
                            <p class="text-sm text-green-600 font-medium">Active</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
{{ block.super }}
<style>
    /* Settings Navigation */
    .settings-nav-link {
        @apply flex items-center w-full px-3 py-2 text-sm font-medium text-gray-700 rounded-lg transition-all duration-200 hover:bg-gray-100 hover:text-harrier-red;
        font-family: 'Raleway', sans-serif;
    }

    .settings-nav-link.active {
        @apply bg-harrier-red text-white;
    }

    .settings-nav-link:hover {
        transform: translateX(4px);
    }

    /* Form Elements */
    input:focus, select:focus, textarea:focus {
        box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
    }

    /* Status indicators */
    .status-indicator {
        transition: all 0.3s ease;
    }

    .status-indicator:hover {
        transform: scale(1.05);
    }

    /* Mobile responsiveness */
    @media (max-width: 1024px) {
        .lg\\:col-span-1 {
            order: 2;
        }
        
        .lg\\:col-span-3 {
            order: 1;
        }
    }
</style>
{% endblock %}
