{% extends 'base_dashboard.html' %}
{% load static %}

{% block dashboard_title %}Spare Parts Inventory{% endblock %}
{% block page_title %}Spare Parts Management{% endblock %}
{% block page_description %}Manage your spare parts inventory, stock levels, and sales{% endblock %}

{% block breadcrumb_items %}
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2 font-raleway">Spare Parts</span>
        </div>
    </li>
{% endblock %}

{% block dashboard_content %}
    <!-- Simple Spare Parts Header -->
    <div class="mb-8 bg-white rounded-xl shadow-lg border border-gray-200/50 p-6">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-harrier-dark font-montserrat">
                    <i class="fas fa-cogs mr-3 text-harrier-red"></i>Spare Parts Inventory
                </h1>
                <p class="text-gray-600 font-raleway">Manage your automotive spare parts</p>
            </div>
            <div class="flex items-center space-x-4">
                <div class="text-center">
                    <div class="text-xl font-bold text-harrier-dark">{{ total_parts|default:0 }}</div>
                    <div class="text-sm text-gray-500">Total Parts</div>
                </div>
                <button class="btn-harrier-primary">
                    <i class="fas fa-plus mr-2"></i>Add Part
                </button>
            </div>
        </div>
    </div>

    <!-- Parts Container -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-200/50 overflow-hidden">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-bold text-harrier-dark">Parts Inventory</h3>
        </div>
        <div class="p-6">
            {% if spare_parts %}
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    {% for part in spare_parts %}
                        <div class="bg-gray-50 rounded-lg border border-gray-200 p-4">
                            <div class="text-center">
                                <i class="fas fa-cog text-4xl text-gray-400 mb-3"></i>
                                <h4 class="font-semibold text-harrier-dark">{{ part.name }}</h4>
                                <p class="text-sm text-gray-600">KSh {{ part.price|floatformat:0 }}</p>
                                <p class="text-xs text-gray-500 mt-2">Stock: {{ part.stock_quantity }}</p>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-12">
                    <i class="fas fa-cogs text-gray-300 text-6xl mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-600 mb-2">No spare parts found</h3>
                    <p class="text-gray-500 mb-6">Start building your inventory by adding spare parts.</p>
                    <button class="btn-harrier-primary px-6 py-3 rounded-xl font-semibold">
                        <i class="fas fa-plus mr-2"></i>Add Your First Part
                    </button>
                </div>
            {% endif %}
        </div>
    </div>
{% endblock %}

{% block extra_js %}
<script>
// Simple spare parts management
document.addEventListener('DOMContentLoaded', function() {
    console.log('Spare parts page loaded');
});
</script>
{% endblock %}
