{% extends 'base_admin_dashboard.html' %}
{% load static %}

{% block dashboard_title %}User Management{% endblock %}
{% block page_title %}User Management{% endblock %}
{% block page_description %}Manage system users and their permissions{% endblock %}

{% comment %} {% block sidebar_nav %}
    <li>
        <a href="{% url 'core:dashboard' %}" class="dashboard-nav-link">
            <i class="fas fa-tachometer-alt"></i>
            <span>Dashboard</span>
        </a>
    </li>
    <li>
        <a href="{% url 'core:admin_users' %}" class="dashboard-nav-link active">
            <i class="fas fa-users"></i>
            <span>User Management</span>
        </a>
    </li>
    <li>
        <a href="{% url 'core:admin_vendors' %}" class="dashboard-nav-link">
            <i class="fas fa-store"></i>
            <span>Vendor Management</span>
        </a>
    </li>
    <li>
        <a href="{% url 'core:admin_listings' %}" class="dashboard-nav-link">
            <i class="fas fa-car"></i>
            <span>Car Listings</span>
        </a>
    </li>
    <li>
        <a href="#" class="dashboard-nav-link">
            <i class="fas fa-ship"></i>
            <span>Import Requests</span>
        </a>
    </li>
    <li>
        <a href="#" class="dashboard-nav-link">
            <i class="fas fa-tools"></i>
            <span>Spare Parts</span>
        </a>
    </li>
    <li>
        <a href="#" class="dashboard-nav-link">
            <i class="fas fa-envelope"></i>
            <span>Inquiries</span>
        </a>
    </li>
    <li>
        <a href="#" class="dashboard-nav-link">
            <i class="fas fa-newspaper"></i>
            <span>Content Management</span>
        </a>
    </li>
    <li>
        <a href="{% url 'core:admin_analytics' %}" class="dashboard-nav-link">
            <i class="fas fa-chart-bar"></i>
            <span>Analytics</span>
        </a>
    </li>
    <li>
        <a href="#" class="dashboard-nav-link">
            <i class="fas fa-cog"></i>
            <span>System Settings</span>
        </a>
    </li>
{% endblock %} {% endcomment %}

{% block breadcrumb_items %}
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">User Management</span>
        </div>
    </li>
{% endblock %}

{% block dashboard_content %}
    <!-- User Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="admin-stat-card text-center">
            <div class="admin-stat-value">{{ total_users }}</div>
            <div class="admin-stat-label">Total Users</div>
        </div>
        <div class="admin-stat-card text-center">
            <div class="admin-stat-value text-blue-600">{{ customers }}</div>
            <div class="admin-stat-label">Customers</div>
        </div>
        <div class="admin-stat-card text-center">
            <div class="admin-stat-value text-green-600">{{ vendors }}</div>
            <div class="admin-stat-label">Vendors</div>
        </div>
        <div class="admin-stat-card text-center">
            <div class="admin-stat-value text-purple-600">{{ admins }}</div>
            <div class="admin-stat-label">Administrators</div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="admin-card mb-6">
        <div class="p-6">
            <form method="get" class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                <div class="flex items-center space-x-4">
                    <select 
                        name="role" 
                        class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-harrier-red focus:border-harrier-red"
                        onchange="this.form.submit()">
                        <option value="">All Roles</option>
                        <option value="customer" {% if current_filter == 'customer' %}selected{% endif %}>Customers</option>
                        <option value="vendor" {% if current_filter == 'vendor' %}selected{% endif %}>Vendors</option>
                        <option value="admin" {% if current_filter == 'admin' %}selected{% endif %}>Administrators</option>
                    </select>
                    
                    <select 
                        name="status" 
                        class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-harrier-red focus:border-harrier-red"
                        onchange="this.form.submit()">
                        <option value="">All Status</option>
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                        <option value="verified">Verified</option>
                        <option value="unverified">Unverified</option>
                    </select>
                </div>
                
                <div class="flex items-center space-x-2">
                    <input 
                        type="text" 
                        name="search"
                        value="{{ search_query }}"
                        placeholder="Search users..." 
                        class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-harrier-red focus:border-harrier-red">
                    <button type="submit" class="btn-harrier-primary">
                        <i class="fas fa-search"></i>
                    </button>
                    <a href="{% url 'core:admin_users' %}" class="px-3 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700">
                        <i class="fas fa-times"></i>
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Bulk Actions -->
    <div id="bulkActions" class="hidden bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <div class="flex items-center justify-between">
            <span class="text-blue-800 font-medium">
                <span id="selectedCount">0</span> users selected
            </span>
            <div class="flex items-center space-x-2">
                <button 
                    type="button" 
                    class="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700"
                    onclick="performBulkAction('activate')">
                    Activate
                </button>
                <button 
                    type="button" 
                    class="px-3 py-1 text-sm bg-yellow-600 text-white rounded hover:bg-yellow-700"
                    onclick="performBulkAction('deactivate')">
                    Deactivate
                </button>
                <button 
                    type="button" 
                    class="px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700"
                    onclick="performBulkAction('delete')">
                    Delete
                </button>
            </div>
        </div>
    </div>

    <!-- Users Table -->
    <div class="dashboard-card">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left">
                            <input 
                                type="checkbox" 
                                id="selectAll" 
                                class="h-4 w-4 text-harrier-red focus:ring-harrier-red border-gray-300 rounded"
                                onchange="selectAllUsers()">
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Joined</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for user in users %}
                        <tr id="user-{{ user.id }}" class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input 
                                    type="checkbox" 
                                    name="selected_users" 
                                    value="{{ user.id }}"
                                    class="h-4 w-4 text-harrier-red focus:ring-harrier-red border-gray-300 rounded"
                                    onchange="updateBulkActions()">
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-harrier-red rounded-full flex items-center justify-center text-white font-semibold text-sm mr-3">
                                        {{ user.first_name|first|default:user.username|first|upper }}
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">
                                            {{ user.first_name|default:user.username }} {{ user.last_name }}
                                        </div>
                                        <div class="text-sm text-gray-500">{{ user.email }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                    {% if user.role == 'admin' %}bg-purple-100 text-purple-800
                                    {% elif user.role == 'vendor' %}bg-blue-100 text-blue-800
                                    {% else %}bg-gray-100 text-gray-800{% endif %}">
                                    {{ user.get_role_display }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex flex-col space-y-1">
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium
                                        {% if user.is_active %}bg-green-100 text-green-800
                                        {% else %}bg-red-100 text-red-800{% endif %}">
                                        {% if user.is_active %}Active{% else %}Inactive{% endif %}
                                    </span>
                                    {% if user.is_verified %}
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            Verified
                                        </span>
                                    {% endif %}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ user.date_joined|date:"M d, Y" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex items-center space-x-2">
                                    <button 
                                        type="button" 
                                        class="text-harrier-red hover:text-harrier-dark"
                                        onclick="viewUser({{ user.id }})">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button 
                                        type="button" 
                                        class="text-blue-600 hover:text-blue-900"
                                        onclick="editUser({{ user.id }})">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    {% if user.role == 'vendor' %}
                                        <button 
                                            type="button" 
                                            class="text-green-600 hover:text-green-900"
                                            onclick="manageVendor({{ user.id }})">
                                            <i class="fas fa-store"></i>
                                        </button>
                                    {% endif %}
                                    <button 
                                        type="button" 
                                        class="text-red-600 hover:text-red-900"
                                        onclick="deleteUser({{ user.id }})">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    {% empty %}
                        <tr>
                            <td colspan="6" class="px-6 py-8 text-center text-gray-500">
                                No users found matching your criteria.
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if users.has_other_pages %}
            <div class="px-6 py-3 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-700">
                        Showing {{ users.start_index }} to {{ users.end_index }} of {{ users.paginator.count }} results
                    </div>
                    <div class="flex space-x-2">
                        {% if users.has_previous %}
                            <a href="?page={{ users.previous_page_number }}" class="px-3 py-1 text-sm bg-white border border-gray-300 rounded hover:bg-gray-50">Previous</a>
                        {% endif %}
                        {% if users.has_next %}
                            <a href="?page={{ users.next_page_number }}" class="px-3 py-1 text-sm bg-white border border-gray-300 rounded hover:bg-gray-50">Next</a>
                        {% endif %}
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
{% endblock %}

{% block extra_js %}
<script>
function selectAllUsers() {
    const checkboxes = document.querySelectorAll('input[name="selected_users"]');
    const selectAll = document.getElementById('selectAll');
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
    updateBulkActions();
}

function updateBulkActions() {
    const selected = document.querySelectorAll('input[name="selected_users"]:checked');
    const bulkActions = document.getElementById('bulkActions');
    if (selected.length > 0) {
        bulkActions.classList.remove('hidden');
        document.getElementById('selectedCount').textContent = selected.length;
    } else {
        bulkActions.classList.add('hidden');
    }
}

function performBulkAction(action) {
    const selected = Array.from(document.querySelectorAll('input[name="selected_users"]:checked'))
                          .map(cb => cb.value);
    
    if (selected.length === 0) {
        alert('Please select at least one user.');
        return;
    }
    
    let confirmMessage = '';
    switch(action) {
        case 'delete':
            confirmMessage = `Delete ${selected.length} selected users? This cannot be undone.`;
            break;
        case 'activate':
            confirmMessage = `Activate ${selected.length} selected users?`;
            break;
        case 'deactivate':
            confirmMessage = `Deactivate ${selected.length} selected users?`;
            break;
    }
    
    if (confirm(confirmMessage)) {
        // Perform bulk action via AJAX
        console.log(`Performing ${action} on users:`, selected);
    }
}

function viewUser(userId) {
    window.location.href = `/dashboard/admin/users/${userId}/`;
}

function editUser(userId) {
    window.location.href = `/dashboard/admin/users/${userId}/edit/`;
}

function manageVendor(userId) {
    window.location.href = `/dashboard/admin/vendors/user/${userId}/`;
}

function deleteUser(userId) {
    if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
        // Perform delete via AJAX
        console.log('Deleting user:', userId);
    }
}
</script>
{% endblock %}
