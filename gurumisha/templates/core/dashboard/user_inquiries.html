{% extends 'base_dashboard.html' %}
{% load static %}

{% block dashboard_title %}Inquiries{% endblock %}
{% block page_title %}My Inquiries{% endblock %}
{% block page_description %}Manage your support requests and communication history{% endblock %}

{% block breadcrumb_items %}
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">Inquiries</span>
        </div>
    </li>
{% endblock %}

{% block dashboard_content %}
    <!-- Inquiry Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                    <i class="fas fa-envelope text-blue-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-2xl font-bold text-harrier-dark">{{ total_inquiries }}</p>
                    <p class="text-gray-600 text-sm font-medium">Total Inquiries</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center">
                    <i class="fas fa-clock text-yellow-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-2xl font-bold text-yellow-600">{{ open_inquiries }}</p>
                    <p class="text-gray-600 text-sm font-medium">Open</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                    <i class="fas fa-cog text-blue-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-2xl font-bold text-blue-600">{{ in_progress_inquiries }}</p>
                    <p class="text-gray-600 text-sm font-medium">In Progress</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                    <i class="fas fa-check-circle text-green-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-2xl font-bold text-green-600">{{ resolved_inquiries }}</p>
                    <p class="text-gray-600 text-sm font-medium">Resolved</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-6 mb-8">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <div class="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
                <!-- Status Filter -->
                <div class="relative">
                    <select name="status" class="appearance-none bg-gray-50 border border-gray-300 rounded-lg px-4 py-3 pr-8 focus:ring-2 focus:ring-harrier-red focus:border-harrier-red transition-all duration-200" 
                            hx-get="{% url 'core:user_inquiries' %}" 
                            hx-trigger="change" 
                            hx-target="#inquiries-list"
                            hx-include="[name='search']">
                        <option value="">All Status</option>
                        <option value="open" {% if status_filter == 'open' %}selected{% endif %}>Open</option>
                        <option value="in_progress" {% if status_filter == 'in_progress' %}selected{% endif %}>In Progress</option>
                        <option value="resolved" {% if status_filter == 'resolved' %}selected{% endif %}>Resolved</option>
                        <option value="closed" {% if status_filter == 'closed' %}selected{% endif %}>Closed</option>
                    </select>
                    <i class="fas fa-chevron-down absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none"></i>
                </div>
                
                <!-- Priority Filter -->
                <div class="relative">
                    <select name="priority" class="appearance-none bg-gray-50 border border-gray-300 rounded-lg px-4 py-3 pr-8 focus:ring-2 focus:ring-harrier-red focus:border-harrier-red transition-all duration-200">
                        <option value="">All Priority</option>
                        <option value="low">Low</option>
                        <option value="medium">Medium</option>
                        <option value="high">High</option>
                        <option value="urgent">Urgent</option>
                    </select>
                    <i class="fas fa-chevron-down absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none"></i>
                </div>
            </div>
            
            <div class="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
                <!-- Search -->
                <div class="relative">
                    <input type="text" 
                           name="search" 
                           value="{{ search_query }}"
                           placeholder="Search inquiries..." 
                           class="bg-gray-50 border border-gray-300 rounded-lg px-4 py-3 pl-10 focus:ring-2 focus:ring-harrier-red focus:border-harrier-red transition-all duration-200 w-full sm:w-80"
                           hx-get="{% url 'core:user_inquiries' %}" 
                           hx-trigger="keyup changed delay:500ms" 
                           hx-target="#inquiries-list"
                           hx-include="[name='status']">
                    <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                </div>
                
                <!-- New Inquiry Button -->
                <button onclick="openNewInquiryModal()" 
                        class="bg-gradient-to-r from-harrier-red to-harrier-dark hover:from-harrier-dark hover:to-harrier-red text-white px-6 py-3 rounded-lg font-medium transition-all duration-200 flex items-center justify-center whitespace-nowrap">
                    <i class="fas fa-plus mr-2"></i>
                    New Inquiry
                </button>
            </div>
        </div>
    </div>

    <!-- Inquiries List -->
    <div id="inquiries-list">
        {% if inquiries %}
            <div class="space-y-6">
                {% for inquiry in inquiries %}
                    <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden hover:shadow-xl transition-all duration-300 group">
                        <div class="p-6">
                            <div class="flex items-start justify-between mb-4">
                                <div class="flex-1">
                                    <div class="flex items-center justify-between mb-3">
                                        <h3 class="text-xl font-bold text-harrier-dark group-hover:text-harrier-red transition-colors">
                                            {{ inquiry.subject }}
                                        </h3>
                                        <div class="flex items-center space-x-2">
                                            <span class="px-3 py-1 text-sm font-bold rounded-full
                                                {% if inquiry.status == 'resolved' %}bg-green-100 text-green-800 border border-green-200
                                                {% elif inquiry.status == 'closed' %}bg-gray-100 text-gray-800 border border-gray-200
                                                {% elif inquiry.status == 'in_progress' %}bg-blue-100 text-blue-800 border border-blue-200
                                                {% else %}bg-yellow-100 text-yellow-800 border border-yellow-200{% endif %}">
                                                {{ inquiry.get_status_display }}
                                            </span>
                                            {% if inquiry.priority %}
                                                <span class="px-2 py-1 text-xs font-bold rounded-full
                                                    {% if inquiry.priority == 'urgent' %}bg-red-100 text-red-800
                                                    {% elif inquiry.priority == 'high' %}bg-orange-100 text-orange-800
                                                    {% elif inquiry.priority == 'medium' %}bg-yellow-100 text-yellow-800
                                                    {% else %}bg-gray-100 text-gray-800{% endif %}">
                                                    {{ inquiry.priority|title }}
                                                </span>
                                            {% endif %}
                                        </div>
                                    </div>
                                    
                                    <p class="text-gray-600 mb-4 leading-relaxed">{{ inquiry.message|truncatewords:30 }}</p>
                                    
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                                        <div class="flex items-center text-gray-600">
                                            <i class="fas fa-calendar mr-2 text-blue-500"></i>
                                            <span class="text-sm">{{ inquiry.created_at|date:"M d, Y" }}</span>
                                        </div>
                                        <div class="flex items-center text-gray-600">
                                            <i class="fas fa-clock mr-2 text-green-500"></i>
                                            <span class="text-sm">{{ inquiry.created_at|time:"H:i" }}</span>
                                        </div>
                                        {% if inquiry.car %}
                                            <div class="flex items-center text-gray-600">
                                                <i class="fas fa-car mr-2 text-purple-500"></i>
                                                <span class="text-sm">{{ inquiry.car.title|truncatechars:20 }}</span>
                                            </div>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-4">
                                            {% if inquiry.response_count %}
                                                <div class="flex items-center text-sm text-gray-600">
                                                    <i class="fas fa-reply mr-1 text-blue-500"></i>
                                                    <span>{{ inquiry.response_count }} response{{ inquiry.response_count|pluralize }}</span>
                                                </div>
                                            {% endif %}
                                            {% if inquiry.last_response_at %}
                                                <div class="flex items-center text-sm text-gray-600">
                                                    <i class="fas fa-clock mr-1 text-green-500"></i>
                                                    <span>Last reply: {{ inquiry.last_response_at|timesince }} ago</span>
                                                </div>
                                            {% endif %}
                                        </div>
                                        
                                        <div class="flex items-center space-x-3">
                                            <button onclick="viewInquiry({{ inquiry.id }})" 
                                                    class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center">
                                                <i class="fas fa-eye mr-2"></i>
                                                View Details
                                            </button>
                                            
                                            {% if inquiry.status != 'closed' %}
                                                <button onclick="replyToInquiry({{ inquiry.id }})" 
                                                        class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center">
                                                    <i class="fas fa-reply mr-2"></i>
                                                    Reply
                                                </button>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
            
            <!-- Pagination -->
            <div class="mt-8 flex items-center justify-center">
                <nav class="flex items-center space-x-2">
                    <button class="px-4 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gray-50 text-gray-700 transition-all duration-200">
                        <i class="fas fa-chevron-left mr-1"></i>Previous
                    </button>
                    <button class="px-4 py-2 text-sm bg-harrier-red text-white rounded-lg">1</button>
                    <button class="px-4 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gray-50 text-gray-700 transition-all duration-200">2</button>
                    <button class="px-4 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gray-50 text-gray-700 transition-all duration-200">3</button>
                    <button class="px-4 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gray-50 text-gray-700 transition-all duration-200">
                        Next<i class="fas fa-chevron-right ml-1"></i>
                    </button>
                </nav>
            </div>
        {% else %}
            <!-- Empty State -->
            <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-12 text-center">
                <div class="w-24 h-24 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-envelope text-4xl text-orange-500"></i>
                </div>
                <h3 class="text-2xl font-bold text-harrier-dark mb-4">No Inquiries Yet</h3>
                <p class="text-gray-600 mb-8 max-w-md mx-auto">
                    Have a question or need help? Create your first inquiry and our support team will assist you.
                </p>
                <button onclick="openNewInquiryModal()" 
                        class="bg-gradient-to-r from-harrier-red to-harrier-dark hover:from-harrier-dark hover:to-harrier-red text-white px-8 py-4 rounded-xl font-bold text-lg transition-all duration-200 inline-flex items-center">
                    <i class="fas fa-plus mr-3"></i>
                    Create Inquiry
                </button>
            </div>
        {% endif %}
    </div>

    <!-- New Inquiry Modal -->
    <div id="newInquiryModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
        <div class="bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-2xl font-bold text-harrier-dark">Create New Inquiry</h3>
                    <button onclick="closeNewInquiryModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            
            <form class="p-6 space-y-6">
                <div>
                    <label class="block text-sm font-bold text-gray-700 mb-2">Subject *</label>
                    <input type="text" name="subject" required 
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-harrier-red transition-all duration-200"
                           placeholder="Brief description of your inquiry">
                </div>
                
                <div>
                    <label class="block text-sm font-bold text-gray-700 mb-2">Priority</label>
                    <select name="priority" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-harrier-red transition-all duration-200">
                        <option value="low">Low</option>
                        <option value="medium" selected>Medium</option>
                        <option value="high">High</option>
                        <option value="urgent">Urgent</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-bold text-gray-700 mb-2">Category</label>
                    <select name="category" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-harrier-red transition-all duration-200">
                        <option value="general">General Inquiry</option>
                        <option value="car_inquiry">Car Inquiry</option>
                        <option value="import_request">Import Request</option>
                        <option value="spare_parts">Spare Parts</option>
                        <option value="technical_support">Technical Support</option>
                        <option value="billing">Billing</option>
                        <option value="complaint">Complaint</option>
                    </select>
                </div>
                
                <div>
                    <label class="block text-sm font-bold text-gray-700 mb-2">Message *</label>
                    <textarea name="message" rows="6" required 
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-harrier-red transition-all duration-200"
                              placeholder="Please provide detailed information about your inquiry..."></textarea>
                </div>
                
                <div class="flex items-center justify-end space-x-4 pt-4 border-t border-gray-200">
                    <button type="button" onclick="closeNewInquiryModal()" 
                            class="px-6 py-3 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-all duration-200">
                        Cancel
                    </button>
                    <button type="submit" 
                            class="px-6 py-3 bg-gradient-to-r from-harrier-red to-harrier-dark text-white rounded-lg hover:from-harrier-dark hover:to-harrier-red transition-all duration-200">
                        Create Inquiry
                    </button>
                </div>
            </form>
        </div>
    </div>
{% endblock %}

{% block extra_js %}
<script>
function openNewInquiryModal() {
    document.getElementById('newInquiryModal').classList.remove('hidden');
    document.body.style.overflow = 'hidden';
}

function closeNewInquiryModal() {
    document.getElementById('newInquiryModal').classList.add('hidden');
    document.body.style.overflow = 'auto';
}

function viewInquiry(inquiryId) {
    // Implementation for viewing inquiry details
    console.log('View inquiry:', inquiryId);
}

function replyToInquiry(inquiryId) {
    // Implementation for replying to inquiry
    console.log('Reply to inquiry:', inquiryId);
}

document.addEventListener('DOMContentLoaded', function() {
    // Enhanced filtering and search functionality
    document.addEventListener('htmx:beforeRequest', function(event) {
        const target = event.target;
        if (target.matches('select[name="status"], input[name="search"]')) {
            document.getElementById('inquiries-list').classList.add('opacity-50');
        }
    });
    
    document.addEventListener('htmx:afterRequest', function(event) {
        document.getElementById('inquiries-list').classList.remove('opacity-50');
    });
    
    // Close modal on escape key
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            closeNewInquiryModal();
        }
    });
    
    // Close modal on backdrop click
    document.getElementById('newInquiryModal').addEventListener('click', function(event) {
        if (event.target === this) {
            closeNewInquiryModal();
        }
    });
});
</script>
{% endblock %}
