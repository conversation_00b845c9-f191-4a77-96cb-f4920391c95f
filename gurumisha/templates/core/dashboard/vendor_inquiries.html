{% extends 'base_dashboard.html' %}
{% load static %}

{% block dashboard_title %}Customer Inquiries{% endblock %}
{% block page_title %}Customer Inquiries{% endblock %}
{% block page_description %}Manage and respond to customer inquiries{% endblock %}

{% block breadcrumb_items %}
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">Inquiries</span>
        </div>
    </li>
{% endblock %}

{% block dashboard_content %}
    <!-- Enhanced Inquiry Stats with Modern Design -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8 animate-fade-in-up">
        <div class="bg-gradient-to-br from-white to-gray-50 rounded-xl p-6 border border-gray-200/50 shadow-lg hover:shadow-xl transition-all duration-300 group">
            <div class="flex items-center justify-between mb-4">
                <div class="w-12 h-12 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-envelope-open text-white"></i>
                </div>
                <div class="text-right">
                    <div class="text-2xl font-bold text-yellow-600 font-montserrat">{{ open_inquiries }}</div>
                    <div class="text-sm text-gray-600 font-raleway">Open Inquiries</div>
                </div>
            </div>
            <div class="flex items-center text-sm">
                {% if open_inquiries > 0 %}
                    <div class="w-2 h-2 bg-yellow-500 rounded-full mr-2 animate-pulse"></div>
                    <span class="text-yellow-600 font-medium">Needs attention</span>
                {% else %}
                    <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                    <span class="text-green-600 font-medium">All caught up</span>
                {% endif %}
            </div>
        </div>

        <div class="bg-gradient-to-br from-white to-gray-50 rounded-xl p-6 border border-gray-200/50 shadow-lg hover:shadow-xl transition-all duration-300 group">
            <div class="flex items-center justify-between mb-4">
                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-cogs text-white"></i>
                </div>
                <div class="text-right">
                    <div class="text-2xl font-bold text-blue-600 font-montserrat">{{ in_progress_inquiries }}</div>
                    <div class="text-sm text-gray-600 font-raleway">In Progress</div>
                </div>
            </div>
            <div class="flex items-center text-sm">
                <div class="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                <span class="text-blue-600 font-medium">Being handled</span>
            </div>
        </div>

        <div class="bg-gradient-to-br from-white to-gray-50 rounded-xl p-6 border border-gray-200/50 shadow-lg hover:shadow-xl transition-all duration-300 group">
            <div class="flex items-center justify-between mb-4">
                <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-check-circle text-white"></i>
                </div>
                <div class="text-right">
                    <div class="text-2xl font-bold text-green-600 font-montserrat">{{ resolved_inquiries }}</div>
                    <div class="text-sm text-gray-600 font-raleway">Resolved</div>
                </div>
            </div>
            <div class="flex items-center text-sm">
                <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                <span class="text-green-600 font-medium">Completed</span>
            </div>
        </div>

        <div class="bg-gradient-to-br from-white to-gray-50 rounded-xl p-6 border border-gray-200/50 shadow-lg hover:shadow-xl transition-all duration-300 group">
            <div class="flex items-center justify-between mb-4">
                <div class="w-12 h-12 bg-gradient-to-br from-harrier-red to-harrier-red-dark rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                    <i class="fas fa-envelope text-white"></i>
                </div>
                <div class="text-right">
                    <div class="text-2xl font-bold text-harrier-dark font-montserrat">{{ inquiries.count }}</div>
                    <div class="text-sm text-gray-600 font-raleway">Total Inquiries</div>
                </div>
            </div>
            <div class="flex items-center text-sm">
                <div class="w-2 h-2 bg-harrier-red rounded-full mr-2"></div>
                <span class="text-gray-600 font-medium">All time</span>
            </div>
        </div>
    </div>

    <!-- Enhanced Inquiry Filters -->
    <div class="bg-gradient-to-br from-white to-gray-50 rounded-xl p-6 border border-gray-200/50 shadow-lg mb-8 animate-fade-in-up" style="animation-delay: 0.1s;">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <!-- Filters Section -->
            <div class="flex flex-col sm:flex-row sm:items-center space-y-3 sm:space-y-0 sm:space-x-4">
                <div class="flex items-center space-x-2">
                    <i class="fas fa-filter text-harrier-red"></i>
                    <span class="text-sm font-semibold text-harrier-dark font-montserrat">Filters:</span>
                </div>

                <div class="flex flex-wrap items-center gap-3">
                    <select
                        name="status_filter"
                        class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-harrier-red bg-white shadow-sm text-sm font-raleway transition-all duration-200 hover:border-harrier-red/50"
                        hx-get="{% url 'core:vendor_inquiries' %}"
                        hx-target="#inquiries-list"
                        hx-trigger="change">
                        <option value="">📋 All Status</option>
                        <option value="open">📬 Open</option>
                        <option value="in_progress">⚙️ In Progress</option>
                        <option value="resolved">✅ Resolved</option>
                    </select>

                    <select
                        name="car_filter"
                        class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-harrier-red bg-white shadow-sm text-sm font-raleway transition-all duration-200 hover:border-harrier-red/50"
                        hx-get="{% url 'core:vendor_inquiries' %}"
                        hx-target="#inquiries-list"
                        hx-trigger="change">
                        <option value="">🚗 All Cars</option>
                        {% for car in vendor_cars %}
                            <option value="{{ car.id }}">{{ car.title }}</option>
                        {% endfor %}
                    </select>

                    <!-- Priority Filter -->
                    <select
                        name="priority_filter"
                        class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-harrier-red bg-white shadow-sm text-sm font-raleway transition-all duration-200 hover:border-harrier-red/50"
                        hx-get="{% url 'core:vendor_inquiries' %}"
                        hx-target="#inquiries-list"
                        hx-trigger="change">
                        <option value="">⭐ All Priority</option>
                        <option value="high">🔴 High</option>
                        <option value="medium">🟡 Medium</option>
                        <option value="low">🟢 Low</option>
                    </select>
                </div>
            </div>

            <!-- Search and Actions Section -->
            <div class="flex flex-col sm:flex-row sm:items-center space-y-3 sm:space-y-0 sm:space-x-3">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input
                        type="text"
                        name="search"
                        placeholder="Search inquiries..."
                        class="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-harrier-red bg-white shadow-sm text-sm font-raleway transition-all duration-200 hover:border-harrier-red/50 w-full sm:w-64"
                        hx-get="{% url 'core:vendor_inquiries' %}"
                        hx-target="#inquiries-list"
                        hx-trigger="keyup changed delay:500ms">
                </div>

                <div class="flex items-center space-x-2">
                    <button onclick="exportInquiries()" class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg font-medium text-sm hover:from-blue-600 hover:to-blue-700 transition-all duration-200 transform hover:scale-105 shadow-lg">
                        <i class="fas fa-download mr-2"></i>Export
                    </button>

                    <button onclick="openResponseTemplates()" class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-lg font-medium text-sm hover:from-purple-600 hover:to-purple-700 transition-all duration-200 transform hover:scale-105 shadow-lg">
                        <i class="fas fa-templates mr-2"></i>Templates
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Inquiries List -->
    <div id="inquiries-list">
        {% include 'core/dashboard/partials/inquiry_list.html' %}
    </div>
{% endblock %}

{% block extra_js %}
<script src="https://unpkg.com/htmx.org@1.9.10"></script>
<script>
// Enhanced inquiry management with better UX
document.addEventListener('DOMContentLoaded', function() {
    // Auto-refresh inquiries every 60 seconds with visual indicator
    setInterval(function() {
        const indicator = document.createElement('div');
        indicator.className = 'fixed top-4 left-4 bg-harrier-red text-white px-3 py-1 rounded-lg text-sm z-50 animate-fade-in-up';
        indicator.innerHTML = '<i class="fas fa-sync-alt fa-spin mr-1"></i>Refreshing inquiries...';
        document.body.appendChild(indicator);

        htmx.trigger('#inquiries-list', 'refresh');

        setTimeout(() => {
            if (indicator.parentNode) {
                indicator.remove();
            }
        }, 2000);
    }, 60000);
});

// Enhanced response modal with templates
function respondToInquiry(inquiryId, customerName = '', subject = '') {
    const modal = document.getElementById('responseModal');

    // Set the inquiry ID
    document.getElementById('inquiryId').value = inquiryId;

    // Pre-fill customer info if available
    if (customerName) {
        document.getElementById('customerName').textContent = customerName;
    }
    if (subject) {
        document.getElementById('inquirySubject').textContent = subject;
    }

    // Show the modal with animation
    modal.classList.remove('hidden');
    setTimeout(() => {
        modal.querySelector('.modal-content').classList.add('modal-enter');
    }, 10);
}

function closeResponseModal() {
    const modal = document.getElementById('responseModal');
    const modalContent = modal.querySelector('.modal-content');

    // Animate out
    modalContent.classList.remove('modal-enter');
    setTimeout(() => {
        modal.classList.add('hidden');
        // Reset form
        document.getElementById('responseForm').reset();
    }, 300);
}

// Enhanced status update with confirmation
function updateInquiryStatus(inquiryId, status, customerName = '') {
    const statusMessages = {
        'in_progress': '⚙️ Mark this inquiry as in progress?',
        'resolved': '✅ Mark this inquiry as resolved?',
        'open': '📬 Reopen this inquiry?'
    };

    const confirmMessage = statusMessages[status] || `Update inquiry status to ${status}?`;

    if (!confirm(confirmMessage + (customerName ? `\n\nCustomer: ${customerName}` : ''))) {
        return;
    }

    // Show loading state
    const button = event.target;
    const originalContent = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i>Updating...';
    button.disabled = true;

    htmx.ajax('POST', '{% url "core:update_inquiry_status" %}', {
        values: {
            'inquiry_id': inquiryId,
            'status': status,
            'csrfmiddlewaretoken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        target: '#inquiry-' + inquiryId,
        swap: 'outerHTML'
    });
}

// Response templates functionality
function openResponseTemplates() {
    const templates = [
        {
            name: 'Thank You for Interest',
            content: 'Thank you for your interest in our vehicle. I would be happy to provide you with more information and arrange a viewing at your convenience.'
        },
        {
            name: 'Price Negotiation',
            content: 'Thank you for your inquiry about the price. I understand you are looking for the best value. Let me see what I can do to work within your budget.'
        },
        {
            name: 'Vehicle Inspection',
            content: 'I appreciate your interest in inspecting the vehicle. You are welcome to bring a qualified mechanic for a thorough inspection. Please let me know when would be convenient for you.'
        },
        {
            name: 'Financing Options',
            content: 'We offer various financing options to help make your purchase easier. I can connect you with our financing partners who offer competitive rates.'
        },
        {
            name: 'Trade-in Inquiry',
            content: 'Yes, we do accept trade-ins. If you could provide details about your current vehicle including make, model, year, and condition, I can provide you with a preliminary estimate.'
        }
    ];

    showTemplateModal(templates);
}

function showTemplateModal(templates) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 z-50 overflow-y-auto';
    modal.innerHTML = `
        <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" onclick="this.parentElement.parentElement.remove()"></div>

            <div class="inline-block w-full max-w-2xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-xl">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-xl font-bold text-harrier-dark font-montserrat">Response Templates</h3>
                    <button type="button" class="text-gray-400 hover:text-gray-600" onclick="this.closest('.fixed').remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div class="space-y-3 max-h-96 overflow-y-auto">
                    ${templates.map(template => `
                        <div class="border border-gray-200 rounded-lg p-4 hover:border-harrier-red/30 hover:bg-harrier-red/5 transition-all duration-200 cursor-pointer" onclick="useTemplate('${template.content.replace(/'/g, "\\'")}'); this.closest('.fixed').remove();">
                            <h4 class="font-semibold text-harrier-dark font-montserrat">${template.name}</h4>
                            <p class="text-sm text-gray-600 mt-1 font-raleway">${template.content}</p>
                        </div>
                    `).join('')}
                </div>

                <div class="mt-6 pt-4 border-t border-gray-200">
                    <button onclick="this.closest('.fixed').remove()" class="w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium">
                        Close
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
}

function useTemplate(content) {
    const responseTextarea = document.getElementById('response');
    if (responseTextarea) {
        responseTextarea.value = content;
        responseTextarea.focus();
    }
}

// Export inquiries function
function exportInquiries() {
    showMessage('📊 Preparing inquiries export...', 'info');

    // Create download link
    const link = document.createElement('a');
    link.href = '/dashboard/vendor/inquiries/export/';
    link.download = `inquiries_${new Date().toISOString().split('T')[0]}.csv`;
    link.click();

    setTimeout(() => {
        showMessage('✅ Inquiries exported successfully!', 'success');
    }, 1000);
}

// Enhanced message display
function showMessage(message, type) {
    const toast = document.createElement('div');
    const icons = {
        'success': '✅',
        'error': '❌',
        'info': 'ℹ️',
        'warning': '⚠️'
    };

    const colors = {
        'success': 'bg-gradient-to-r from-green-500 to-green-600',
        'error': 'bg-gradient-to-r from-red-500 to-red-600',
        'info': 'bg-gradient-to-r from-blue-500 to-blue-600',
        'warning': 'bg-gradient-to-r from-yellow-500 to-yellow-600'
    };

    toast.className = `fixed top-4 right-4 z-50 px-6 py-4 rounded-xl text-white font-semibold shadow-xl transform transition-all duration-300 ${colors[type] || colors.info} animate-fade-in-up`;
    toast.innerHTML = `
        <div class="flex items-center">
            <span class="mr-2 text-lg">${icons[type] || icons.info}</span>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(toast);

    // Auto remove after 4 seconds
    setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        toast.style.opacity = '0';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.remove();
            }
        }, 300);
    }, 4000);
}

// Handle HTMX events with enhanced feedback
document.body.addEventListener('htmx:afterRequest', function(event) {
    if (event.detail.successful) {
        showMessage('✅ Action completed successfully!', 'success');
    } else {
        showMessage('❌ Something went wrong. Please try again.', 'error');
    }
});

// Keyboard shortcuts
document.addEventListener('keydown', function(event) {
    // Ctrl/Cmd + R for refresh
    if ((event.ctrlKey || event.metaKey) && event.key === 'r') {
        event.preventDefault();
        htmx.trigger('#inquiries-list', 'refresh');
        showMessage('🔄 Refreshing inquiries...', 'info');
    }

    // Escape to close modals
    if (event.key === 'Escape') {
        const modal = document.getElementById('responseModal');
        if (modal && !modal.classList.contains('hidden')) {
            closeResponseModal();
        }
    }
});
</script>

<style>
.modal-content {
    transform: scale(0.95);
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.modal-content.modal-enter {
    transform: scale(1);
    opacity: 1;
}
</style>
{% endblock %}

<!-- Enhanced Response Modal -->
{% block extra_content %}
<div id="responseModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75 backdrop-blur-sm" onclick="closeResponseModal()"></div>

        <div class="modal-content inline-block w-full max-w-2xl p-8 my-8 overflow-hidden text-left align-middle bg-gradient-to-br from-white to-gray-50 shadow-2xl rounded-2xl border border-gray-200/50">
            <!-- Enhanced Header -->
            <div class="flex items-center justify-between mb-8">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-gradient-to-br from-harrier-red to-harrier-red-dark rounded-lg flex items-center justify-center mr-4">
                        <i class="fas fa-reply text-white"></i>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold text-harrier-dark font-montserrat">Respond to Inquiry</h3>
                        <p class="text-sm text-gray-600 font-raleway">Customer: <span id="customerName" class="font-medium"></span></p>
                        <p class="text-sm text-gray-600 font-raleway">Subject: <span id="inquirySubject" class="font-medium"></span></p>
                    </div>
                </div>
                <button type="button" class="text-gray-400 hover:text-gray-600 p-2 hover:bg-gray-100 rounded-lg transition-colors" onclick="closeResponseModal()">
                    <i class="fas fa-times text-lg"></i>
                </button>
            </div>

            <form
                id="responseForm"
                hx-post="{% url 'core:respond_to_inquiry' %}"
                hx-target="#inquiries-list"
                hx-on::after-request="closeResponseModal()">
                {% csrf_token %}
                <input type="hidden" id="inquiryId" name="inquiry_id">

                <div class="space-y-6">
                    <!-- Response Text Area -->
                    <div>
                        <div class="flex items-center justify-between mb-2">
                            <label for="response" class="block text-sm font-bold text-harrier-dark font-montserrat">Your Response</label>
                            <button type="button" onclick="openResponseTemplates()" class="text-sm text-harrier-red hover:text-harrier-dark font-medium transition-colors">
                                <i class="fas fa-templates mr-1"></i>Use Template
                            </button>
                        </div>
                        <textarea
                            id="response"
                            name="response"
                            required
                            rows="6"
                            class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-harrier-red bg-white shadow-sm font-raleway transition-all duration-200"
                            placeholder="Type your response to the customer..."></textarea>
                        <div class="mt-2 text-xs text-gray-500">
                            <span id="charCount">0</span> characters
                        </div>
                    </div>

                    <!-- Status and Priority -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="status" class="block text-sm font-bold text-harrier-dark mb-2 font-montserrat">Update Status</label>
                            <select
                                id="status"
                                name="status"
                                required
                                class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-harrier-red bg-white shadow-sm font-raleway">
                                <option value="in_progress">⚙️ In Progress</option>
                                <option value="resolved">✅ Resolved</option>
                            </select>
                        </div>

                        <div>
                            <label for="priority" class="block text-sm font-bold text-harrier-dark mb-2 font-montserrat">Priority Level</label>
                            <select
                                id="priority"
                                name="priority"
                                class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-harrier-red bg-white shadow-sm font-raleway">
                                <option value="low">🟢 Low</option>
                                <option value="medium">🟡 Medium</option>
                                <option value="high">🔴 High</option>
                            </select>
                        </div>
                    </div>

                    <!-- Options -->
                    <div class="bg-white/60 rounded-xl p-4 border border-gray-100">
                        <h4 class="font-bold text-harrier-dark mb-3 font-montserrat">Response Options</h4>
                        <div class="space-y-3">
                            <div class="flex items-center">
                                <input
                                    type="checkbox"
                                    id="send_email"
                                    name="send_email"
                                    checked
                                    class="h-5 w-5 text-harrier-red focus:ring-harrier-red border-gray-300 rounded">
                                <label for="send_email" class="ml-3 block text-sm text-gray-700 font-raleway">
                                    <span class="font-medium">Send email notification to customer</span>
                                    <div class="text-xs text-gray-500">Customer will receive your response via email</div>
                                </label>
                            </div>

                            <div class="flex items-center">
                                <input
                                    type="checkbox"
                                    id="follow_up"
                                    name="follow_up"
                                    class="h-5 w-5 text-harrier-red focus:ring-harrier-red border-gray-300 rounded">
                                <label for="follow_up" class="ml-3 block text-sm text-gray-700 font-raleway">
                                    <span class="font-medium">Schedule follow-up reminder</span>
                                    <div class="text-xs text-gray-500">Remind me to follow up in 3 days</div>
                                </label>
                            </div>

                            <div class="flex items-center">
                                <input
                                    type="checkbox"
                                    id="internal_note"
                                    name="internal_note"
                                    class="h-5 w-5 text-harrier-red focus:ring-harrier-red border-gray-300 rounded">
                                <label for="internal_note" class="ml-3 block text-sm text-gray-700 font-raleway">
                                    <span class="font-medium">Add internal note</span>
                                    <div class="text-xs text-gray-500">Add private notes for your team</div>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Action Buttons -->
                <div class="flex items-center justify-end space-x-4 mt-8 pt-6 border-t border-gray-200">
                    <button type="button" class="px-6 py-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-xl hover:bg-gray-50 transition-all duration-200 transform hover:scale-105" onclick="closeResponseModal()">
                        <i class="fas fa-times mr-2"></i>Cancel
                    </button>
                    <button type="submit" class="inline-flex items-center px-8 py-3 bg-gradient-to-r from-harrier-red to-harrier-red-dark text-white rounded-xl font-bold hover:from-harrier-red-dark hover:to-harrier-red transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl font-montserrat">
                        <i class="fas fa-paper-plane mr-2"></i>Send Response
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Character counter for response textarea
document.addEventListener('DOMContentLoaded', function() {
    const responseTextarea = document.getElementById('response');
    const charCount = document.getElementById('charCount');

    if (responseTextarea && charCount) {
        responseTextarea.addEventListener('input', function() {
            charCount.textContent = this.value.length;
        });
    }
});
</script>
{% endblock %}
