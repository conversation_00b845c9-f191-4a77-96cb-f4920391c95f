{% extends 'base_dashboard.html' %}
{% load static %}

{% block dashboard_title %}My Orders{% endblock %}
{% block page_title %}My Orders{% endblock %}
{% block page_description %}Track your orders and purchase history{% endblock %}

{% block breadcrumb_items %}
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">Orders</span>
        </div>
    </li>
{% endblock %}

{% block dashboard_content %}
    <!-- Order Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                    <i class="fas fa-shopping-bag text-blue-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-2xl font-bold text-harrier-dark">{{ total_orders|default:0 }}</p>
                    <p class="text-gray-600 text-sm font-medium">Total Orders</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-yellow-100 rounded-xl flex items-center justify-center">
                    <i class="fas fa-clock text-yellow-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-2xl font-bold text-yellow-600">{{ pending_orders|default:0 }}</p>
                    <p class="text-gray-600 text-sm font-medium">Pending</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                    <i class="fas fa-check-circle text-green-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-2xl font-bold text-green-600">{{ completed_orders|default:0 }}</p>
                    <p class="text-gray-600 text-sm font-medium">Completed</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-xl p-6 shadow-lg border border-gray-200 hover:shadow-xl transition-all duration-300">
            <div class="flex items-center">
                <div class="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                    <i class="fas fa-dollar-sign text-purple-600 text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-2xl font-bold text-purple-600">KSh {{ total_spent|default:0|floatformat:0 }}</p>
                    <p class="text-gray-600 text-sm font-medium">Total Spent</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Order Filters -->
    <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-6 mb-8">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <div class="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
                <!-- Status Filter -->
                <div class="relative">
                    <select name="status" class="appearance-none bg-gray-50 border border-gray-300 rounded-lg px-4 py-3 pr-8 focus:ring-2 focus:ring-harrier-red focus:border-harrier-red transition-all duration-200"
                            hx-get="{% url 'core:user_orders' %}"
                            hx-trigger="change"
                            hx-target="#orders-list"
                            hx-include="[name='search']">
                        <option value="">All Orders</option>
                        <option value="pending">Pending</option>
                        <option value="processing">Processing</option>
                        <option value="shipped">Shipped</option>
                        <option value="delivered">Delivered</option>
                        <option value="cancelled">Cancelled</option>
                    </select>
                    <i class="fas fa-chevron-down absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none"></i>
                </div>

                <!-- Date Filter -->
                <div class="relative">
                    <select name="date_range" class="appearance-none bg-gray-50 border border-gray-300 rounded-lg px-4 py-3 pr-8 focus:ring-2 focus:ring-harrier-red focus:border-harrier-red transition-all duration-200">
                        <option value="">All Time</option>
                        <option value="7">Last 7 Days</option>
                        <option value="30">Last 30 Days</option>
                        <option value="90">Last 3 Months</option>
                        <option value="365">Last Year</option>
                    </select>
                    <i class="fas fa-chevron-down absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 pointer-events-none"></i>
                </div>
            </div>

            <div class="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
                <!-- Search -->
                <div class="relative">
                    <input type="text"
                           name="search"
                           placeholder="Search orders..."
                           class="bg-gray-50 border border-gray-300 rounded-lg px-4 py-3 pl-10 focus:ring-2 focus:ring-harrier-red focus:border-harrier-red transition-all duration-200 w-full sm:w-80"
                           hx-get="{% url 'core:user_orders' %}"
                           hx-trigger="keyup changed delay:500ms"
                           hx-target="#orders-list"
                           hx-include="[name='status']">
                    <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                </div>

                <!-- Export Button -->
                <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-6 py-3 rounded-lg font-medium transition-all duration-200 flex items-center justify-center whitespace-nowrap">
                    <i class="fas fa-download mr-2"></i>
                    Export
                </button>
            </div>
        </div>
    </div>

    <!-- Enhanced Orders List -->
    <div id="orders-list">
        {% if orders %}
            <div class="space-y-6">
                {% for order in orders %}
                    <div class="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden hover:shadow-xl transition-all duration-300 group">
                        <div class="p-6">
                            <div class="flex items-start justify-between mb-6">
                                <div class="flex-1">
                                    <div class="flex items-center justify-between mb-3">
                                        <h3 class="text-xl font-bold text-harrier-dark group-hover:text-harrier-red transition-colors">
                                            Order #{{ order.id|default:"ORD-2024-001" }}
                                        </h3>
                                        <span class="px-3 py-1 text-sm font-bold rounded-full
                                            {% if order.status == 'delivered' %}bg-green-100 text-green-800 border border-green-200
                                            {% elif order.status == 'shipped' %}bg-blue-100 text-blue-800 border border-blue-200
                                            {% elif order.status == 'processing' %}bg-yellow-100 text-yellow-800 border border-yellow-200
                                            {% elif order.status == 'cancelled' %}bg-red-100 text-red-800 border border-red-200
                                            {% else %}bg-gray-100 text-gray-800 border border-gray-200{% endif %}">
                                            {{ order.get_status_display|default:"Pending" }}
                                        </span>
                                    </div>

                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                                        <div class="flex items-center text-gray-600">
                                            <i class="fas fa-calendar mr-2 text-blue-500"></i>
                                            <span class="text-sm">{{ order.created_at|date:"M d, Y"|default:"Jan 15, 2024" }}</span>
                                        </div>
                                        <div class="flex items-center text-gray-600">
                                            <i class="fas fa-clock mr-2 text-green-500"></i>
                                            <span class="text-sm">{{ order.created_at|time:"H:i"|default:"14:30" }}</span>
                                        </div>
                                        <div class="flex items-center text-gray-600">
                                            <i class="fas fa-box mr-2 text-purple-500"></i>
                                            <span class="text-sm">{{ order.items.count|default:1 }} item{{ order.items.count|pluralize }}</span>
                                        </div>
                                    </div>

                                    <div class="flex items-center justify-between">
                                        <div class="text-2xl font-bold text-harrier-red">
                                            KSh {{ order.total_amount|default:2500000|floatformat:0 }}
                                        </div>
                                        {% if order.payment_status %}
                                            <div class="flex items-center">
                                                <div class="w-2 h-2 bg-green-400 rounded-full mr-2"></div>
                                                <span class="text-sm text-green-600 font-medium">{{ order.payment_status|title }}</span>
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- Enhanced Order Items -->
                            <div class="border-t border-gray-200 pt-6 mt-6">
                                <h4 class="font-bold text-harrier-dark mb-4">Order Items</h4>
                                {% for item in order.items.all %}
                                    <div class="bg-gray-50 rounded-lg p-4 mb-4 hover:bg-gray-100 transition-colors">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                {% if item.car.main_image %}
                                                    <img src="{{ item.car.main_image.url }}" alt="{{ item.car.title }}" class="w-20 h-20 object-cover rounded-lg mr-4 border border-gray-200">
                                                {% else %}
                                                    <div class="w-20 h-20 bg-gray-200 rounded-lg mr-4 flex items-center justify-center border border-gray-300">
                                                        <i class="fas fa-car text-gray-400 text-xl"></i>
                                                    </div>
                                                {% endif %}
                                                <div>
                                                    <h5 class="font-bold text-harrier-dark text-lg">{{ item.car.title|default:"2020 Toyota Camry" }}</h5>
                                                    <p class="text-gray-600 text-sm">{{ item.car.description|truncatewords:10|default:"Premium sedan with excellent fuel economy" }}</p>
                                                    <div class="flex items-center mt-2 space-x-4">
                                                        <span class="text-sm text-gray-600">
                                                            <i class="fas fa-hashtag mr-1"></i>
                                                            Qty: {{ item.quantity|default:1 }}
                                                        </span>
                                                        {% if item.car.year %}
                                                            <span class="text-sm text-gray-600">
                                                                <i class="fas fa-calendar mr-1"></i>
                                                                {{ item.car.year }}
                                                            </span>
                                                        {% endif %}
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="text-right">
                                                <p class="text-xl font-bold text-harrier-red">KSh {{ item.price|default:2500000|floatformat:0 }}</p>
                                                {% if item.original_price and item.original_price != item.price %}
                                                    <p class="text-sm text-gray-500 line-through">KSh {{ item.original_price|floatformat:0 }}</p>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                {% empty %}
                                    <!-- Demo item when no items exist -->
                                    <div class="bg-gray-50 rounded-lg p-4 mb-4">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                <div class="w-20 h-20 bg-gray-200 rounded-lg mr-4 flex items-center justify-center border border-gray-300">
                                                    <i class="fas fa-car text-gray-400 text-xl"></i>
                                                </div>
                                                <div>
                                                    <h5 class="font-bold text-harrier-dark text-lg">2020 Toyota Camry</h5>
                                                    <p class="text-gray-600 text-sm">Premium sedan with excellent fuel economy</p>
                                                    <div class="flex items-center mt-2 space-x-4">
                                                        <span class="text-sm text-gray-600">
                                                            <i class="fas fa-hashtag mr-1"></i>
                                                            Qty: 1
                                                        </span>
                                                        <span class="text-sm text-gray-600">
                                                            <i class="fas fa-calendar mr-1"></i>
                                                            2020
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="text-right">
                                                <p class="text-xl font-bold text-harrier-red">KSh 2,500,000</p>
                                            </div>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>

                            <!-- Enhanced Order Actions -->
                            <div class="border-t border-gray-200 pt-6 mt-6">
                                <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
                                    <div class="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-6">
                                        {% if order.tracking_number %}
                                            <div class="flex items-center text-gray-600">
                                                <i class="fas fa-truck mr-2 text-blue-500"></i>
                                                <span class="text-sm font-medium">Tracking: {{ order.tracking_number|default:"TRK123456789" }}</span>
                                            </div>
                                        {% endif %}
                                        {% if order.estimated_delivery %}
                                            <div class="flex items-center text-gray-600">
                                                <i class="fas fa-calendar mr-2 text-green-500"></i>
                                                <span class="text-sm font-medium">Est. Delivery: {{ order.estimated_delivery|date:"M d, Y"|default:"Jan 25, 2024" }}</span>
                                            </div>
                                        {% endif %}
                                        {% if order.payment_method %}
                                            <div class="flex items-center text-gray-600">
                                                <i class="fas fa-credit-card mr-2 text-purple-500"></i>
                                                <span class="text-sm font-medium">{{ order.payment_method|default:"M-Pesa" }}</span>
                                            </div>
                                        {% endif %}
                                    </div>

                                    <div class="flex items-center space-x-3">
                                        <button onclick="viewOrderDetails({{ order.id }})"
                                                class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center">
                                            <i class="fas fa-eye mr-2"></i>
                                            View Details
                                        </button>

                                        {% if order.status == 'delivered' %}
                                            <button onclick="leaveReview({{ order.id }})"
                                                    class="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center">
                                                <i class="fas fa-star mr-2"></i>
                                                Leave Review
                                            </button>
                                        {% endif %}

                                        {% if order.status in 'pending,processing' %}
                                            <button onclick="cancelOrder({{ order.id }})"
                                                    class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center">
                                                <i class="fas fa-times mr-2"></i>
                                                Cancel Order
                                            </button>
                                        {% endif %}

                                        {% if order.tracking_number %}
                                            <button onclick="trackOrder('{{ order.tracking_number }}')"
                                                    class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center">
                                                <i class="fas fa-map-marker-alt mr-2"></i>
                                                Track
                                            </button>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                    </div>
                </div>
            {% endfor %}
        </div>
        
        <!-- Pagination -->
        <div class="mt-8 flex items-center justify-center">
            <nav class="flex items-center space-x-2">
                <a href="#" class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gray-50 text-gray-700">
                    Previous
                </a>
                <a href="#" class="px-3 py-2 text-sm bg-harrier-red text-white rounded-lg">1</a>
                <a href="#" class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gray-50 text-gray-700">2</a>
                <a href="#" class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gray-50 text-gray-700">3</a>
                <a href="#" class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-lg hover:bg-gray-50 text-gray-700">
                    Next
                </a>
            </nav>
        </div>
        {% else %}
            <!-- Enhanced Empty State -->
            <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-12 text-center">
                <div class="w-24 h-24 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-shopping-bag text-4xl text-blue-500"></i>
                </div>
                <h3 class="text-2xl font-bold text-harrier-dark mb-4">No Orders Yet</h3>
                <p class="text-gray-600 mb-8 max-w-md mx-auto">
                    You haven't placed any orders yet. Start exploring our collection of quality vehicles and spare parts.
                </p>
                <div class="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-4">
                    <a href="{% url 'core:car_list' %}"
                       class="bg-gradient-to-r from-harrier-red to-harrier-dark hover:from-harrier-dark hover:to-harrier-red text-white px-8 py-4 rounded-xl font-bold text-lg transition-all duration-200 inline-flex items-center">
                        <i class="fas fa-car mr-3"></i>
                        Browse Cars
                    </a>
                    <a href="{% url 'core:spare_parts' %}"
                       class="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white px-8 py-4 rounded-xl font-bold text-lg transition-all duration-200 inline-flex items-center">
                        <i class="fas fa-tools mr-3"></i>
                        Shop Parts
                    </a>
                </div>
            </div>
        {% endif %}
    </div>

    <!-- Order Summary Stats -->
    <div class="mt-8 grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="stat-card text-center">
            <div class="stat-value">0</div>
            <div class="stat-label">Total Orders</div>
        </div>
        <div class="stat-card text-center">
            <div class="stat-value">KSh 0</div>
            <div class="stat-label">Total Spent</div>
        </div>
        <div class="stat-card text-center">
            <div class="stat-value">0</div>
            <div class="stat-label">Pending Orders</div>
        </div>
        <div class="stat-card text-center">
            <div class="stat-value">0</div>
            <div class="stat-label">Completed Orders</div>
        </div>
    </div>
{% endblock %}

{% block extra_js %}
<script>
function viewOrderDetails(orderId) {
    // This would typically open a modal or navigate to order details page
    console.log('Viewing order details:', orderId);
    // For demo purposes, show an alert
    alert('Order details functionality would be implemented here');
}

function leaveReview(orderId) {
    // This would typically open a review modal
    console.log('Leaving review for order:', orderId);
    alert('Review functionality would be implemented here');
}

function cancelOrder(orderId) {
    if (confirm('Are you sure you want to cancel this order?')) {
        // This would typically make an AJAX request to cancel the order
        console.log('Cancelling order:', orderId);

        // Show success message
        const orderElement = event.target.closest('.group');
        if (orderElement) {
            const statusBadge = orderElement.querySelector('.rounded-full');
            if (statusBadge) {
                statusBadge.className = 'px-3 py-1 text-sm font-bold rounded-full bg-red-100 text-red-800 border border-red-200';
                statusBadge.textContent = 'Cancelled';
            }
        }
    }
}

function trackOrder(trackingNumber) {
    // This would typically open a tracking modal or navigate to tracking page
    console.log('Tracking order:', trackingNumber);
    alert('Order tracking functionality would be implemented here');
}

document.addEventListener('DOMContentLoaded', function() {
    // Enhanced filtering and search functionality
    const statusFilter = document.querySelector('select[name="status"]');
    const dateFilter = document.querySelector('select[name="date_range"]');
    const searchInput = document.querySelector('input[name="search"]');

    // Add loading states for HTMX requests
    document.addEventListener('htmx:beforeRequest', function(event) {
        const target = event.target;
        if (target.matches('select[name="status"], input[name="search"]')) {
            document.getElementById('orders-list').classList.add('opacity-50');
        }
    });

    document.addEventListener('htmx:afterRequest', function(event) {
        document.getElementById('orders-list').classList.remove('opacity-50');
    });

    // Smooth animations for cards
    const cards = document.querySelectorAll('.group');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('animate-fade-in-up');
    });

    // Export functionality
    const exportButton = document.querySelector('button:contains("Export")');
    if (exportButton) {
        exportButton.addEventListener('click', function() {
            // This would typically trigger a download
            console.log('Exporting orders...');
            alert('Export functionality would be implemented here');
        });
    }
});
</script>

<style>
@keyframes fade-in-up {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in-up {
    animation: fade-in-up 0.6s ease-out forwards;
}

/* Enhanced hover effects */
.group:hover .bg-gray-50 {
    background-color: #F9FAFB;
}

.group:hover img {
    transform: scale(1.05);
    transition: transform 0.3s ease;
}

/* Loading state */
.opacity-50 {
    opacity: 0.5;
    transition: opacity 0.3s ease;
}
</style>
{% endblock %}
