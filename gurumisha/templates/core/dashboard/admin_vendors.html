{% extends 'base_admin_dashboard.html' %}
{% load static %}

{% block dashboard_title %}Vendor Management{% endblock %}
{% block page_title %}Vendor Management{% endblock %}
{% block page_description %}Manage vendor applications and approvals{% endblock %}

{% comment %} {% block sidebar_nav %}
    <li>
        <a href="{% url 'core:dashboard' %}" class="dashboard-nav-link">
            <i class="fas fa-tachometer-alt"></i>
            <span>Dashboard</span>
        </a>
    </li>
    <li>
        <a href="{% url 'core:admin_users' %}" class="dashboard-nav-link">
            <i class="fas fa-users"></i>
            <span>User Management</span>
        </a>
    </li>
    <li>
        <a href="{% url 'core:admin_vendors' %}" class="dashboard-nav-link active">
            <i class="fas fa-store"></i>
            <span>Vendor Management</span>
        </a>
    </li>
    <li>
        <a href="{% url 'core:admin_listings' %}" class="dashboard-nav-link">
            <i class="fas fa-car"></i>
            <span>Car Listings</span>
        </a>
    </li>
    <li>
        <a href="#" class="dashboard-nav-link">
            <i class="fas fa-ship"></i>
            <span>Import Requests</span>
        </a>
    </li>
    <li>
        <a href="#" class="dashboard-nav-link">
            <i class="fas fa-tools"></i>
            <span>Spare Parts</span>
        </a>
    </li>
    <li>
        <a href="#" class="dashboard-nav-link">
            <i class="fas fa-envelope"></i>
            <span>Inquiries</span>
        </a>
    </li>
    <li>
        <a href="#" class="dashboard-nav-link">
            <i class="fas fa-newspaper"></i>
            <span>Content Management</span>
        </a>
    </li>
    <li>
        <a href="{% url 'core:admin_analytics' %}" class="dashboard-nav-link">
            <i class="fas fa-chart-bar"></i>
            <span>Analytics</span>
        </a>
    </li>
    <li>
        <a href="#" class="dashboard-nav-link">
            <i class="fas fa-cog"></i>
            <span>System Settings</span>
        </a>
    </li>
{% endblock %} {% endcomment %}

{% block breadcrumb_items %}
    <li>
        <div class="flex items-center">
            <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
            <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">Vendor Management</span>
        </div>
    </li>
{% endblock %}

{% block dashboard_content %}
    {% csrf_token %}
    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="admin-stat-card text-center">
            <div class="admin-stat-value">{{ total_vendors|default:0 }}</div>
            <div class="admin-stat-label">Total Vendors</div>
        </div>
        <div class="admin-stat-card text-center">
            <div class="admin-stat-value text-green-600">{{ approved_vendors|default:0 }}</div>
            <div class="admin-stat-label">Approved Vendors</div>
        </div>
        <div class="admin-stat-card text-center">
            <div class="admin-stat-value text-orange-600">{{ pending_vendors|default:0 }}</div>
            <div class="admin-stat-label">Pending Approval</div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div class="flex flex-col sm:flex-row gap-4">
                <!-- Status Filter -->
                <div class="flex items-center space-x-2">
                    <label class="text-sm font-medium text-harrier-dark">Status:</label>
                    <select class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-harrier-red focus:border-harrier-red text-sm" 
                            onchange="window.location.href='?status=' + this.value">
                        <option value="">All Vendors</option>
                        <option value="approved" {% if current_filter == 'approved' %}selected{% endif %}>Approved</option>
                        <option value="pending" {% if current_filter == 'pending' %}selected{% endif %}>Pending</option>
                    </select>
                </div>
            </div>

            <!-- Search -->
            <div class="flex items-center space-x-2">
                <form method="GET" class="flex">
                    <input type="text" name="search" placeholder="Search vendors..." 
                           value="{{ request.GET.search }}"
                           class="px-3 py-2 border border-gray-300 rounded-l-lg focus:ring-harrier-red focus:border-harrier-red text-sm">
                    <button type="submit" class="bg-harrier-red text-white px-4 py-2 rounded-r-lg hover:bg-red-600 transition-colors">
                        <i class="fas fa-search"></i>
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Vendors Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-heading font-bold text-harrier-dark">Vendor Applications</h3>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Vendor Details
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Contact Info
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Applied Date
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for vendor in vendors %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-12 w-12">
                                        <div class="h-12 w-12 bg-harrier-red rounded-full flex items-center justify-center text-white font-semibold">
                                            {{ vendor.company_name|first|upper }}
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-harrier-dark">{{ vendor.company_name }}</div>
                                        <div class="text-sm text-gray-500">{{ vendor.user.get_full_name|default:vendor.user.username }}</div>
                                        {% if vendor.business_license %}
                                            <div class="text-xs text-green-600">
                                                <i class="fas fa-certificate mr-1"></i>Licensed
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-harrier-dark">{{ vendor.user.email }}</div>
                                {% if vendor.user.phone %}
                                    <div class="text-sm text-gray-500">{{ vendor.user.phone }}</div>
                                {% endif %}
                                {% if vendor.address %}
                                    <div class="text-xs text-gray-500">{{ vendor.address|truncatechars:30 }}</div>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% if vendor.is_approved %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        <i class="fas fa-check-circle mr-1"></i>Approved
                                    </span>
                                {% else %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                        <i class="fas fa-clock mr-1"></i>Pending
                                    </span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ vendor.created_at|date:"M d, Y" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex items-center space-x-2">
                                    {% if not vendor.is_approved %}
                                        <button onclick="approveVendor({{ vendor.id }})" 
                                                class="text-green-600 hover:text-green-900 transition-colors">
                                            <i class="fas fa-check mr-1"></i>Approve
                                        </button>
                                        <span class="text-gray-300">|</span>
                                    {% endif %}
                                    <a href="#" class="text-harrier-red hover:text-harrier-dark transition-colors">
                                        <i class="fas fa-eye mr-1"></i>View
                                    </a>
                                    <span class="text-gray-300">|</span>
                                    <a href="#" class="text-harrier-red hover:text-harrier-dark transition-colors">
                                        <i class="fas fa-edit mr-1"></i>Edit
                                    </a>
                                </div>
                            </td>
                        </tr>
                    {% empty %}
                        <tr>
                            <td colspan="5" class="px-6 py-12 text-center">
                                <div class="text-gray-500">
                                    <i class="fas fa-store text-4xl mb-4"></i>
                                    <p class="text-lg">No vendors found</p>
                                    <p class="text-sm">No vendor applications match your current filters.</p>
                                </div>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
{% endblock %}

{% block extra_js %}
{{ block.super }}
<script>
function approveVendor(vendorId) {
    if (confirm('Are you sure you want to approve this vendor?')) {
        fetch(`/dashboard/admin/approve-vendor/${vendorId}/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'approved') {
                location.reload();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while approving the vendor.');
        });
    }
}
</script>
{% endblock %}
