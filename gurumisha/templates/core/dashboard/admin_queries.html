{% extends 'base_admin_dashboard.html' %}
{% load static %}

{% block page_title %}Customer Queries{% endblock %}
{% block page_description %}Manage customer inquiries and support requests{% endblock %}

{% block dashboard_content %}
<div class="space-y-8">
    <!-- Queries Header -->
    <div class="flex justify-between items-center animate-fade-in-up" style="animation-delay: 0.1s;">
        <div>
            <h2 class="text-3xl font-bold text-harrier-dark font-montserrat">Customer Queries</h2>
            <p class="text-gray-600 mt-1 font-raleway">Manage customer inquiries and support requests</p>
        </div>
        
        <div class="flex space-x-3">
            <button class="btn-admin-secondary text-sm">
                <i class="fas fa-filter mr-2"></i>Filter
            </button>
            <button class="btn-admin-primary text-sm">
                <i class="fas fa-reply mr-2"></i>Bulk Reply
            </button>
        </div>
    </div>

    <!-- Query Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 animate-fade-in-up" style="animation-delay: 0.2s;">
        <div class="admin-stat-card text-center">
            <div class="admin-stat-value text-violet-600">{{ total_inquiries|default:0 }}</div>
            <div class="admin-stat-label">Total Queries</div>
        </div>
        <div class="admin-stat-card text-center">
            <div class="admin-stat-value text-red-600">{{ new_inquiries|default:0 }}</div>
            <div class="admin-stat-label">New</div>
        </div>
        <div class="admin-stat-card text-center">
            <div class="admin-stat-value text-orange-600">{{ in_progress_inquiries|default:0 }}</div>
            <div class="admin-stat-label">In Progress</div>
        </div>
        <div class="admin-stat-card text-center">
            <div class="admin-stat-value text-green-600">{{ resolved_inquiries|default:0 }}</div>
            <div class="admin-stat-label">Resolved</div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 animate-fade-in-up" style="animation-delay: 0.3s;">
        <div class="quick-action-card">
            <div class="flex items-center">
                <div class="w-14 h-14 bg-gradient-to-br from-red-500 to-red-600 rounded-xl flex items-center justify-center">
                    <i class="fas fa-exclamation-circle text-white text-xl"></i>
                </div>
                <div class="ml-4">
                    <h4 class="font-semibold text-harrier-dark font-raleway">Urgent Queries</h4>
                    <p class="text-sm text-gray-600">{{ new_inquiries|default:0 }} require immediate attention</p>
                </div>
            </div>
        </div>

        <div class="quick-action-card">
            <div class="flex items-center">
                <div class="w-14 h-14 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                    <i class="fas fa-clock text-white text-xl"></i>
                </div>
                <div class="ml-4">
                    <h4 class="font-semibold text-harrier-dark font-raleway">Response Time</h4>
                    <p class="text-sm text-gray-600">Avg: 2.5 hours</p>
                </div>
            </div>
        </div>

        <div class="quick-action-card">
            <div class="flex items-center">
                <div class="w-14 h-14 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center">
                    <i class="fas fa-chart-line text-white text-xl"></i>
                </div>
                <div class="ml-4">
                    <h4 class="font-semibold text-harrier-dark font-raleway">Resolution Rate</h4>
                    <p class="text-sm text-gray-600">95% this month</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Queries List -->
    <div class="admin-card animate-fade-in-up" style="animation-delay: 0.4s;">
        <div class="p-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-gradient-to-br from-violet-500 to-violet-600 rounded-lg flex items-center justify-center mr-3">
                        <i class="fas fa-envelope text-white"></i>
                    </div>
                    <h3 class="text-lg font-bold text-harrier-dark font-montserrat">Recent Queries</h3>
                </div>
                <div class="flex space-x-2">
                    <button class="text-sm text-gray-600 hover:text-harrier-red transition-colors">
                        <i class="fas fa-download mr-1"></i>Export
                    </button>
                    <button class="text-sm text-gray-600 hover:text-harrier-red transition-colors">
                        <i class="fas fa-sync mr-1"></i>Refresh
                    </button>
                </div>
            </div>
        </div>
        
        <div class="p-6">
            {% if inquiries %}
                <div class="space-y-4">
                    {% for inquiry in inquiries %}
                    <div class="query-card border border-gray-200 rounded-lg p-6 hover:border-violet-300 hover:shadow-md transition-all duration-300">
                        <div class="flex items-start justify-between">
                            <div class="flex items-start space-x-4 flex-1">
                                <!-- Customer Avatar -->
                                <div class="w-12 h-12 bg-gradient-to-br from-violet-500 to-violet-600 rounded-full flex items-center justify-center text-white font-semibold">
                                    {{ inquiry.name|first|upper }}
                                </div>
                                
                                <!-- Query Content -->
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center space-x-3 mb-2">
                                        <h4 class="text-lg font-semibold text-harrier-dark font-raleway">{{ inquiry.name }}</h4>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium font-montserrat
                                            {% if inquiry.status == 'new' %}bg-red-100 text-red-800
                                            {% elif inquiry.status == 'in_progress' %}bg-orange-100 text-orange-800
                                            {% elif inquiry.status == 'resolved' %}bg-green-100 text-green-800
                                            {% else %}bg-gray-100 text-gray-800{% endif %}">
                                            {{ inquiry.get_status_display|default:inquiry.status|title }}
                                        </span>
                                        {% if inquiry.priority == 'high' %}
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                            <i class="fas fa-exclamation-triangle mr-1"></i>High Priority
                                        </span>
                                        {% endif %}
                                    </div>
                                    
                                    <p class="text-sm text-gray-600 mb-2">{{ inquiry.email }}</p>
                                    <p class="text-gray-800 font-raleway">{{ inquiry.subject }}</p>
                                    <p class="text-gray-600 mt-2 line-clamp-2">{{ inquiry.message|truncatewords:20 }}</p>
                                    
                                    <!-- Query Meta -->
                                    <div class="flex items-center space-x-4 mt-3 text-sm text-gray-500">
                                        <span><i class="fas fa-clock mr-1"></i>{{ inquiry.created_at|timesince }} ago</span>
                                        {% if inquiry.car %}
                                        <span><i class="fas fa-car mr-1"></i>{{ inquiry.car.title }}</span>
                                        {% endif %}
                                        {% if inquiry.phone %}
                                        <span><i class="fas fa-phone mr-1"></i>{{ inquiry.phone }}</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Actions -->
                            <div class="flex flex-col space-y-2 ml-4">
                                <button class="bg-violet-100 text-violet-700 hover:bg-violet-200 px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                                    <i class="fas fa-reply mr-1"></i>Reply
                                </button>
                                <button class="bg-green-100 text-green-700 hover:bg-green-200 px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                                    <i class="fas fa-check mr-1"></i>Resolve
                                </button>
                                <button class="bg-gray-100 text-gray-700 hover:bg-gray-200 px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                                    <i class="fas fa-eye mr-1"></i>View
                                </button>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <!-- Pagination -->
                <div class="mt-8 flex justify-center">
                    <nav class="flex space-x-2">
                        <button class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                            Previous
                        </button>
                        <button class="px-3 py-2 text-sm font-medium text-white bg-harrier-red border border-harrier-red rounded-md">
                            1
                        </button>
                        <button class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                            2
                        </button>
                        <button class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                            Next
                        </button>
                    </nav>
                </div>
            {% else %}
                <div class="text-center py-12">
                    <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-envelope text-gray-400 text-2xl"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-gray-900 mb-2 font-montserrat">No queries yet</h4>
                    <p class="text-gray-600 font-raleway">Customer inquiries will appear here when they contact you.</p>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
{{ block.super }}
<style>
    /* Query-specific styles */
    .query-card {
        transform: translateY(0);
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .query-card:hover {
        transform: translateY(-2px);
    }

    /* Line clamp for message preview */
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    /* Status badge animations */
    .inline-flex {
        transition: all 0.2s ease;
    }

    .inline-flex:hover {
        transform: scale(1.05);
    }

    /* Action button hover effects */
    .query-card button {
        transition: all 0.2s ease;
    }

    .query-card button:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    /* Mobile responsiveness */
    @media (max-width: 768px) {
        .query-card {
            padding: 1rem;
        }
        
        .query-card .flex {
            flex-direction: column;
            space-y: 1rem;
        }
        
        .query-card .ml-4 {
            margin-left: 0;
            margin-top: 1rem;
        }
    }
</style>
{% endblock %}
