{% extends 'base.html' %}
{% load static %}

{% block title %}Edit Profile - Gurumisha{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="bg-harrier-dark py-12">
    <div class="container mx-auto px-4">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl md:text-4xl font-heading font-bold text-white mb-2">
                    EDIT PROFILE
                </h1>
                <p class="text-lg text-gray-300">
                    Update your account information and preferences
                </p>
            </div>
            <a href="{% url 'core:dashboard' %}" class="btn-harrier-secondary">
                <i class="fas fa-arrow-left mr-2"></i>BACK TO DASHBOARD
            </a>
        </div>
    </div>
</div>

<!-- Profile Edit Form -->
<section class="py-16 bg-harrier-gray">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
            <div class="bg-white rounded-lg shadow-xl overflow-hidden">
                <div class="grid grid-cols-1 lg:grid-cols-3">
                    <!-- Sidebar -->
                    <div class="bg-harrier-dark text-white p-8">
                        <div class="text-center mb-8">
                            <div class="w-24 h-24 bg-harrier-red bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-user text-harrier-red text-3xl"></i>
                            </div>
                            <h3 class="text-xl font-heading font-bold">{{ user.first_name }} {{ user.last_name }}</h3>
                            <p class="text-gray-300">{{ user.get_role_display }}</p>
                        </div>
                        
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-300">Member Since</span>
                                <span class="text-sm font-semibold">{{ user.date_joined|date:"M Y" }}</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-300">Account Status</span>
                                <span class="text-sm font-semibold {% if user.is_verified %}text-green-400{% else %}text-yellow-400{% endif %}">
                                    {% if user.is_verified %}Verified{% else %}Pending{% endif %}
                                </span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-300">Last Login</span>
                                <span class="text-sm font-semibold">{{ user.last_login|date:"M d, Y" }}</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Form -->
                    <div class="lg:col-span-2 p-8">
                        <div class="mb-8">
                            <h2 class="text-2xl font-heading font-bold text-harrier-dark mb-2">Personal Information</h2>
                            <p class="text-gray-600">Update your personal details and contact information</p>
                        </div>

                        <!-- Display Messages -->
                        {% if messages %}
                            {% for message in messages %}
                                <div class="mb-6 p-4 rounded-lg {% if message.tags == 'error' %}bg-red-100 border border-red-400 text-red-700{% elif message.tags == 'success' %}bg-green-100 border border-green-400 text-green-700{% else %}bg-blue-100 border border-blue-400 text-blue-700{% endif %}">
                                    {{ message }}
                                </div>
                            {% endfor %}
                        {% endif %}

                        <form method="post" class="space-y-6">
                            {% csrf_token %}
                            
                            <!-- Name Fields -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-semibold text-harrier-dark mb-2">
                                        First Name <span class="text-harrier-red">*</span>
                                    </label>
                                    <input type="text" name="first_name" value="{{ user.first_name }}" required
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent transition-all duration-200">
                                </div>
                                <div>
                                    <label class="block text-sm font-semibold text-harrier-dark mb-2">
                                        Last Name <span class="text-harrier-red">*</span>
                                    </label>
                                    <input type="text" name="last_name" value="{{ user.last_name }}" required
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent transition-all duration-200">
                                </div>
                            </div>

                            <!-- Contact Information -->
                            <div>
                                <label class="block text-sm font-semibold text-harrier-dark mb-2">
                                    Email Address <span class="text-harrier-red">*</span>
                                </label>
                                <input type="email" name="email" value="{{ user.email }}" required
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent transition-all duration-200">
                            </div>

                            <div>
                                <label class="block text-sm font-semibold text-harrier-dark mb-2">
                                    Phone Number
                                </label>
                                <input type="text" name="phone" value="{{ user.phone }}"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent transition-all duration-200"
                                       placeholder="Enter your phone number">
                            </div>

                            <div>
                                <label class="block text-sm font-semibold text-harrier-dark mb-2">
                                    Address
                                </label>
                                <textarea name="address" rows="3"
                                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent transition-all duration-200"
                                          placeholder="Enter your address">{{ user.address }}</textarea>
                            </div>

                            <!-- Account Type -->
                            <div>
                                <label class="block text-sm font-semibold text-harrier-dark mb-2">
                                    Account Type
                                </label>
                                <select name="role" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent transition-all duration-200">
                                    <option value="customer" {% if user.role == 'customer' %}selected{% endif %}>Customer</option>
                                    <option value="vendor" {% if user.role == 'vendor' %}selected{% endif %}>Vendor</option>
                                </select>
                                <p class="mt-1 text-sm text-gray-500">
                                    Choose "Customer" to buy cars and request imports, or "Vendor" to sell cars and parts.
                                </p>
                            </div>

                            <!-- Submit Button -->
                            <div class="pt-6 border-t border-gray-200">
                                <div class="flex flex-col sm:flex-row gap-4">
                                    <button type="submit" class="flex-1 btn-harrier-primary py-3 text-lg font-semibold">
                                        <i class="fas fa-save mr-2"></i>UPDATE PROFILE
                                    </button>
                                    <a href="{% url 'core:dashboard' %}" class="flex-1 btn-harrier-secondary py-3 text-lg font-semibold text-center">
                                        <i class="fas fa-times mr-2"></i>CANCEL
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Password Change Section -->
<section class="py-16 bg-white">
    <div class="container mx-auto px-4">
        <div class="max-w-2xl mx-auto">
            <div class="bg-harrier-gray rounded-lg p-8">
                <div class="text-center mb-8">
                    <h2 class="text-2xl font-heading font-bold text-harrier-dark mb-2">Change Password</h2>
                    <p class="text-gray-600">Update your account password for better security</p>
                </div>
                
                <form method="post" action="#" class="space-y-6">
                    {% csrf_token %}
                    <input type="hidden" name="form_type" value="password_change">
                    
                    <div>
                        <label class="block text-sm font-semibold text-harrier-dark mb-2">
                            Current Password <span class="text-harrier-red">*</span>
                        </label>
                        <input type="password" name="old_password" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent transition-all duration-200">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-semibold text-harrier-dark mb-2">
                            New Password <span class="text-harrier-red">*</span>
                        </label>
                        <input type="password" name="new_password1" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent transition-all duration-200">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-semibold text-harrier-dark mb-2">
                            Confirm New Password <span class="text-harrier-red">*</span>
                        </label>
                        <input type="password" name="new_password2" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent transition-all duration-200">
                    </div>
                    
                    <div class="pt-4">
                        <button type="submit" class="w-full btn-harrier-primary py-3 text-lg font-semibold">
                            <i class="fas fa-key mr-2"></i>CHANGE PASSWORD
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>
{% endblock %}
