{% extends 'base.html' %}
{% load static %}

{% block title %}Email Verified - Gurumisha Motors{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/auth-pages.css' %}">
<style>
/* Inline Critical CSS for Email Verification Success */
.auth-hero {
    position: relative;
    min-height: 60vh;
    background: linear-gradient(135deg, #10b981 0%, #1F2937 100%);
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
}

.auth-hero-content {
    position: relative;
    z-index: 3;
    text-align: center;
    color: white;
    max-width: 800px;
    padding: 2rem;
}

.auth-hero-title {
    font-family: 'Montserrat', 'Raleway', sans-serif;
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, #ffffff 0%, #f3f4f6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.auth-hero-subtitle {
    font-family: 'Raleway', sans-serif;
    font-size: clamp(1.1rem, 2.5vw, 1.5rem);
    font-weight: 400;
    line-height: 1.6;
    opacity: 0.95;
    margin-bottom: 2rem;
}

.auth-form-container {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 1rem !important;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04), 0 0 40px rgba(16, 185, 129, 0.1) !important;
    overflow: hidden !important;
}

.auth-form-section {
    padding: 3rem;
    position: relative;
    text-align: center;
}

.auth-success-icon {
    width: 6rem !important;
    height: 6rem !important;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin: 0 auto 2rem !important;
    color: white !important;
    font-size: 2.5rem !important;
    animation: successBounce 1s ease-in-out;
    position: relative;
    overflow: hidden;
}

.auth-success-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 100%);
    border-radius: 50%;
}

@keyframes successBounce {
    0% { transform: scale(0); opacity: 0; }
    50% { transform: scale(1.1); opacity: 1; }
    100% { transform: scale(1); opacity: 1; }
}

.auth-form-title {
    font-family: 'Montserrat', sans-serif;
    font-size: 2.5rem;
    font-weight: 700;
    color: #1F2937;
    margin-bottom: 0.75rem;
    line-height: 1.2;
}

.auth-form-subtitle {
    font-family: 'Raleway', sans-serif;
    color: #6B7280;
    font-size: 1.2rem;
    margin-bottom: 2.5rem;
    line-height: 1.5;
}

.auth-action-btn {
    display: inline-block !important;
    padding: 1.25rem 2.5rem !important;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
    color: white !important;
    text-decoration: none !important;
    border-radius: 0.5rem !important;
    font-family: 'Montserrat', sans-serif !important;
    font-size: 1.1rem !important;
    font-weight: 700 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    transition: all 0.25s ease;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06), 0 4px 20px rgba(16, 185, 129, 0.3) !important;
    margin: 0.5rem !important;
}

.auth-action-btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05), 0 8px 30px rgba(16, 185, 129, 0.4) !important;
    background: linear-gradient(135deg, #34d399 0%, #10b981 100%) !important;
}

.auth-secondary-btn {
    display: inline-block !important;
    padding: 1.25rem 2.5rem !important;
    background: transparent !important;
    color: #6B7280 !important;
    text-decoration: none !important;
    border: 2px solid #e5e7eb !important;
    border-radius: 0.5rem !important;
    font-family: 'Montserrat', sans-serif !important;
    font-size: 1.1rem !important;
    font-weight: 700 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    transition: all 0.25s ease;
    margin: 0.5rem !important;
}

.auth-secondary-btn:hover {
    border-color: #DC2626 !important;
    color: #DC2626 !important;
    transform: translateY(-1px) !important;
}

.auth-info-box {
    background: rgba(16, 185, 129, 0.1) !important;
    border: 1px solid rgba(16, 185, 129, 0.2) !important;
    border-radius: 0.75rem !important;
    padding: 2rem !important;
    margin-bottom: 2.5rem !important;
    text-align: left;
}

.auth-info-title {
    font-family: 'Montserrat', sans-serif !important;
    font-size: 1.2rem !important;
    font-weight: 600 !important;
    color: #047857 !important;
    margin-bottom: 1rem !important;
}

.auth-info-text {
    font-family: 'Raleway', sans-serif !important;
    color: #065f46 !important;
    font-size: 1rem !important;
    line-height: 1.6;
}

@media (max-width: 768px) {
    .auth-hero {
        min-height: 50vh !important;
        padding: 2rem 1rem !important;
    }
    
    .auth-hero-content {
        padding: 1rem !important;
    }
    
    .auth-form-container {
        margin: 1rem !important;
        border-radius: 0.75rem !important;
    }
    
    .auth-form-section {
        padding: 2rem 1.5rem !important;
    }
    
    .auth-form-title {
        font-size: 2rem !important;
    }
    
    .auth-action-btn,
    .auth-secondary-btn {
        display: block !important;
        width: 100% !important;
        margin: 0.5rem 0 !important;
    }
}

@media (max-width: 480px) {
    .auth-hero-title {
        font-size: 2rem !important;
    }
    
    .auth-hero-subtitle {
        font-size: 1rem !important;
    }
    
    .auth-form-section {
        padding: 1.5rem 1rem !important;
    }
    
    .auth-form-title {
        font-size: 1.75rem !important;
    }
    
    .auth-success-icon {
        width: 5rem !important;
        height: 5rem !important;
        font-size: 2rem !important;
    }
}
</style>
{% endblock %}

{% block content %}
<!-- Enhanced Hero Section -->
<section class="auth-hero">
    <div class="auth-hero-content">
        <h1 class="auth-hero-title">
            Email Verified Successfully!
        </h1>
        <p class="auth-hero-subtitle">
            Welcome to Gurumisha Motors - Your automotive journey starts now
        </p>
    </div>
</section>

<!-- Email Verification Success Section -->
<section class="py-16 bg-gradient-to-br from-harrier-gray to-white">
    <div class="container mx-auto px-4">
        <div class="max-w-2xl mx-auto">
            <div class="auth-form-container">
                <div class="auth-form-section">
                    <!-- Success Icon -->
                    <div class="auth-success-icon">
                        <i class="fas fa-check"></i>
                    </div>

                    <div class="mb-8">
                        <h2 class="auth-form-title">Verification Complete!</h2>
                        <p class="auth-form-subtitle">
                            Hello {{ user.get_full_name|default:user.username }}, your email has been successfully verified. 
                            You now have full access to all Gurumisha Motors features.
                        </p>
                    </div>

                    <!-- Information Box -->
                    <div class="auth-info-box">
                        <h3 class="auth-info-title">
                            <i class="fas fa-rocket mr-2"></i>What's Next?
                        </h3>
                        <p class="auth-info-text">
                            Now that your email is verified, you can:
                        </p>
                        <ul class="auth-info-text mt-3 space-y-2">
                            <li><i class="fas fa-check mr-2 text-green-600"></i>Browse our extensive car inventory</li>
                            <li><i class="fas fa-check mr-2 text-green-600"></i>Request car imports from international markets</li>
                            <li><i class="fas fa-check mr-2 text-green-600"></i>Access our spare parts marketplace</li>
                            <li><i class="fas fa-check mr-2 text-green-600"></i>Connect with verified dealers and vendors</li>
                            <li><i class="fas fa-check mr-2 text-green-600"></i>Track your orders and requests in real-time</li>
                        </ul>
                    </div>

                    <!-- Action Buttons -->
                    <div class="text-center mb-8">
                        <a href="{% url 'core:login' %}" class="auth-action-btn">
                            <i class="fas fa-sign-in-alt mr-2"></i>Sign In Now
                        </a>
                        <a href="{% url 'core:homepage' %}" class="auth-secondary-btn">
                            <i class="fas fa-home mr-2"></i>Explore Homepage
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}
