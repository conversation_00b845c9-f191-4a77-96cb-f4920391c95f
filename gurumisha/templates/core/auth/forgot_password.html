{% extends 'base.html' %}
{% load static %}

{% block title %}Forgot Password - Gurumisha Motors{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/auth-pages.css' %}">
<style>
/* Inline Critical CSS for Authentication Pages */
.auth-hero {
    position: relative;
    min-height: 60vh;
    background: linear-gradient(135deg, #DC2626 0%, #1F2937 100%);
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
}

.auth-hero-content {
    position: relative;
    z-index: 3;
    text-align: center;
    color: white;
    max-width: 800px;
    padding: 2rem;
}

.auth-hero-title {
    font-family: 'Montserrat', 'Raleway', sans-serif;
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, #ffffff 0%, #f3f4f6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.auth-hero-subtitle {
    font-family: 'Raleway', sans-serif;
    font-size: clamp(1.1rem, 2.5vw, 1.5rem);
    font-weight: 400;
    line-height: 1.6;
    opacity: 0.95;
    margin-bottom: 2rem;
}

.auth-form-container {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 1rem !important;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04), 0 0 40px rgba(220, 38, 38, 0.1) !important;
    overflow: hidden !important;
}

.auth-form-section {
    padding: 3rem;
    position: relative;
}

.auth-form-title {
    font-family: 'Montserrat', sans-serif;
    font-size: 2.25rem;
    font-weight: 700;
    color: #1F2937;
    margin-bottom: 0.75rem;
    line-height: 1.2;
}

.auth-form-subtitle {
    font-family: 'Raleway', sans-serif;
    color: #6B7280;
    font-size: 1.1rem;
    margin-bottom: 2.5rem;
    line-height: 1.5;
}

.auth-form-group {
    margin-bottom: 1.75rem !important;
    position: relative;
}

.auth-form-label {
    font-family: 'Raleway', sans-serif !important;
    font-weight: 600 !important;
    color: #1F2937 !important;
    font-size: 0.95rem !important;
    margin-bottom: 0.5rem !important;
    display: block !important;
}

.auth-form-input {
    width: 100% !important;
    padding: 1rem 1.25rem !important;
    border: 2px solid #e5e7eb !important;
    border-radius: 0.5rem !important;
    font-size: 1rem !important;
    font-family: 'Inter', sans-serif !important;
    background: rgba(255, 255, 255, 0.8) !important;
    transition: all 0.25s ease;
}

.auth-form-input:focus {
    outline: none !important;
    border-color: #DC2626 !important;
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1) !important;
    background: rgba(255, 255, 255, 0.95) !important;
    transform: translateY(-1px);
}

.auth-submit-btn {
    width: 100% !important;
    padding: 1.25rem 2rem !important;
    background: linear-gradient(135deg, #DC2626 0%, #B91C1C 100%) !important;
    color: white !important;
    border: none !important;
    border-radius: 0.5rem !important;
    font-family: 'Montserrat', sans-serif !important;
    font-size: 1.1rem !important;
    font-weight: 700 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    cursor: pointer !important;
    transition: all 0.25s ease;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06), 0 4px 20px rgba(220, 38, 38, 0.3) !important;
}

.auth-submit-btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05), 0 8px 30px rgba(220, 38, 38, 0.4) !important;
    background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%) !important;
}

.auth-info-box {
    background: rgba(59, 130, 246, 0.1) !important;
    border: 1px solid rgba(59, 130, 246, 0.2) !important;
    border-radius: 0.75rem !important;
    padding: 1.5rem !important;
    margin-bottom: 2rem !important;
}

.auth-info-title {
    font-family: 'Montserrat', sans-serif !important;
    font-size: 1.1rem !important;
    font-weight: 600 !important;
    color: #1E3A8A !important;
    margin-bottom: 0.5rem !important;
}

.auth-info-text {
    font-family: 'Raleway', sans-serif !important;
    color: #1E40AF !important;
    font-size: 0.95rem !important;
    line-height: 1.5;
}

@media (max-width: 768px) {
    .auth-hero {
        min-height: 50vh !important;
        padding: 2rem 1rem !important;
    }
    
    .auth-hero-content {
        padding: 1rem !important;
    }
    
    .auth-form-container {
        margin: 1rem !important;
        border-radius: 0.75rem !important;
    }
    
    .auth-form-section {
        padding: 2rem 1.5rem !important;
    }
    
    .auth-form-title {
        font-size: 1.75rem !important;
    }
}

@media (max-width: 480px) {
    .auth-hero-title {
        font-size: 2rem !important;
    }
    
    .auth-hero-subtitle {
        font-size: 1rem !important;
    }
    
    .auth-form-section {
        padding: 1.5rem 1rem !important;
    }
    
    .auth-form-title {
        font-size: 1.5rem !important;
    }
    
    .auth-form-input {
        padding: 0.875rem 1rem !important;
    }
    
    .auth-submit-btn {
        padding: 1rem 1.5rem !important;
        font-size: 1rem !important;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/auth-enhancements.js' %}"></script>
{% endblock %}

{% block content %}
<!-- Enhanced Hero Section -->
<section class="auth-hero">
    <div class="auth-hero-content">
        <h1 class="auth-hero-title">
            Reset Your Password
        </h1>
        <p class="auth-hero-subtitle">
            Enter your email address and we'll send you instructions to reset your password
        </p>
    </div>
</section>

<!-- Forgot Password Form Section -->
<section class="py-16 bg-gradient-to-br from-harrier-gray to-white">
    <div class="container mx-auto px-4">
        <div class="max-w-md mx-auto">
            <div class="auth-form-container">
                <div class="auth-form-section">
                    <div class="mb-8">
                        <h2 class="auth-form-title">Forgot Password</h2>
                        <p class="auth-form-subtitle">We'll send you a secure link to reset your password</p>
                    </div>

                    <!-- Information Box -->
                    <div class="auth-info-box">
                        <h3 class="auth-info-title">
                            <i class="fas fa-info-circle mr-2"></i>How it works
                        </h3>
                        <p class="auth-info-text">
                            Enter your email address below and we'll send you a secure link to reset your password. The link will expire in 24 hours for security.
                        </p>
                    </div>

                    <!-- Display Messages -->
                    {% if messages %}
                        {% for message in messages %}
                            <div class="mb-6 p-4 rounded-lg {% if message.tags == 'error' %}bg-red-100 border border-red-400 text-red-700{% elif message.tags == 'success' %}bg-green-100 border border-green-400 text-green-700{% else %}bg-blue-100 border border-blue-400 text-blue-700{% endif %}">
                                {{ message }}
                            </div>
                        {% endfor %}
                    {% endif %}

                    <form method="post" class="space-y-6" id="forgot-password-form">
                        {% csrf_token %}
                        
                        <!-- Email Field -->
                        <div class="auth-form-group">
                            <label for="{{ form.email.id_for_label }}" class="auth-form-label">
                                <i class="fas fa-envelope mr-2"></i>Email Address
                            </label>
                            <input type="email" 
                                   name="{{ form.email.name }}" 
                                   id="{{ form.email.id_for_label }}"
                                   class="auth-form-input {% if form.email.errors %}error{% endif %}"
                                   placeholder="Enter your email address"
                                   value="{{ form.email.value|default:'' }}"
                                   required>
                            {% if form.email.errors %}
                                <div class="auth-error-message">
                                    <i class="fas fa-exclamation-circle mr-1"></i>{{ form.email.errors.0 }}
                                </div>
                            {% endif %}
                            <p class="mt-2 text-sm text-gray-500">
                                <i class="fas fa-info-circle mr-1"></i>{{ form.email.help_text }}
                            </p>
                        </div>

                        <!-- Submit Button -->
                        <button type="submit" class="auth-submit-btn">
                            <i class="fas fa-paper-plane mr-2"></i>Send Reset Instructions
                        </button>

                        <!-- Non-field errors -->
                        {% if form.non_field_errors %}
                            <div class="auth-error-message text-center p-4 bg-red-50 border border-red-200 rounded-lg">
                                <i class="fas fa-exclamation-triangle mr-2"></i>{{ form.non_field_errors.0 }}
                            </div>
                        {% endif %}
                    </form>

                    <!-- Back to Login Link -->
                    <div class="mt-8 text-center">
                        <p class="text-gray-600">
                            Remember your password? 
                            <a href="{% url 'core:login' %}" class="text-harrier-red hover:text-harrier-dark font-semibold transition-colors">
                                <i class="fas fa-sign-in-alt mr-1"></i>Sign in here
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}
