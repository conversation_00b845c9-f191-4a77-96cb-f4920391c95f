{% extends 'base.html' %}
{% load static %}

{% block title %}Register - <PERSON><PERSON><PERSON>{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/auth-pages.css' %}">
<style>
/* Inline Critical CSS for Authentication Pages */
.auth-hero {
    position: relative;
    min-height: 60vh;
    background: linear-gradient(135deg, #DC2626 0%, #1F2937 100%);
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
}

.auth-hero-content {
    position: relative;
    z-index: 3;
    text-align: center;
    color: white;
    max-width: 800px;
    padding: 2rem;
}

.auth-hero-title {
    font-family: 'Montser<PERSON>', 'Raleway', sans-serif;
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, #ffffff 0%, #f3f4f6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.auth-hero-subtitle {
    font-family: 'Raleway', sans-serif;
    font-size: clamp(1.1rem, 2.5vw, 1.5rem);
    font-weight: 400;
    line-height: 1.6;
    opacity: 0.95;
    margin-bottom: 2rem;
}

.auth-hero-features {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.auth-hero-feature {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.95rem;
    font-weight: 500;
    opacity: 0.9;
}

.auth-form-container {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 1rem !important;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04), 0 0 40px rgba(220, 38, 38, 0.1) !important;
    overflow: hidden !important;
}

.auth-form-section {
    padding: 3rem;
    position: relative;
}

.auth-form-title {
    font-family: 'Montserrat', sans-serif;
    font-size: 2.25rem;
    font-weight: 700;
    color: #1F2937;
    margin-bottom: 0.75rem;
    line-height: 1.2;
}

.auth-form-subtitle {
    font-family: 'Raleway', sans-serif;
    color: #6B7280;
    font-size: 1.1rem;
    margin-bottom: 2.5rem;
    line-height: 1.5;
}

.auth-form-group {
    margin-bottom: 1.75rem !important;
    position: relative;
}

.auth-form-label {
    font-family: 'Raleway', sans-serif !important;
    font-weight: 600 !important;
    color: #1F2937 !important;
    font-size: 0.95rem !important;
    margin-bottom: 0.5rem !important;
    display: block !important;
}

.auth-form-input {
    width: 100% !important;
    padding: 1rem 1.25rem !important;
    border: 2px solid #e5e7eb !important;
    border-radius: 0.5rem !important;
    font-size: 1rem !important;
    font-family: 'Inter', sans-serif !important;
    background: rgba(255, 255, 255, 0.8) !important;
    transition: all 0.25s ease;
}

.auth-form-input:focus {
    outline: none !important;
    border-color: #DC2626 !important;
    box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1) !important;
    background: rgba(255, 255, 255, 0.95) !important;
    transform: translateY(-1px);
}

.auth-submit-btn {
    width: 100% !important;
    padding: 1.25rem 2rem !important;
    background: linear-gradient(135deg, #DC2626 0%, #B91C1C 100%) !important;
    color: white !important;
    border: none !important;
    border-radius: 0.5rem !important;
    font-family: 'Montserrat', sans-serif !important;
    font-size: 1.1rem !important;
    font-weight: 700 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    cursor: pointer !important;
    transition: all 0.25s ease;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06), 0 4px 20px rgba(220, 38, 38, 0.3) !important;
}

.auth-submit-btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05), 0 8px 30px rgba(220, 38, 38, 0.4) !important;
    background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%) !important;
}

.auth-side-panel {
    background: linear-gradient(135deg, #DC2626 0%, #1F2937 100%) !important;
    color: white !important;
    padding: 3rem !important;
    position: relative;
    overflow: hidden;
}

.auth-side-title {
    font-family: 'Montserrat', sans-serif !important;
    font-size: 2rem !important;
    font-weight: 700 !important;
    margin-bottom: 1.5rem !important;
    line-height: 1.2;
}

.auth-side-description {
    font-family: 'Raleway', sans-serif !important;
    font-size: 1.1rem !important;
    line-height: 1.6;
    opacity: 0.95;
    margin-bottom: 2.5rem !important;
}

.auth-features-list {
    list-style: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

.auth-features-item {
    display: flex !important;
    align-items: center !important;
    gap: 1rem !important;
    margin-bottom: 1.25rem !important;
    font-family: 'Raleway', sans-serif !important;
    font-size: 1rem !important;
    opacity: 0.9;
}

.auth-features-icon {
    width: 2.5rem !important;
    height: 2.5rem !important;
    background: rgba(255, 255, 255, 0.15) !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    color: white !important;
    font-size: 1.1rem !important;
    flex-shrink: 0;
}

/* Features Showcase Section */
.auth-features-showcase {
    padding: 5rem 0 !important;
    background: linear-gradient(135deg, #F9FAFB 0%, #ffffff 100%) !important;
    position: relative;
    overflow: hidden;
}

.auth-features-content {
    position: relative;
    z-index: 2;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.auth-features-header {
    text-align: center;
    margin-bottom: 4rem;
}

.auth-features-header h2 {
    font-family: 'Montserrat', sans-serif !important;
    font-size: clamp(2rem, 4vw, 3rem) !important;
    font-weight: 700 !important;
    color: #1F2937 !important;
    margin-bottom: 1rem !important;
    line-height: 1.2;
}

.auth-features-header p {
    font-family: 'Raleway', sans-serif !important;
    font-size: 1.2rem !important;
    color: #6B7280 !important;
    max-width: 600px;
    margin: 0 auto !important;
    line-height: 1.6;
}

.auth-features-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
    gap: 2.5rem !important;
    margin-bottom: 3rem !important;
}

.auth-feature-card {
    background: rgba(255, 255, 255, 0.8) !important;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 1rem !important;
    padding: 2.5rem !important;
    text-align: center;
    position: relative;
    overflow: hidden;
    transition: all 0.25s ease;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

.auth-feature-card:hover {
    transform: translateY(-8px) !important;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
}

.auth-feature-icon-wrapper {
    width: 4rem !important;
    height: 4rem !important;
    background: linear-gradient(135deg, #DC2626 0%, #1E3A8A 100%) !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin: 0 auto 1.5rem !important;
    color: white !important;
    font-size: 1.5rem !important;
    position: relative;
    overflow: hidden;
}

.auth-feature-title {
    font-family: 'Montserrat', sans-serif !important;
    font-size: 1.25rem !important;
    font-weight: 600 !important;
    color: #1F2937 !important;
    margin-bottom: 1rem !important;
    line-height: 1.3;
}

.auth-feature-description {
    font-family: 'Raleway', sans-serif !important;
    color: #6B7280 !important;
    line-height: 1.6;
    font-size: 0.95rem !important;
}

/* Trust Indicators */
.auth-trust-section {
    background: white !important;
    padding: 3rem 0 !important;
    border-top: 1px solid #e5e7eb !important;
}

.auth-trust-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 2rem;
    text-align: center;
}

.auth-trust-stats {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)) !important;
    gap: 2rem !important;
    margin-bottom: 2rem !important;
}

.auth-trust-number {
    font-family: 'Montserrat', sans-serif !important;
    font-size: 2.5rem !important;
    font-weight: 800 !important;
    color: #DC2626 !important;
    display: block !important;
    line-height: 1;
}

.auth-trust-label {
    font-family: 'Raleway', sans-serif !important;
    color: #6B7280 !important;
    font-size: 0.9rem !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    margin-top: 0.5rem !important;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .auth-hero {
        min-height: 50vh !important;
        padding: 2rem 1rem !important;
    }

    .auth-hero-content {
        padding: 1rem !important;
    }

    .auth-hero-features {
        flex-direction: column !important;
        gap: 1rem !important;
    }

    .auth-form-container {
        margin: 1rem !important;
        border-radius: 0.75rem !important;
    }

    .auth-form-section {
        padding: 2rem 1.5rem !important;
    }

    .auth-form-title {
        font-size: 1.75rem !important;
    }

    .auth-side-panel {
        padding: 2rem 1.5rem !important;
    }

    .auth-side-title {
        font-size: 1.5rem !important;
    }

    .auth-features-content {
        padding: 0 1rem !important;
    }

    .auth-features-grid {
        grid-template-columns: 1fr !important;
        gap: 1.5rem !important;
    }

    .auth-feature-card {
        padding: 2rem 1.5rem !important;
    }

    .auth-trust-stats {
        grid-template-columns: repeat(3, 1fr) !important;
        gap: 1rem !important;
    }

    .auth-trust-number {
        font-size: 2rem !important;
    }
}

@media (max-width: 480px) {
    .auth-hero-title {
        font-size: 2rem !important;
    }

    .auth-hero-subtitle {
        font-size: 1rem !important;
    }

    .auth-form-section {
        padding: 1.5rem 1rem !important;
    }

    .auth-form-title {
        font-size: 1.5rem !important;
    }

    .auth-form-input {
        padding: 0.875rem 1rem !important;
    }

    .auth-submit-btn {
        padding: 1rem 1.5rem !important;
        font-size: 1rem !important;
    }

    .auth-side-panel {
        padding: 1.5rem 1rem !important;
    }

    .auth-features-showcase {
        padding: 3rem 0 !important;
    }

    .auth-feature-card {
        padding: 1.5rem 1rem !important;
    }

    .auth-trust-stats {
        grid-template-columns: 1fr !important;
        gap: 1.5rem !important;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/auth-enhancements.js' %}"></script>
{% endblock %}

{% block content %}
<!-- Enhanced Hero Section -->
<section class="auth-hero">
    <div class="auth-hero-content">
        <h1 class="auth-hero-title">
            Join Gurumisha
        </h1>
        <p class="auth-hero-subtitle">
            Create your account and become part of Kenya's most trusted automotive marketplace community
        </p>
        <div class="auth-hero-features">
            <div class="auth-hero-feature">
                <i class="fas fa-rocket"></i>
                <span>Quick Setup</span>
            </div>
            <div class="auth-hero-feature">
                <i class="fas fa-star"></i>
                <span>Premium Access</span>
            </div>
            <div class="auth-hero-feature">
                <i class="fas fa-handshake"></i>
                <span>Trusted Network</span>
            </div>
        </div>
    </div>
</section>

<!-- Enhanced Registration Form Section -->
<section class="py-16 bg-gradient-to-br from-harrier-gray to-white">
    <div class="container mx-auto px-4">
        <div class="max-w-5xl mx-auto">
            <div class="auth-form-container">
                <div class="grid grid-cols-1 lg:grid-cols-2">
                    <!-- Registration Form -->
                    <div class="auth-form-section">
                        <div class="mb-8">
                            <h2 class="auth-form-title">Get Started</h2>
                            <p class="auth-form-subtitle">Create your account to access all premium features and services</p>
                        </div>

                        <!-- Display Messages -->
                        {% if messages %}
                            {% for message in messages %}
                                <div class="mb-6 p-4 rounded-lg {% if message.tags == 'error' %}bg-red-100 border border-red-400 text-red-700{% elif message.tags == 'success' %}bg-green-100 border border-green-400 text-green-700{% else %}bg-blue-100 border border-blue-400 text-blue-700{% endif %}">
                                    {{ message }}
                                </div>
                            {% endfor %}
                        {% endif %}

                        <form method="post" class="space-y-6" id="register-form">
                            {% csrf_token %}

                            <!-- Name Fields -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="auth-form-group">
                                    <label for="{{ form.first_name.id_for_label }}" class="auth-form-label">
                                        <i class="fas fa-user mr-2"></i>First Name
                                    </label>
                                    <input type="text"
                                           name="{{ form.first_name.name }}"
                                           id="{{ form.first_name.id_for_label }}"
                                           class="auth-form-input {% if form.first_name.errors %}error{% endif %}"
                                           placeholder="Enter your first name"
                                           value="{{ form.first_name.value|default:'' }}"
                                           required>
                                    {% if form.first_name.errors %}
                                        <div class="auth-error-message">
                                            <i class="fas fa-exclamation-circle mr-1"></i>{{ form.first_name.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="auth-form-group">
                                    <label for="{{ form.last_name.id_for_label }}" class="auth-form-label">
                                        <i class="fas fa-user mr-2"></i>Last Name
                                    </label>
                                    <input type="text"
                                           name="{{ form.last_name.name }}"
                                           id="{{ form.last_name.id_for_label }}"
                                           class="auth-form-input {% if form.last_name.errors %}error{% endif %}"
                                           placeholder="Enter your last name"
                                           value="{{ form.last_name.value|default:'' }}"
                                           required>
                                    {% if form.last_name.errors %}
                                        <div class="auth-error-message">
                                            <i class="fas fa-exclamation-circle mr-1"></i>{{ form.last_name.errors.0 }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- Username Field -->
                            <div class="auth-form-group">
                                <label for="{{ form.username.id_for_label }}" class="auth-form-label">
                                    <i class="fas fa-at mr-2"></i>Username
                                </label>
                                <input type="text"
                                       name="{{ form.username.name }}"
                                       id="{{ form.username.id_for_label }}"
                                       class="auth-form-input {% if form.username.errors %}error{% endif %}"
                                       placeholder="Choose a unique username"
                                       value="{{ form.username.value|default:'' }}"
                                       required>
                                {% if form.username.errors %}
                                    <div class="auth-error-message">
                                        <i class="fas fa-exclamation-circle mr-1"></i>{{ form.username.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Email Field -->
                            <div class="auth-form-group">
                                <label for="{{ form.email.id_for_label }}" class="auth-form-label">
                                    <i class="fas fa-envelope mr-2"></i>Email Address
                                </label>
                                <input type="email"
                                       name="{{ form.email.name }}"
                                       id="{{ form.email.id_for_label }}"
                                       class="auth-form-input {% if form.email.errors %}error{% endif %}"
                                       placeholder="Enter your email address"
                                       value="{{ form.email.value|default:'' }}"
                                       required>
                                {% if form.email.errors %}
                                    <div class="auth-error-message">
                                        <i class="fas fa-exclamation-circle mr-1"></i>{{ form.email.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Phone Field -->
                            <div class="auth-form-group">
                                <label for="{{ form.phone.id_for_label }}" class="auth-form-label">
                                    <i class="fas fa-phone mr-2"></i>Phone Number
                                </label>
                                <input type="tel"
                                       name="{{ form.phone.name }}"
                                       id="{{ form.phone.id_for_label }}"
                                       class="auth-form-input {% if form.phone.errors %}error{% endif %}"
                                       placeholder="Enter your phone number"
                                       value="{{ form.phone.value|default:'' }}">
                                {% if form.phone.errors %}
                                    <div class="auth-error-message">
                                        <i class="fas fa-exclamation-circle mr-1"></i>{{ form.phone.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Role Field -->
                            <div class="auth-form-group">
                                <label for="{{ form.role.id_for_label }}" class="auth-form-label">
                                    <i class="fas fa-user-tag mr-2"></i>Account Type
                                </label>
                                <select name="{{ form.role.name }}"
                                        id="{{ form.role.id_for_label }}"
                                        class="auth-form-input {% if form.role.errors %}error{% endif %}"
                                        required>
                                    <option value="">Choose your account type</option>
                                    {% for value, label in form.role.field.choices %}
                                        <option value="{{ value }}" {% if form.role.value == value %}selected{% endif %}>{{ label }}</option>
                                    {% endfor %}
                                </select>
                                <p class="mt-2 text-sm text-gray-500">
                                    <i class="fas fa-info-circle mr-1"></i>Choose "Customer" to buy cars and request imports, or "Vendor" to sell cars and parts.
                                </p>
                                {% if form.role.errors %}
                                    <div class="auth-error-message">
                                        <i class="fas fa-exclamation-circle mr-1"></i>{{ form.role.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Password Field -->
                            <div class="auth-form-group">
                                <label for="{{ form.password1.id_for_label }}" class="auth-form-label">
                                    <i class="fas fa-lock mr-2"></i>Create Password
                                </label>
                                <div class="password-input-wrapper">
                                    <input type="password"
                                           name="{{ form.password1.name }}"
                                           id="{{ form.password1.id_for_label }}"
                                           class="auth-form-input {% if form.password1.errors %}error{% endif %}"
                                           placeholder="Create a strong password"
                                           required>
                                    <button type="button" class="password-toggle-btn" data-target="{{ form.password1.id_for_label }}">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>

                                <!-- Password Strength Indicator -->
                                <div class="password-strength-container" id="password-strength-{{ form.password1.id_for_label }}">
                                    <div class="password-strength-bar">
                                        <div class="password-strength-fill"></div>
                                    </div>
                                    <div class="password-strength-text">
                                        <span class="strength-label">Password Strength:</span>
                                        <span class="strength-value">Enter a password</span>
                                    </div>
                                    <div class="password-requirements">
                                        <div class="requirement" data-requirement="length">
                                            <i class="fas fa-circle requirement-icon"></i>
                                            <span>At least 8 characters</span>
                                        </div>
                                        <div class="requirement" data-requirement="uppercase">
                                            <i class="fas fa-circle requirement-icon"></i>
                                            <span>One uppercase letter</span>
                                        </div>
                                        <div class="requirement" data-requirement="lowercase">
                                            <i class="fas fa-circle requirement-icon"></i>
                                            <span>One lowercase letter</span>
                                        </div>
                                        <div class="requirement" data-requirement="number">
                                            <i class="fas fa-circle requirement-icon"></i>
                                            <span>One number</span>
                                        </div>
                                        <div class="requirement" data-requirement="special">
                                            <i class="fas fa-circle requirement-icon"></i>
                                            <span>One special character</span>
                                        </div>
                                    </div>
                                </div>

                                {% if form.password1.errors %}
                                    <div class="auth-error-message">
                                        <i class="fas fa-exclamation-circle mr-1"></i>{{ form.password1.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Confirm Password Field -->
                            <div class="auth-form-group">
                                <label for="{{ form.password2.id_for_label }}" class="auth-form-label">
                                    <i class="fas fa-lock mr-2"></i>Confirm Password
                                </label>
                                <div class="password-input-wrapper">
                                    <input type="password"
                                           name="{{ form.password2.name }}"
                                           id="{{ form.password2.id_for_label }}"
                                           class="auth-form-input {% if form.password2.errors %}error{% endif %}"
                                           placeholder="Confirm your password"
                                           required>
                                    <button type="button" class="password-toggle-btn" data-target="{{ form.password2.id_for_label }}">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>

                                <!-- Password Match Indicator -->
                                <div class="password-match-container" id="password-match-{{ form.password2.id_for_label }}" style="display: none;">
                                    <div class="password-match-indicator">
                                        <i class="fas fa-check-circle match-icon"></i>
                                        <span class="match-text">Passwords match</span>
                                    </div>
                                </div>

                                {% if form.password2.errors %}
                                    <div class="auth-error-message">
                                        <i class="fas fa-exclamation-circle mr-1"></i>{{ form.password2.errors.0 }}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Submit Button -->
                            <button type="submit" class="auth-submit-btn">
                                <i class="fas fa-user-plus mr-2"></i>Create Your Account
                            </button>

                            <!-- Non-field errors -->
                            {% if form.non_field_errors %}
                                <div class="auth-error-message text-center p-4 bg-red-50 border border-red-200 rounded-lg">
                                    <i class="fas fa-exclamation-triangle mr-2"></i>{{ form.non_field_errors.0 }}
                                </div>
                            {% endif %}
                        </form>

                        <!-- Login Link -->
                        <div class="mt-8 text-center">
                            <p class="text-gray-600">
                                Already have an account?
                                <a href="{% url 'core:login' %}" class="text-harrier-red hover:text-harrier-dark font-semibold transition-colors">
                                    <i class="fas fa-sign-in-alt mr-1"></i>Sign in here
                                </a>
                            </p>
                        </div>
                    </div>

                    <!-- Enhanced Side Panel -->
                    <div class="auth-side-panel">
                        <div class="auth-side-content">
                            <div class="mb-8">
                                <h3 class="auth-side-title">Why Join Us?</h3>
                                <p class="auth-side-description">
                                    Become part of Kenya's largest automotive community and enjoy exclusive benefits and premium services.
                                </p>
                            </div>

                            <ul class="auth-features-list">
                                <li class="auth-features-item">
                                    <div class="auth-features-icon">
                                        <i class="fas fa-shield-alt"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold mb-1">Secure & Trusted</h4>
                                        <p class="text-sm opacity-80">All transactions are secure and dealers are verified</p>
                                    </div>
                                </li>

                                <li class="auth-features-item">
                                    <div class="auth-features-icon">
                                        <i class="fas fa-search"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold mb-1">Easy Search</h4>
                                        <p class="text-sm opacity-80">Advanced filters to find exactly what you need</p>
                                    </div>
                                </li>

                                <li class="auth-features-item">
                                    <div class="auth-features-icon">
                                        <i class="fas fa-headset"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold mb-1">24/7 Support</h4>
                                        <p class="text-sm opacity-80">Our team is always ready to help you</p>
                                    </div>
                                </li>

                                <li class="auth-features-item">
                                    <div class="auth-features-icon">
                                        <i class="fas fa-globe"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold mb-1">Global Network</h4>
                                        <p class="text-sm opacity-80">Access to international car markets</p>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Registration Benefits Section -->
<section class="auth-features-showcase">
    <div class="auth-features-content">
        <div class="auth-features-header">
            <h2>Start Your Automotive Journey Today</h2>
            <p>Join our community and unlock exclusive benefits designed for automotive enthusiasts</p>
        </div>

        <div class="auth-features-grid">
            <div class="auth-feature-card">
                <div class="auth-feature-icon-wrapper">
                    <i class="fas fa-car-side"></i>
                </div>
                <h3 class="auth-feature-title">Exclusive Inventory</h3>
                <p class="auth-feature-description">
                    Access to premium vehicles and exclusive listings not available to the general public.
                </p>
            </div>

            <div class="auth-feature-card">
                <div class="auth-feature-icon-wrapper">
                    <i class="fas fa-shipping-fast"></i>
                </div>
                <h3 class="auth-feature-title">Priority Import Service</h3>
                <p class="auth-feature-description">
                    Fast-track your import requests with priority processing and dedicated support team.
                </p>
            </div>

            <div class="auth-feature-card">
                <div class="auth-feature-icon-wrapper">
                    <i class="fas fa-percentage"></i>
                </div>
                <h3 class="auth-feature-title">Member Discounts</h3>
                <p class="auth-feature-description">
                    Enjoy special pricing on spare parts, services, and exclusive member-only deals.
                </p>
            </div>
        </div>

        <!-- Registration Trust Indicators -->
        <div class="auth-trust-section">
            <div class="auth-trust-content">
                <div class="auth-trust-stats">
                    <div class="auth-trust-stat">
                        <span class="auth-trust-number">10,000+</span>
                        <span class="auth-trust-label">Active Members</span>
                    </div>
                    <div class="auth-trust-stat">
                        <span class="auth-trust-number">98%</span>
                        <span class="auth-trust-label">Satisfaction Rate</span>
                    </div>
                    <div class="auth-trust-stat">
                        <span class="auth-trust-number">24/7</span>
                        <span class="auth-trust-label">Support Available</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}
