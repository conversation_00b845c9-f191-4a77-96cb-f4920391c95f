<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Password Reset Code - {{ site_name }}</title>
    <style>
        /* Email-safe CSS */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .email-header {
            background: linear-gradient(135deg, #F59E0B 0%, #1F2937 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .email-header h1 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: 700;
        }
        
        .email-body {
            padding: 2rem;
        }
        
        .verification-code-box {
            background: linear-gradient(135deg, #FEF3C7 0%, #FDE68A 100%);
            border: 2px solid #F59E0B;
            border-radius: 12px;
            padding: 2rem;
            text-align: center;
            margin: 2rem 0;
        }
        
        .verification-code {
            font-size: 2.5rem;
            font-weight: 900;
            color: #D97706;
            letter-spacing: 0.5rem;
            font-family: 'Courier New', monospace;
            margin: 1rem 0;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
        }
        
        .code-label {
            font-size: 1.1rem;
            font-weight: 600;
            color: #92400E;
            margin-bottom: 0.5rem;
        }
        
        .expiry-notice {
            background-color: #FEE2E2;
            border: 1px solid #EF4444;
            border-radius: 8px;
            padding: 1rem;
            margin: 1.5rem 0;
            color: #991B1B;
        }
        
        .instructions {
            background-color: #EFF6FF;
            border: 1px solid #3B82F6;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1.5rem 0;
        }
        
        .instructions h3 {
            color: #1E40AF;
            margin-top: 0;
            font-size: 1.1rem;
        }
        
        .instructions ol {
            margin: 0.5rem 0;
            padding-left: 1.5rem;
        }
        
        .instructions li {
            margin: 0.5rem 0;
            color: #1F2937;
        }
        
        .security-notice {
            background-color: #FEE2E2;
            border: 1px solid #EF4444;
            border-radius: 8px;
            padding: 1rem;
            margin: 1.5rem 0;
            color: #991B1B;
        }
        
        .email-footer {
            background-color: #F9FAFB;
            padding: 1.5rem;
            text-align: center;
            border-top: 1px solid #E5E7EB;
            color: #6B7280;
        }
        
        .brand-logo {
            font-size: 1.2rem;
            font-weight: 700;
            color: #DC2626;
        }
        
        @media (max-width: 600px) {
            .email-container {
                margin: 0;
                border-radius: 0;
            }
            
            .email-header, .email-body {
                padding: 1.5rem 1rem;
            }
            
            .verification-code {
                font-size: 2rem;
                letter-spacing: 0.3rem;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Email Header -->
        <div class="email-header">
            <h1>🔑 Password Reset</h1>
            <p style="margin: 0.5rem 0 0 0; opacity: 0.9;">{{ site_name }}</p>
        </div>

        <!-- Email Body -->
        <div class="email-body">
            <h2>Hello {{ user.get_full_name|default:user.username }},</h2>
            
            <p>We received a request to reset your password for your {{ site_name }} account. Use the verification code below to proceed with resetting your password:</p>

            <!-- Verification Code Box -->
            <div class="verification-code-box">
                <div class="code-label">Password Reset Code</div>
                <div class="verification-code">{{ verification_code }}</div>
                <p style="margin: 0.5rem 0 0 0; color: #92400E; font-size: 0.9rem;">
                    Enter this code exactly as shown
                </p>
            </div>

            <!-- Instructions -->
            <div class="instructions">
                <h3>📋 How to reset your password:</h3>
                <ol>
                    <li>Go back to the password reset page on {{ site_name }}</li>
                    <li>Enter the 6-digit code shown above</li>
                    <li>Create your new secure password</li>
                    <li>Log in with your new password</li>
                </ol>
            </div>

            <!-- Expiry Notice -->
            <div class="expiry-notice">
                <strong>⏰ Important:</strong> This password reset code will expire in <strong>{{ expiry_minutes }} minutes</strong> for your security.
            </div>

            <!-- Security Notice -->
            <div class="security-notice">
                <strong>🛡️ Security Notice:</strong> If you didn't request a password reset, please ignore this email and your password will remain unchanged. Consider changing your password if you suspect unauthorized access.
            </div>

            <p>If you have any questions or need assistance, our support team is here to help!</p>

            <p>Best regards,<br>
            <strong>The {{ site_name }} Team</strong></p>
        </div>

        <!-- Email Footer -->
        <div class="email-footer">
            <div class="brand-logo">{{ site_name }}</div>
            <p style="margin: 0.5rem 0 0 0; font-size: 0.9rem;">
                Your trusted automotive partner
            </p>
        </div>
    </div>
</body>
</html>
