{% extends 'base.html' %}
{% load static %}

{% block title %}Contact Us - <PERSON>misha{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="bg-harrier-dark py-16">
    <div class="container mx-auto px-4">
        <div class="text-center">
            <h1 class="text-4xl md:text-5xl font-heading font-bold text-white mb-4">
                CONTACT US
            </h1>
            <p class="text-xl text-gray-300">
                Get in touch with our automotive experts - we're here to help you find your perfect car
            </p>
        </div>
    </div>
</div>

<!-- Contact Section -->
<section class="py-16 bg-harrier-gray">
    <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <!-- Contact Information -->
            <div>
                <h2 class="text-3xl font-heading font-bold text-harrier-dark mb-8">Get in Touch</h2>

                <div class="space-y-6 mb-8">
                    <div class="flex items-start">
                        <div class="w-12 h-12 bg-harrier-red rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                            <i class="fas fa-map-marker-alt text-white text-xl"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold text-harrier-dark mb-1">Our Location</h3>
                            <p class="text-gray-600">Westlands Business District<br>Nairobi, Kenya<br>P.O. Box 12345-00100</p>
                        </div>
                    </div>

                    <div class="flex items-start">
                        <div class="w-12 h-12 bg-harrier-red rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                            <i class="fas fa-phone text-white text-xl"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold text-harrier-dark mb-1">Phone Numbers</h3>
                            <p class="text-gray-600">Sales: +254 700 000 000<br>Support: +254 711 000 000<br>Import Desk: +254 722 000 000</p>
                        </div>
                    </div>

                    <div class="flex items-start">
                        <div class="w-12 h-12 bg-harrier-red rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                            <i class="fas fa-envelope text-white text-xl"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold text-harrier-dark mb-1">Email Addresses</h3>
                            <p class="text-gray-600"><EMAIL><br><EMAIL><br><EMAIL></p>
                        </div>
                    </div>

                    <div class="flex items-start">
                        <div class="w-12 h-12 bg-harrier-red rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                            <i class="fas fa-clock text-white text-xl"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold text-harrier-dark mb-1">Business Hours</h3>
                            <p class="text-gray-600">Monday - Friday: 8:00 AM - 6:00 PM<br>Saturday: 9:00 AM - 4:00 PM<br>Sunday: Closed</p>
                        </div>
                    </div>
                </div>
                
                <!-- Social Media -->
                <div>
                    <h3 class="font-semibold text-harrier-dark mb-4">Follow Us</h3>
                    <div class="flex space-x-4">
                        <a href="#" class="w-10 h-10 bg-blue-600 text-white rounded-full flex items-center justify-center hover:bg-blue-700 transition-colors">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-blue-400 text-white rounded-full flex items-center justify-center hover:bg-blue-500 transition-colors">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-pink-600 text-white rounded-full flex items-center justify-center hover:bg-pink-700 transition-colors">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-blue-700 text-white rounded-full flex items-center justify-center hover:bg-blue-800 transition-colors">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Contact Form -->
            <div>
                <div class="bg-white rounded-2xl shadow-xl p-8">
                    <h2 class="text-2xl font-heading font-bold text-harrier-dark mb-6">Send us a Message</h2>
                    
                    <!-- Display Messages -->
                    {% if messages %}
                        {% for message in messages %}
                            <div class="mb-6 p-4 rounded-lg {% if message.tags == 'error' %}bg-red-100 border border-red-400 text-red-700{% elif message.tags == 'success' %}bg-green-100 border border-green-400 text-green-700{% else %}bg-blue-100 border border-blue-400 text-blue-700{% endif %}">
                                {{ message }}
                            </div>
                        {% endfor %}
                    {% endif %}

                    <form method="post" class="space-y-6">
                        {% csrf_token %}

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="{{ form.first_name.id_for_label }}" class="block text-sm font-semibold text-harrier-dark mb-2">
                                    First Name <span class="text-harrier-red">*</span>
                                </label>
                                {{ form.first_name }}
                                {% if form.first_name.errors %}
                                    <div class="mt-1 text-sm text-red-600">{{ form.first_name.errors.0 }}</div>
                                {% endif %}
                            </div>
                            <div>
                                <label for="{{ form.last_name.id_for_label }}" class="block text-sm font-semibold text-harrier-dark mb-2">
                                    Last Name <span class="text-harrier-red">*</span>
                                </label>
                                {{ form.last_name }}
                                {% if form.last_name.errors %}
                                    <div class="mt-1 text-sm text-red-600">{{ form.last_name.errors.0 }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <div>
                            <label for="{{ form.email.id_for_label }}" class="block text-sm font-semibold text-harrier-dark mb-2">
                                Email Address <span class="text-harrier-red">*</span>
                            </label>
                            {{ form.email }}
                            {% if form.email.errors %}
                                <div class="mt-1 text-sm text-red-600">{{ form.email.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <div>
                            <label for="{{ form.phone.id_for_label }}" class="block text-sm font-semibold text-harrier-dark mb-2">
                                Phone Number
                            </label>
                            {{ form.phone }}
                            {% if form.phone.errors %}
                                <div class="mt-1 text-sm text-red-600">{{ form.phone.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <div>
                            <label for="{{ form.subject.id_for_label }}" class="block text-sm font-semibold text-harrier-dark mb-2">
                                Subject <span class="text-harrier-red">*</span>
                            </label>
                            {{ form.subject }}
                            {% if form.subject.errors %}
                                <div class="mt-1 text-sm text-red-600">{{ form.subject.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <div>
                            <label for="{{ form.message.id_for_label }}" class="block text-sm font-semibold text-harrier-dark mb-2">
                                Message <span class="text-harrier-red">*</span>
                            </label>
                            {{ form.message }}
                            {% if form.message.errors %}
                                <div class="mt-1 text-sm text-red-600">{{ form.message.errors.0 }}</div>
                            {% endif %}
                        </div>

                        <div class="pt-4">
                            <button type="submit" class="w-full btn-harrier-primary py-4 text-lg font-semibold">
                                <i class="fas fa-paper-plane mr-2"></i>SEND MESSAGE
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="py-16 bg-gray-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-heading font-bold text-midnight mb-4">Frequently Asked Questions</h2>
            <p class="text-lg text-gray-600">Quick answers to common questions</p>
        </div>
        
        <div class="space-y-6">
            <div class="bg-white rounded-xl p-6 shadow-md">
                <button class="w-full text-left flex justify-between items-center" onclick="toggleFAQ(1)">
                    <h3 class="text-lg font-semibold text-midnight">How do I buy a car through Gurumisha?</h3>
                    <i class="fas fa-chevron-down text-gray-400 transform transition-transform" id="faq-icon-1"></i>
                </button>
                <div class="mt-4 text-gray-600 hidden" id="faq-content-1">
                    <p>Browse our car listings, contact the dealer directly through our platform, arrange for inspection, and complete the purchase. We facilitate the entire process to ensure a smooth transaction.</p>
                </div>
            </div>
            
            <div class="bg-white rounded-xl p-6 shadow-md">
                <button class="w-full text-left flex justify-between items-center" onclick="toggleFAQ(2)">
                    <h3 class="text-lg font-semibold text-midnight">Are all dealers on your platform verified?</h3>
                    <i class="fas fa-chevron-down text-gray-400 transform transition-transform" id="faq-icon-2"></i>
                </button>
                <div class="mt-4 text-gray-600 hidden" id="faq-content-2">
                    <p>Yes, all dealers undergo a thorough verification process including business license checks, background verification, and quality assessments before being approved on our platform.</p>
                </div>
            </div>
            
            <div class="bg-white rounded-xl p-6 shadow-md">
                <button class="w-full text-left flex justify-between items-center" onclick="toggleFAQ(3)">
                    <h3 class="text-lg font-semibold text-midnight">Do you offer car import services?</h3>
                    <i class="fas fa-chevron-down text-gray-400 transform transition-transform" id="faq-icon-3"></i>
                </button>
                <div class="mt-4 text-gray-600 hidden" id="faq-content-3">
                    <p>Yes, we offer comprehensive car import services from Japan, UK, and other countries. Our team handles all documentation, shipping, and customs clearance processes.</p>
                </div>
            </div>
            
            <div class="bg-white rounded-xl p-6 shadow-md">
                <button class="w-full text-left flex justify-between items-center" onclick="toggleFAQ(4)">
                    <h3 class="text-lg font-semibold text-midnight">How can I sell my car on your platform?</h3>
                    <i class="fas fa-chevron-down text-gray-400 transform transition-transform" id="faq-icon-4"></i>
                </button>
                <div class="mt-4 text-gray-600 hidden" id="faq-content-4">
                    <p>Contact us to become a verified seller. Once approved, you can list your vehicle with photos and detailed information. We'll help you reach potential buyers through our platform.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Map Section (Placeholder) -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-8">
            <h2 class="text-3xl font-heading font-bold text-midnight mb-4">Visit Our Showroom</h2>
            <p class="text-lg text-gray-600">Come see our vehicles in person</p>
        </div>
        
        <div class="bg-gray-200 rounded-2xl h-96 flex items-center justify-center">
            <div class="text-center">
                <i class="fas fa-map-marker-alt text-gray-400 text-6xl mb-4"></i>
                <p class="text-gray-600">Interactive map coming soon</p>
                <p class="text-sm text-gray-500 mt-2">Westlands, Nairobi, Kenya</p>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
    function toggleFAQ(id) {
        const content = document.getElementById(`faq-content-${id}`);
        const icon = document.getElementById(`faq-icon-${id}`);
        
        if (content.classList.contains('hidden')) {
            content.classList.remove('hidden');
            icon.classList.add('rotate-180');
        } else {
            content.classList.add('hidden');
            icon.classList.remove('rotate-180');
        }
    }
</script>
{% endblock %}
