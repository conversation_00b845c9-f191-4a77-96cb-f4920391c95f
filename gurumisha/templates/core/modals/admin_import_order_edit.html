{% load static %}

<!-- Edit Import Order Modal -->
<div class="fixed inset-0 z-50 overflow-y-auto" 
     id="edit-import-order-modal"
     x-data="{ show: false }"
     x-init="show = true"
     x-show="show"
     x-transition:enter="ease-out duration-300"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100"
     x-transition:leave="ease-in duration-200"
     x-transition:leave-start="opacity-100"
     x-transition:leave-end="opacity-0">
    
    <!-- Modal Backdrop -->
    <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity modal-backdrop"
         @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)"></div>
    
    <!-- Modal Panel -->
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="relative bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden modal-panel"
             x-show="show"
             x-transition:enter="ease-out duration-300"
             x-transition:enter-start="opacity-0 scale-95"
             x-transition:enter-end="opacity-100 scale-100"
             x-transition:leave="ease-in duration-200"
             x-transition:leave-start="opacity-100 scale-100"
             x-transition:leave-end="opacity-0 scale-95">
            
            <!-- Modal Header -->
            <div class="bg-gradient-to-r from-harrier-red via-harrier-dark to-harrier-red px-6 py-4 rounded-t-2xl modal-header">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-white bg-opacity-20 rounded-xl flex items-center justify-center backdrop-blur-sm">
                            <i class="fas fa-edit text-white text-lg"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-white font-montserrat">Edit Import Order</h3>
                            <p class="text-white text-opacity-90 text-sm font-raleway">Order #{{ import_order.order_number }}</p>
                        </div>
                    </div>
                    <button type="button" 
                            class="text-white hover:text-gray-200 transition-colors duration-200 p-2 hover:bg-white hover:bg-opacity-10 rounded-lg"
                            @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            
            <!-- Modal Body -->
            <div class="bg-gradient-to-br from-gray-50 to-white px-6 py-6 max-h-96 overflow-y-auto modal-body">
                <form id="edit-import-order-form"
                      hx-post="{% url 'core:admin_import_order_edit' import_order.id %}"
                      hx-target="#tracking-management-table"
                      hx-swap="outerHTML"
                      hx-on::after-request="if(event.detail.successful) { show = false; setTimeout(() => $el.closest('.fixed').remove(), 200); }"
                      class="space-y-6"
                      x-data="{ isSubmitting: false }"
                      @submit="isSubmitting = true">
                    {% csrf_token %}
                    
                    <!-- Customer Information (Read-only) -->
                    <div class="bg-white rounded-xl p-4 border border-gray-200 shadow-sm">
                        <h4 class="text-lg font-semibold text-harrier-dark mb-4 flex items-center">
                            <i class="fas fa-user mr-2 text-blue-600"></i>
                            Customer Information
                        </h4>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-harrier-red rounded-full flex items-center justify-center text-white font-semibold mr-4">
                                    {{ import_order.customer.first_name|first|default:import_order.customer.username|first|upper }}
                                </div>
                                <div>
                                    <div class="font-semibold text-gray-900 font-montserrat">{{ import_order.customer.get_full_name|default:import_order.customer.username }}</div>
                                    <div class="text-sm text-gray-600">{{ import_order.customer.email }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Vehicle Information -->
                    <div class="bg-white rounded-xl p-4 border border-gray-200 shadow-sm">
                        <h4 class="text-lg font-semibold text-harrier-dark mb-4 flex items-center">
                            <i class="fas fa-car mr-2 text-harrier-red"></i>
                            Vehicle Details
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="form-group">
                                <label for="brand" class="form-label">
                                    <i class="fas fa-tag mr-1 text-harrier-red"></i>
                                    Brand <span class="text-red-500">*</span>
                                </label>
                                <input type="text" name="brand" id="brand" required 
                                       value="{{ import_order.brand }}"
                                       class="form-input" placeholder="e.g., Toyota, Honda, BMW">
                            </div>
                            
                            <div class="form-group">
                                <label for="model" class="form-label">
                                    <i class="fas fa-car-side mr-1 text-harrier-red"></i>
                                    Model <span class="text-red-500">*</span>
                                </label>
                                <input type="text" name="model" id="model" required 
                                       value="{{ import_order.model }}"
                                       class="form-input" placeholder="e.g., Camry, Civic, X5">
                            </div>
                            
                            <div class="form-group">
                                <label for="year" class="form-label">
                                    <i class="fas fa-calendar mr-1 text-harrier-red"></i>
                                    Year <span class="text-red-500">*</span>
                                </label>
                                <input type="number" name="year" id="year" required min="1990" max="2025"
                                       value="{{ import_order.year }}"
                                       class="form-input" placeholder="2020">
                            </div>
                            
                            <div class="form-group">
                                <label for="color" class="form-label">
                                    <i class="fas fa-palette mr-1 text-harrier-red"></i>
                                    Color
                                </label>
                                <input type="text" name="color" id="color" 
                                       value="{{ import_order.color }}"
                                       class="form-input" placeholder="e.g., White, Black, Silver">
                            </div>
                            
                            <div class="form-group">
                                <label for="engine_size" class="form-label">
                                    <i class="fas fa-cog mr-1 text-harrier-red"></i>
                                    Engine Size
                                </label>
                                <input type="text" name="engine_size" id="engine_size" 
                                       value="{{ import_order.engine_size }}"
                                       class="form-input" placeholder="e.g., 2.0L, 3.5L">
                            </div>
                            
                            <div class="form-group">
                                <label for="fuel_type" class="form-label">
                                    <i class="fas fa-gas-pump mr-1 text-harrier-red"></i>
                                    Fuel Type
                                </label>
                                <select name="fuel_type" id="fuel_type" class="form-input">
                                    <option value="">Select fuel type...</option>
                                    <option value="Petrol" {% if import_order.fuel_type == 'Petrol' %}selected{% endif %}>Petrol</option>
                                    <option value="Diesel" {% if import_order.fuel_type == 'Diesel' %}selected{% endif %}>Diesel</option>
                                    <option value="Hybrid" {% if import_order.fuel_type == 'Hybrid' %}selected{% endif %}>Hybrid</option>
                                    <option value="Electric" {% if import_order.fuel_type == 'Electric' %}selected{% endif %}>Electric</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Order Details -->
                    <div class="bg-white rounded-xl p-4 border border-gray-200 shadow-sm">
                        <h4 class="text-lg font-semibold text-harrier-dark mb-4 flex items-center">
                            <i class="fas fa-clipboard-list mr-2 text-green-600"></i>
                            Order Information
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="form-group">
                                <label for="total_cost" class="form-label">
                                    <i class="fas fa-money-bill-wave mr-1 text-green-500"></i>
                                    Total Cost (KES) <span class="text-red-500">*</span>
                                </label>
                                <div class="relative">
                                    <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">KES</span>
                                    <input type="number" name="total_cost" id="total_cost" required min="0" step="1000"
                                           value="{{ import_order.total_cost }}"
                                           class="form-input pl-12" placeholder="2,500,000">
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="status" class="form-label">
                                    <i class="fas fa-flag mr-1 text-blue-500"></i>
                                    Current Status <span class="text-red-500">*</span>
                                </label>
                                <select name="status" id="status" required class="form-input">
                                    {% for choice in status_choices %}
                                    <option value="{{ choice.0 }}" {% if import_order.status == choice.0 %}selected{% endif %}>{{ choice.1 }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Shipping Information -->
                    <div class="bg-white rounded-xl p-4 border border-gray-200 shadow-sm">
                        <h4 class="text-lg font-semibold text-harrier-dark mb-4 flex items-center">
                            <i class="fas fa-ship mr-2 text-purple-600"></i>
                            Shipping Details
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="form-group">
                                <label for="chassis_number" class="form-label">
                                    <i class="fas fa-barcode mr-1 text-purple-500"></i>
                                    Chassis Number
                                </label>
                                <input type="text" name="chassis_number" id="chassis_number" 
                                       value="{{ import_order.chassis_number }}"
                                       class="form-input" placeholder="Enter chassis number">
                            </div>
                            
                            <div class="form-group">
                                <label for="bill_of_lading" class="form-label">
                                    <i class="fas fa-file-alt mr-1 text-purple-500"></i>
                                    Bill of Lading
                                </label>
                                <input type="text" name="bill_of_lading" id="bill_of_lading" 
                                       value="{{ import_order.bill_of_lading }}"
                                       class="form-input" placeholder="Enter B/L number">
                            </div>
                            
                            <div class="form-group">
                                <label for="vessel_name" class="form-label">
                                    <i class="fas fa-ship mr-1 text-purple-500"></i>
                                    Vessel Name
                                </label>
                                <input type="text" name="vessel_name" id="vessel_name" 
                                       value="{{ import_order.vessel_name }}"
                                       class="form-input" placeholder="Enter vessel name">
                            </div>
                            
                            <div class="form-group">
                                <label for="estimated_arrival" class="form-label">
                                    <i class="fas fa-calendar-alt mr-1 text-purple-500"></i>
                                    Estimated Arrival
                                </label>
                                <input type="date" name="estimated_arrival" id="estimated_arrival" 
                                       value="{{ import_order.estimated_arrival|date:'Y-m-d' }}"
                                       class="form-input">
                            </div>
                        </div>
                    </div>
                    
                    <!-- Additional Information -->
                    <div class="bg-white rounded-xl p-4 border border-gray-200 shadow-sm">
                        <h4 class="text-lg font-semibold text-harrier-dark mb-4 flex items-center">
                            <i class="fas fa-info-circle mr-2 text-orange-600"></i>
                            Additional Details
                        </h4>
                        <div class="form-group">
                            <label for="admin_notes" class="form-label">
                                <i class="fas fa-sticky-note mr-1 text-orange-500"></i>
                                Admin Notes
                            </label>
                            <textarea name="admin_notes" id="admin_notes" rows="3" 
                                      class="form-input" 
                                      placeholder="Add any relevant notes about this import order...">{{ import_order.admin_notes }}</textarea>
                        </div>
                    </div>
                </form>
            </div>
            
            <!-- Modal Footer -->
            <div class="bg-gradient-to-r from-gray-50 via-white to-gray-50 px-6 py-6 rounded-b-2xl border-t border-gray-200">
                <div class="flex flex-col sm:flex-row justify-end items-center gap-3">
                    <!-- Cancel Button -->
                    <button type="button" 
                            class="enhanced-btn enhanced-btn-cancel"
                            @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)">
                        <div class="btn-content">
                            <div class="btn-icon-wrapper">
                                <i class="btn-icon fas fa-times"></i>
                            </div>
                            <span class="btn-text">Cancel</span>
                        </div>
                        <div class="btn-ripple"></div>
                    </button>
                    
                    <!-- Submit Button -->
                    <button type="submit" 
                            form="edit-import-order-form"
                            class="enhanced-btn enhanced-btn-submit"
                            x-bind:disabled="isSubmitting"
                            x-bind:class="{ 'opacity-75 cursor-not-allowed': isSubmitting }">
                        <div class="btn-content">
                            <div class="btn-icon-wrapper">
                                <i class="btn-icon fas fa-save" x-show="!isSubmitting"></i>
                                <i class="btn-icon fas fa-spinner fa-spin" x-show="isSubmitting"></i>
                            </div>
                            <span class="btn-text" x-text="isSubmitting ? 'Saving...' : 'Save Changes'"></span>
                        </div>
                        <div class="btn-ripple"></div>
                        <div class="btn-shine"></div>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    /* Enhanced Modal Styles */
    .modal-panel {
        backdrop-filter: blur(20px);
        background: rgba(255, 255, 255, 0.98);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    }

    /* Form Styling */
    .form-group {
        @apply space-y-2;
    }

    .form-label {
        @apply block text-sm font-semibold text-gray-700 flex items-center;
        font-family: 'Montserrat', sans-serif;
    }

    .form-input {
        @apply w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-harrier-blue focus:border-transparent transition-all duration-300;
        font-family: 'Raleway', sans-serif;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
    }

    .form-input:focus {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
    }

    .form-input:hover {
        border-color: #D1D5DB;
        background: rgba(255, 255, 255, 0.95);
    }

    /* Enhanced Button Base Styles */
    .enhanced-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 12px 20px;
        font-weight: 600;
        font-size: 14px;
        line-height: 1.2;
        border-radius: 10px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        transform: translateY(0);
        font-family: 'Montserrat', sans-serif;
        min-width: 120px;
        max-width: 180px;
        height: 44px;
        border: 2px solid transparent;
        cursor: pointer;
        user-select: none;
        position: relative;
        overflow: visible;
        white-space: nowrap;
        box-sizing: border-box;
    }

    .enhanced-btn:hover {
        transform: translateY(-2px);
    }

    .enhanced-btn:active {
        transform: translateY(0);
    }

    /* Button Content Layout */
    .btn-content {
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        z-index: 2;
    }

    .btn-icon-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 1.25rem;
        height: 1.25rem;
        margin-right: 0.5rem;
        transition: all 0.3s ease;
    }

    .btn-icon {
        font-size: 1rem;
        transition: all 0.3s ease;
    }

    .btn-text {
        font-weight: 600;
        transition: all 0.3s ease;
    }

    /* Cancel Button Styles */
    .enhanced-btn-cancel {
        background: #f8fafc;
        color: #475569;
        border-color: #cbd5e1;
        box-shadow: 0 2px 4px rgba(71, 85, 105, 0.1);
    }

    .enhanced-btn-cancel:hover {
        background: #e2e8f0;
        color: #334155;
        border-color: #94a3b8;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(71, 85, 105, 0.15);
    }

    /* Submit Button Styles */
    .enhanced-btn-submit {
        background: #dc2626;
        color: white;
        border-color: #dc2626;
        box-shadow: 0 2px 8px rgba(220, 38, 38, 0.3);
    }

    .enhanced-btn-submit:hover {
        background: #b91c1c;
        border-color: #b91c1c;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(220, 38, 38, 0.4);
    }

    /* Ripple Effect */
    .btn-ripple {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border-radius: inherit;
        overflow: hidden;
        pointer-events: none;
    }

    .enhanced-btn:active .btn-ripple::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: translate(-50%, -50%);
        animation: ripple 0.6s ease-out;
    }

    @keyframes ripple {
        to {
            width: 300px;
            height: 300px;
        }
    }

    /* Shine Effect for Submit Button */
    .btn-shine {
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
    }

    .enhanced-btn-submit:hover .btn-shine {
        left: 100%;
    }

    /* Button Focus States */
    .enhanced-btn:focus {
        outline: none;
        ring: 2px;
        ring-color: rgba(220, 38, 38, 0.5);
        ring-offset: 2px;
    }

    .enhanced-btn-cancel:focus {
        ring-color: rgba(31, 41, 55, 0.3);
    }

    /* Disabled State */
    .enhanced-btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none !important;
    }

    .enhanced-btn:disabled:hover {
        transform: none !important;
        box-shadow: none !important;
    }

    /* Section Cards */
    .bg-white.rounded-xl {
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.95);
        border: 1px solid rgba(255, 255, 255, 0.3);
        transition: all 0.3s ease;
    }

    .bg-white.rounded-xl:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    /* Mobile Responsiveness */
    @media (max-width: 768px) {
        .modal-panel {
            margin: 1rem;
            max-width: calc(100vw - 2rem);
        }

        .form-input {
            @apply px-3 py-2 text-sm;
        }

        .enhanced-btn {
            padding: 10px 16px;
            font-size: 13px;
            min-width: 100px;
            max-width: 140px;
            height: 40px;
        }

        .modal-footer {
            padding: 16px;
        }

        .modal-footer > div {
            flex-direction: column;
            gap: 12px;
        }

        .enhanced-btn {
            width: 100%;
            max-width: none;
        }
    }
</style>
