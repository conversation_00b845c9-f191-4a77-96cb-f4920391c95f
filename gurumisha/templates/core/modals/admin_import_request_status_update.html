{% load static %}

<!-- Status Update Modal -->
<div class="fixed inset-0 z-50 overflow-y-auto" 
     id="status-update-modal"
     x-data="{ show: false }"
     x-init="show = true"
     x-show="show"
     x-transition:enter="ease-out duration-300"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100"
     x-transition:leave="ease-in duration-200"
     x-transition:leave-start="opacity-100"
     x-transition:leave-end="opacity-0">
    
    <!-- Modal Backdrop -->
    <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity modal-backdrop"
         @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)"></div>
    
    <!-- Modal Container -->
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        
        <!-- Modal Panel -->
        <div class="inline-block align-bottom bg-white rounded-2xl text-left overflow-hidden shadow-2xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full modal-panel"
             x-show="show"
             x-transition:enter="ease-out duration-400"
             x-transition:enter-start="opacity-0 translate-y-8 sm:translate-y-0 sm:scale-90"
             x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave="ease-in duration-300"
             x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave-end="opacity-0 translate-y-8 sm:translate-y-0 sm:scale-90">
            
            <!-- Modal Header -->
            <div class="bg-gradient-to-r from-purple-600 to-pink-600 px-6 py-5">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-white bg-opacity-20 backdrop-blur-sm rounded-xl flex items-center justify-center mr-4 shadow-lg">
                            <i class="fas fa-sync text-white text-lg"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-white font-montserrat">
                                Update Request Status
                            </h3>
                            <p class="text-sm text-white text-opacity-90 font-raleway">Request #{{ import_request.id|stringformat:"05d" }} • 8-Stage Workflow</p>
                        </div>
                    </div>

                    <!-- Close Button -->
                    <button type="button"
                            class="text-white text-opacity-70 hover:text-white hover:bg-white hover:bg-opacity-20 rounded-lg p-2 transition-all duration-200"
                            @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)">
                        <span class="sr-only">Close</span>
                        <i class="fas fa-times text-lg"></i>
                    </button>
                </div>
            </div>
            
            <!-- Modal Body -->
            <div class="bg-gradient-to-br from-gray-50 to-white px-6 py-6">
                <!-- Current Status -->
                <div class="mb-6 bg-white rounded-xl p-4 border border-gray-200 shadow-sm">
                    <h4 class="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                        <i class="fas fa-info-circle mr-2 text-purple-600"></i>
                        Current Status
                    </h4>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium border
                                {% if import_request.status == 'pending' %}bg-yellow-100 text-yellow-800 border-yellow-200
                                {% elif import_request.status == 'on_quotation' %}bg-orange-100 text-orange-800 border-orange-200
                                {% elif import_request.status == 'processing' %}bg-blue-100 text-blue-800 border-blue-200
                                {% elif import_request.status == 'fee_paid' %}bg-purple-100 text-purple-800 border-purple-200

                                {% elif import_request.status == 'completed' %}bg-green-100 text-green-800 border-green-200
                                {% elif import_request.status == 'cancelled' %}bg-red-100 text-red-800 border-red-200
                                {% else %}bg-gray-100 text-gray-800 border-gray-200{% endif %}">
                                <div class="w-2 h-2 rounded-full mr-2
                                    {% if import_request.status == 'pending' %}bg-yellow-500
                                    {% elif import_request.status == 'on_quotation' %}bg-orange-500
                                    {% elif import_request.status == 'processing' %}bg-blue-500
                                    {% elif import_request.status == 'fee_paid' %}bg-purple-500

                                    {% elif import_request.status == 'completed' %}bg-green-500
                                    {% elif import_request.status == 'cancelled' %}bg-red-500
                                    {% else %}bg-gray-500{% endif %} animate-pulse"></div>
                                {{ import_request.get_status_display }}
                            </span>
                        </div>
                        <div class="text-sm text-gray-500 flex items-center">
                            <i class="fas fa-clock mr-1"></i>
                            Updated {{ import_request.updated_at|timesince }} ago
                        </div>
                    </div>
                </div>
                
                <!-- Status Workflow -->
                <div class="mb-6">
                    <h4 class="text-sm font-medium text-gray-700 mb-3">8-Stage Status Workflow</h4>
                    <div class="space-y-2">
                        <!-- Pending -->
                        <div class="flex items-center {% if import_request.status == 'pending' %}text-yellow-600 font-semibold{% else %}text-gray-400{% endif %}">
                            <div class="w-3 h-3 rounded-full {% if import_request.status == 'pending' %}bg-yellow-500{% else %}bg-gray-300{% endif %} mr-3"></div>
                            <span>1. Pending</span>
                        </div>

                        <!-- On Quotation -->
                        <div class="flex items-center {% if import_request.status == 'on_quotation' %}text-orange-600 font-semibold{% else %}text-gray-400{% endif %}">
                            <div class="w-3 h-3 rounded-full {% if import_request.status == 'on_quotation' %}bg-orange-500{% else %}bg-gray-300{% endif %} mr-3"></div>
                            <span>2. On Quotation</span>
                        </div>

                        <!-- Processing -->
                        <div class="flex items-center {% if import_request.status == 'processing' %}text-blue-600 font-semibold{% else %}text-gray-400{% endif %}">
                            <div class="w-3 h-3 rounded-full {% if import_request.status == 'processing' %}bg-blue-500{% else %}bg-gray-300{% endif %} mr-3"></div>
                            <span>3. Processing</span>
                        </div>

                        <!-- Fee Paid -->
                        <div class="flex items-center {% if import_request.status == 'fee_paid' %}text-purple-600 font-semibold{% else %}text-gray-400{% endif %}">
                            <div class="w-3 h-3 rounded-full {% if import_request.status == 'fee_paid' %}bg-purple-500{% else %}bg-gray-300{% endif %} mr-3"></div>
                            <span>4. Fee Paid</span>
                        </div>

                        <!-- Completed -->
                        <div class="flex items-center {% if import_request.status == 'completed' %}text-green-600 font-semibold{% else %}text-gray-400{% endif %}">
                            <div class="w-3 h-3 rounded-full {% if import_request.status == 'completed' %}bg-green-500{% else %}bg-gray-300{% endif %} mr-3"></div>
                            <span>5. Completed</span>
                        </div>

                        <!-- Cancelled -->
                        <div class="flex items-center {% if import_request.status == 'cancelled' %}text-red-600 font-semibold{% else %}text-gray-400{% endif %}">
                            <div class="w-3 h-3 rounded-full {% if import_request.status == 'cancelled' %}bg-red-500{% else %}bg-gray-300{% endif %} mr-3"></div>
                            <span>6. Cancelled</span>
                        </div>
                    </div>
                </div>
                
                <!-- Status Update Form -->
                <form id="status-update-form"
                      hx-post="{% url 'core:admin_import_request_status_update' import_request.id %}"
                      hx-target="#import-requests-table"
                      hx-swap="outerHTML"
                      hx-on::after-request="if(event.detail.successful) { show = false; setTimeout(() => $el.closest('.fixed').remove(), 200); }"
                      class="space-y-4">
                    {% csrf_token %}
                    
                    <!-- New Status Selection -->
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                            New Status <span class="text-red-500">*</span>
                        </label>
                        <select name="status" id="status" required
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent transition-all duration-200">
                            <option value="">Select new status</option>
                            {% if import_request.status == 'pending' %}
                                <option value="on_quotation">→ On Quotation</option>
                                <option value="cancelled">❌ Cancelled</option>
                            {% elif import_request.status == 'on_quotation' %}
                                <option value="processing">→ Processing</option>
                                <option value="pending">← Back to Pending</option>
                                <option value="cancelled">❌ Cancelled</option>
                            {% elif import_request.status == 'processing' %}
                                <option value="fee_paid">→ Fee Paid</option>
                                <option value="on_quotation">← Back to Quotation</option>
                                <option value="cancelled">❌ Cancelled</option>
                            {% elif import_request.status == 'fee_paid' %}
                                <option value="completed">→ Completed</option>
                                <option value="processing">← Back to Processing</option>
                                <option value="cancelled">❌ Cancelled</option>
                            {% elif import_request.status == 'completed' %}
                                <option value="fee_paid">← Back to Fee Paid</option>
                            {% elif import_request.status == 'cancelled' %}
                                <option value="pending">🔄 Reactivate to Pending</option>
                            {% endif %}
                        </select>
                    </div>
                    
                    <!-- Notes -->
                    <div>
                        <label for="notes" class="block text-sm font-medium text-gray-700 mb-2">
                            Notes (Optional)
                        </label>
                        <textarea name="notes" id="notes" rows="3"
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent transition-all duration-200"
                                  placeholder="Add any notes about this status change..."></textarea>
                    </div>
                </form>
                
                <!-- Customer Information -->
                <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                    <h5 class="text-sm font-medium text-gray-700 mb-2">Customer will be notified</h5>
                    <div class="flex items-center text-sm text-gray-600">
                        <i class="fas fa-user mr-2"></i>
                        <span>{{ import_request.customer.get_full_name|default:import_request.customer.username }}</span>
                        <span class="mx-2">•</span>
                        <span>{{ import_request.customer.email }}</span>
                    </div>
                </div>
            </div>
            
            <!-- Modal Footer -->
            <div class="bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-5 flex justify-between items-center">
                <div class="text-sm text-gray-600">
                    <i class="fas fa-bell mr-1"></i>
                    Customer will be automatically notified
                </div>
                <div class="flex space-x-3">
                    <button type="button"
                            class="enhanced-btn enhanced-btn-cancel"
                            @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)">
                        <i class="fas fa-times mr-2"></i>
                        <span>Cancel</span>
                    </button>
                    <button type="submit"
                            form="status-update-form"
                            class="enhanced-btn enhanced-btn-submit">
                        <i class="fas fa-check mr-2"></i>
                        <span>Update Status</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    /* Enhanced Modal Styles */
    .modal-panel {
        backdrop-filter: blur(20px);
        background: rgba(255, 255, 255, 0.98);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    }

    /* Enhanced Button Base Styles */
    .enhanced-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 12px 20px;
        font-weight: 600;
        font-size: 14px;
        line-height: 1.2;
        border-radius: 10px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        transform: translateY(0);
        font-family: 'Montserrat', sans-serif;
        min-width: 120px;
        max-width: 180px;
        height: 44px;
        border: 2px solid transparent;
        cursor: pointer;
        user-select: none;
        position: relative;
        overflow: visible;
        white-space: nowrap;
        box-sizing: border-box;
    }

    /* Simplified Cancel Button */
    .enhanced-btn-cancel {
        background: linear-gradient(135deg, #f9fafb, #f3f4f6);
        color: #374151;
        border: 2px solid #e5e7eb;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    }

    .enhanced-btn-cancel:hover {
        background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
        color: #1f2937;
        border-color: #d1d5db;
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
    }

    .enhanced-btn-cancel:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(156, 163, 175, 0.3);
    }

    .enhanced-btn-cancel:active {
        transform: translateY(0);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    /* Simplified Submit Button */
    .enhanced-btn-submit {
        background: #dc2626;
        color: white;
        border: 2px solid #dc2626;
        box-shadow: 0 2px 8px rgba(220, 38, 38, 0.3);
    }

    .enhanced-btn-submit:hover {
        background: #b91c1c;
        border-color: #b91c1c;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(220, 38, 38, 0.4);
    }

    .enhanced-btn-submit:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(248, 113, 113, 0.4);
    }

    .enhanced-btn-submit:active {
        transform: translateY(-1px);
        box-shadow: 0 6px 16px rgba(220, 38, 38, 0.4);
    }

    /* Enhanced Button Styles */
    .modal-btn {
        @apply inline-flex items-center px-6 py-3 rounded-xl font-semibold text-sm transition-all duration-300;
        font-family: 'Montserrat', sans-serif;
        position: relative;
        overflow: hidden;
    }

    .modal-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        transition: left 0.5s;
    }

    .modal-btn:hover::before {
        left: 100%;
    }

    .modal-btn-primary {
        @apply bg-gradient-to-r from-purple-600 to-pink-600 text-white;
        box-shadow: 0 4px 15px rgba(147, 51, 234, 0.3);
    }

    .modal-btn-primary:hover {
        @apply from-purple-700 to-pink-700;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(147, 51, 234, 0.4);
    }

    .modal-btn-secondary {
        @apply bg-white text-gray-700 border-2 border-gray-300 hover:border-gray-400 hover:bg-gray-50;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .modal-btn-secondary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    /* Status workflow styling */
    .space-y-2 > div {
        transition: all 0.3s ease;
        padding: 0.5rem;
        border-radius: 0.5rem;
    }

    .space-y-2 > div:hover {
        background-color: rgba(147, 51, 234, 0.05);
        transform: translateX(4px);
    }

    /* Form styling */
    select, textarea {
        @apply transition-all duration-300;
    }

    select:focus, textarea:focus {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(147, 51, 234, 0.15);
    }

    /* Mobile Responsiveness */
    @media (max-width: 768px) {
        .modal-panel {
            margin: 1rem;
            max-width: calc(100vw - 2rem);
        }

        .modal-btn {
            @apply px-4 py-2 text-xs;
        }
    }

    /* Loading states */
    .htmx-request .modal-btn {
        @apply opacity-50 cursor-wait;
    }

    .htmx-request .modal-btn i {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    /* Accessibility */
    .modal-btn:focus {
        outline: 2px solid rgba(147, 51, 234, 0.5);
        outline-offset: 2px;
    }
</style>
