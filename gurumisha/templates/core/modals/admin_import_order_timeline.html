{% load static %}

<!-- Timeline Modal -->
<div class="fixed inset-0 z-50 overflow-y-auto" 
     id="timeline-modal"
     x-data="{ show: false }"
     x-init="show = true"
     x-show="show"
     x-transition:enter="ease-out duration-300"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100"
     x-transition:leave="ease-in duration-200"
     x-transition:leave-start="opacity-100"
     x-transition:leave-end="opacity-0">
    
    <!-- Modal Backdrop -->
    <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity modal-backdrop"
         @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)"></div>
    
    <!-- Modal Panel -->
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="relative bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden modal-panel"
             x-show="show"
             x-transition:enter="ease-out duration-300"
             x-transition:enter-start="opacity-0 scale-95"
             x-transition:enter-end="opacity-100 scale-100"
             x-transition:leave="ease-in duration-200"
             x-transition:leave-start="opacity-100 scale-100"
             x-transition:leave-end="opacity-0 scale-95">
            
            <!-- Modal Header -->
            <div class="bg-gradient-to-r from-purple-600 via-indigo-600 to-blue-600 px-6 py-4 rounded-t-2xl modal-header">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-white bg-opacity-20 rounded-xl flex items-center justify-center backdrop-blur-sm">
                            <i class="fas fa-history text-white text-lg"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-white font-montserrat">Order Timeline</h3>
                            <p class="text-white text-opacity-90 text-sm font-raleway">Order #{{ import_order.order_number }} - {{ import_order.year }} {{ import_order.brand }} {{ import_order.model }}</p>
                        </div>
                    </div>
                    <button type="button" 
                            class="text-white hover:text-gray-200 transition-colors duration-200 p-2 hover:bg-white hover:bg-opacity-10 rounded-lg"
                            @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            
            <!-- Modal Body -->
            <div class="bg-gradient-to-br from-gray-50 to-white px-6 py-6 max-h-[70vh] overflow-y-auto modal-body">
                
                <!-- Order Summary -->
                <div class="bg-white rounded-xl p-4 border border-gray-200 shadow-sm mb-6">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-500 rounded-xl flex items-center justify-center mr-4">
                                <i class="fas fa-car text-white text-xl"></i>
                            </div>
                            <div>
                                <h4 class="text-lg font-bold text-gray-900 font-montserrat">{{ import_order.year }} {{ import_order.brand }} {{ import_order.model }}</h4>
                                <p class="text-sm text-gray-600 font-raleway">Customer: {{ import_order.customer.get_full_name|default:import_order.customer.username }}</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-lg font-bold text-green-600 font-montserrat">KES {{ import_order.total_cost|floatformat:0 }}</div>
                            <div class="text-sm text-gray-500">Total Cost</div>
                        </div>
                    </div>
                </div>

                <!-- Progress Overview -->
                <div class="bg-white rounded-xl p-4 border border-gray-200 shadow-sm mb-6">
                    <h4 class="text-lg font-semibold text-gray-900 mb-4 flex items-center font-montserrat">
                        <i class="fas fa-chart-line mr-2 text-blue-600"></i>
                        Progress Overview
                    </h4>
                    <div class="relative">
                        <div class="w-full bg-gray-200 rounded-full h-3 mb-4">
                            <div class="bg-gradient-to-r from-purple-500 to-blue-500 h-3 rounded-full transition-all duration-500" 
                                 style="width: {{ import_order.get_progress_percentage }}%"></div>
                        </div>
                        <div class="flex justify-between text-sm text-gray-600">
                            <span>Started</span>
                            <span class="font-semibold">{{ import_order.get_progress_percentage }}% Complete</span>
                            <span>Delivered</span>
                        </div>
                    </div>
                </div>

                <!-- Timeline -->
                <div class="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
                    <h4 class="text-lg font-semibold text-gray-900 mb-6 flex items-center font-montserrat">
                        <i class="fas fa-timeline mr-2 text-purple-600"></i>
                        Status History Timeline
                    </h4>
                    
                    <div class="relative">
                        <!-- Timeline Line -->
                        <div class="absolute left-6 top-0 bottom-0 w-0.5 bg-gradient-to-b from-purple-500 to-blue-500"></div>
                        
                        <!-- Timeline Items -->
                        <div class="space-y-6">
                            {% for history in status_history %}
                            <div class="relative flex items-start">
                                <!-- Timeline Dot -->
                                <div class="relative z-10 flex items-center justify-center w-12 h-12 bg-gradient-to-br from-purple-500 to-blue-500 rounded-full shadow-lg">
                                    <i class="fas fa-{{ history.get_status_icon }} text-white"></i>
                                </div>
                                
                                <!-- Timeline Content -->
                                <div class="ml-6 flex-1">
                                    <div class="bg-gradient-to-r from-gray-50 to-white rounded-xl p-4 border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
                                        <div class="flex items-center justify-between mb-2">
                                            <h5 class="text-lg font-semibold text-gray-900 font-montserrat">{{ history.get_new_status_display }}</h5>
                                            <span class="text-sm text-gray-500 font-raleway">{{ history.created_at|date:"M d, Y H:i" }}</span>
                                        </div>
                                        
                                        {% if history.previous_status %}
                                        <div class="text-sm text-gray-600 mb-2">
                                            <i class="fas fa-arrow-right mr-1"></i>
                                            Changed from <span class="font-semibold">{{ history.get_previous_status_display }}</span>
                                        </div>
                                        {% endif %}
                                        
                                        {% if history.change_reason %}
                                        <div class="text-sm text-gray-700 mb-2">
                                            <i class="fas fa-info-circle mr-1 text-blue-500"></i>
                                            {{ history.change_reason }}
                                        </div>
                                        {% endif %}
                                        
                                        {% if history.admin_notes %}
                                        <div class="text-sm text-gray-600 bg-gray-50 rounded-lg p-2 mb-2">
                                            <i class="fas fa-sticky-note mr-1 text-yellow-500"></i>
                                            {{ history.admin_notes }}
                                        </div>
                                        {% endif %}
                                        
                                        <div class="flex items-center justify-between text-xs text-gray-500">
                                            <span>
                                                <i class="fas fa-user mr-1"></i>
                                                Updated by {{ history.changed_by.get_full_name|default:history.changed_by.username }}
                                            </span>
                                            {% if history.customer_notification_sent %}
                                            <span class="text-green-600">
                                                <i class="fas fa-check-circle mr-1"></i>
                                                Customer notified
                                            </span>
                                            {% else %}
                                            <span class="text-orange-600">
                                                <i class="fas fa-clock mr-1"></i>
                                                Notification pending
                                            </span>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% empty %}
                            <div class="text-center py-8">
                                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-history text-gray-400 text-2xl"></i>
                                </div>
                                <h4 class="text-lg font-semibold text-gray-900 mb-2 font-montserrat">No Timeline History</h4>
                                <p class="text-gray-600 font-raleway">Status changes will appear here as the order progresses.</p>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Modal Footer -->
            <div class="bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-4 rounded-b-2xl border-t border-gray-200">
                <div class="flex flex-col sm:flex-row justify-between items-center space-y-3 sm:space-y-0">
                    <div class="text-sm text-gray-600 font-raleway">
                        <i class="fas fa-clock mr-1"></i>
                        Last updated: {{ import_order.updated_at|date:"M d, Y H:i" }}
                    </div>
                    
                    <div class="flex space-x-3">
                        <!-- Export Timeline Button -->
                        <button type="button" 
                                class="enhanced-btn enhanced-btn-secondary"
                                title="Export timeline data">
                            <div class="btn-content">
                                <div class="btn-icon-wrapper">
                                    <i class="btn-icon fas fa-download"></i>
                                </div>
                                <span class="btn-text">Export</span>
                            </div>
                        </button>
                        
                        <!-- Close Button -->
                        <button type="button" 
                                class="enhanced-btn enhanced-btn-primary"
                                @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)">
                            <div class="btn-content">
                                <div class="btn-icon-wrapper">
                                    <i class="btn-icon fas fa-times"></i>
                                </div>
                                <span class="btn-text">Close</span>
                            </div>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    /* Enhanced Modal Styles */
    .modal-panel {
        backdrop-filter: blur(20px);
        background: rgba(255, 255, 255, 0.98);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    }

    /* Timeline Specific Styles */
    .timeline-item {
        position: relative;
        transition: all 0.3s ease;
    }

    .timeline-item:hover {
        transform: translateX(4px);
    }

    .timeline-dot {
        position: relative;
        z-index: 10;
        transition: all 0.3s ease;
    }

    .timeline-item:hover .timeline-dot {
        transform: scale(1.1);
        box-shadow: 0 8px 20px rgba(147, 51, 234, 0.4);
    }

    /* Timeline Line Animation */
    .timeline-line {
        background: linear-gradient(to bottom, #8B5CF6, #3B82F6);
        animation: timelineGlow 3s ease-in-out infinite alternate;
    }

    @keyframes timelineGlow {
        0% {
            box-shadow: 0 0 5px rgba(139, 92, 246, 0.5);
        }
        100% {
            box-shadow: 0 0 15px rgba(139, 92, 246, 0.8);
        }
    }

    /* Progress Bar Animation */
    .progress-bar {
        background: linear-gradient(90deg, #8B5CF6, #3B82F6);
        animation: progressPulse 2s ease-in-out infinite alternate;
    }

    @keyframes progressPulse {
        0% {
            box-shadow: 0 0 10px rgba(139, 92, 246, 0.6);
        }
        100% {
            box-shadow: 0 0 20px rgba(139, 92, 246, 0.9);
        }
    }

    /* Enhanced Button Styles */
    .enhanced-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        font-size: 0.875rem;
        border-radius: 0.75rem;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        transform: translateY(0);
        font-family: 'Montserrat', sans-serif;
        min-width: 120px;
        border: none;
        cursor: pointer;
        user-select: none;
        position: relative;
        overflow: hidden;
    }

    .enhanced-btn:hover {
        transform: translateY(-2px);
    }

    .enhanced-btn:active {
        transform: translateY(0);
    }

    /* Button Content Layout */
    .btn-content {
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        z-index: 2;
    }

    .btn-icon-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 1rem;
        height: 1rem;
        margin-right: 0.5rem;
        transition: all 0.3s ease;
    }

    .btn-icon {
        font-size: 0.875rem;
        transition: all 0.3s ease;
    }

    .btn-text {
        font-weight: 600;
        transition: all 0.3s ease;
    }

    /* Secondary Button Styles */
    .enhanced-btn-secondary {
        background: linear-gradient(135deg,
            rgba(107, 114, 128, 0.1) 0%,
            rgba(156, 163, 175, 0.1) 15%,
            rgba(209, 213, 219, 0.1) 35%,
            rgba(243, 244, 246, 0.1) 65%,
            rgba(249, 250, 251, 0.1) 85%,
            rgba(255, 255, 255, 0.1) 100%);
        background-size: 300% 100%;
        background-position: 0% 50%;
        color: #6B7280;
        border: 2px solid #E5E7EB;
        box-shadow:
            0 4px 12px rgba(107, 114, 128, 0.15),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }

    .enhanced-btn-secondary:hover {
        background-position: 100% 50%;
        border-color: #D1D5DB;
        color: #4B5563;
        box-shadow:
            0 8px 20px rgba(107, 114, 128, 0.25),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }

    /* Primary Button Styles */
    .enhanced-btn-primary {
        background: linear-gradient(135deg,
            #8B5CF6 0%,
            #7C3AED 15%,
            #3B82F6 35%,
            #2563EB 65%,
            #1D4ED8 85%,
            #1E40AF 100%);
        background-size: 300% 100%;
        background-position: 0% 50%;
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.1);
        box-shadow:
            0 8px 24px rgba(139, 92, 246, 0.4),
            0 4px 12px rgba(59, 130, 246, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.2),
            inset 0 -1px 0 rgba(0, 0, 0, 0.2);
        position: relative;
    }

    .enhanced-btn-primary:hover {
        background-position: 100% 50%;
        box-shadow:
            0 12px 32px rgba(139, 92, 246, 0.5),
            0 6px 16px rgba(59, 130, 246, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.3),
            inset 0 -1px 0 rgba(0, 0, 0, 0.3);
    }

    /* Card Hover Effects */
    .bg-white.rounded-xl {
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.95);
        border: 1px solid rgba(255, 255, 255, 0.3);
        transition: all 0.3s ease;
    }

    .bg-white.rounded-xl:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    /* Mobile Responsiveness */
    @media (max-width: 768px) {
        .modal-panel {
            margin: 1rem;
            max-width: calc(100vw - 2rem);
        }

        .timeline-item {
            margin-left: -1rem;
        }

        .enhanced-btn {
            @apply px-3 py-2 text-xs;
            min-width: 100px;
        }
    }
</style>
