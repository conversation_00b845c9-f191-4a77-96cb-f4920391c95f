{% load static %}

<!-- Add Import Order Modal -->
<div class="fixed inset-0 z-50 overflow-y-auto" 
     id="add-import-order-modal"
     x-data="{ show: false }"
     x-init="show = true"
     x-show="show"
     x-transition:enter="ease-out duration-300"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100"
     x-transition:leave="ease-in duration-200"
     x-transition:leave-start="opacity-100"
     x-transition:leave-end="opacity-0">
    
    <!-- Modal Backdrop -->
    <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity modal-backdrop"
         @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)"></div>
    
    <!-- Modal Panel -->
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="relative bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden modal-panel"
             x-show="show"
             x-transition:enter="ease-out duration-300"
             x-transition:enter-start="opacity-0 scale-95"
             x-transition:enter-end="opacity-100 scale-100"
             x-transition:leave="ease-in duration-200"
             x-transition:leave-start="opacity-100 scale-100"
             x-transition:leave-end="opacity-0 scale-95">
            
            <!-- Modal Header -->
            <div class="bg-gradient-to-r from-harrier-red via-harrier-dark to-harrier-blue px-6 py-4 rounded-t-2xl modal-header">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-white bg-opacity-20 rounded-xl flex items-center justify-center backdrop-blur-sm">
                            <i class="fas fa-plus text-white text-lg"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-white font-montserrat">Add New Import Order</h3>
                            <p class="text-white text-opacity-90 text-sm font-raleway">Create a new import order for tracking</p>
                        </div>
                    </div>
                    <button type="button" 
                            class="text-white hover:text-gray-200 transition-colors duration-200 p-2 hover:bg-white hover:bg-opacity-10 rounded-lg"
                            @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            
            <!-- Modal Body -->
            <div class="bg-gradient-to-br from-gray-50 to-white px-6 py-6 max-h-96 overflow-y-auto modal-body">
                <form id="add-import-order-form"
                      hx-post="{% url 'core:admin_import_order_add' %}"
                      hx-target="#tracking-management-table"
                      hx-swap="outerHTML"
                      hx-on::after-request="if(event.detail.successful) { show = false; setTimeout(() => $el.closest('.fixed').remove(), 200); }"
                      class="space-y-6"
                      x-data="{ isSubmitting: false }"
                      @submit="isSubmitting = true">
                    {% csrf_token %}
                    
                    <!-- Customer Selection -->
                    <div class="bg-white rounded-xl p-4 border border-gray-200 shadow-sm">
                        <h4 class="text-lg font-semibold text-harrier-dark mb-4 flex items-center">
                            <i class="fas fa-user mr-2 text-blue-600"></i>
                            Customer Information
                        </h4>
                        <div class="form-group">
                            <label for="customer" class="form-label">
                                <i class="fas fa-user-circle mr-1 text-blue-500"></i>
                                Select Customer <span class="text-red-500">*</span>
                            </label>
                            <select name="customer" id="customer" required class="form-input">
                                <option value="">Choose a customer...</option>
                                {% for customer in customers %}
                                <option value="{{ customer.id }}">{{ customer.get_full_name|default:customer.username }} ({{ customer.email }})</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    
                    <!-- Vehicle Information -->
                    <div class="bg-white rounded-xl p-4 border border-gray-200 shadow-sm">
                        <h4 class="text-lg font-semibold text-harrier-dark mb-4 flex items-center">
                            <i class="fas fa-car mr-2 text-harrier-red"></i>
                            Vehicle Details
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="form-group">
                                <label for="brand" class="form-label">
                                    <i class="fas fa-tag mr-1 text-harrier-red"></i>
                                    Brand <span class="text-red-500">*</span>
                                </label>
                                <input type="text" name="brand" id="brand" required 
                                       class="form-input" placeholder="e.g., Toyota, Honda, BMW">
                            </div>
                            
                            <div class="form-group">
                                <label for="model" class="form-label">
                                    <i class="fas fa-car-side mr-1 text-harrier-red"></i>
                                    Model <span class="text-red-500">*</span>
                                </label>
                                <input type="text" name="model" id="model" required 
                                       class="form-input" placeholder="e.g., Camry, Civic, X5">
                            </div>
                            
                            <div class="form-group">
                                <label for="year" class="form-label">
                                    <i class="fas fa-calendar mr-1 text-harrier-red"></i>
                                    Year <span class="text-red-500">*</span>
                                </label>
                                <input type="number" name="year" id="year" required min="1990" max="2025"
                                       class="form-input" placeholder="2020">
                            </div>
                            
                            <div class="form-group">
                                <label for="color" class="form-label">
                                    <i class="fas fa-palette mr-1 text-harrier-red"></i>
                                    Color
                                </label>
                                <input type="text" name="color" id="color" 
                                       class="form-input" placeholder="e.g., White, Black, Silver">
                            </div>
                            
                            <div class="form-group">
                                <label for="engine_size" class="form-label">
                                    <i class="fas fa-cog mr-1 text-harrier-red"></i>
                                    Engine Size
                                </label>
                                <input type="text" name="engine_size" id="engine_size" 
                                       class="form-input" placeholder="e.g., 2.0L, 3.5L">
                            </div>
                            
                            <div class="form-group">
                                <label for="fuel_type" class="form-label">
                                    <i class="fas fa-gas-pump mr-1 text-harrier-red"></i>
                                    Fuel Type
                                </label>
                                <select name="fuel_type" id="fuel_type" class="form-input">
                                    <option value="">Select fuel type...</option>
                                    <option value="Petrol">Petrol</option>
                                    <option value="Diesel">Diesel</option>
                                    <option value="Hybrid">Hybrid</option>
                                    <option value="Electric">Electric</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Order Details -->
                    <div class="bg-white rounded-xl p-4 border border-gray-200 shadow-sm">
                        <h4 class="text-lg font-semibold text-harrier-dark mb-4 flex items-center">
                            <i class="fas fa-clipboard-list mr-2 text-green-600"></i>
                            Order Information
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="form-group">
                                <label for="total_cost" class="form-label">
                                    <i class="fas fa-money-bill-wave mr-1 text-green-500"></i>
                                    Total Cost (KES) <span class="text-red-500">*</span>
                                </label>
                                <div class="relative">
                                    <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">KES</span>
                                    <input type="number" name="total_cost" id="total_cost" required min="0" step="1000"
                                           class="form-input pl-12" placeholder="2,500,000">
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="initial_status" class="form-label">
                                    <i class="fas fa-flag mr-1 text-blue-500"></i>
                                    Initial Status <span class="text-red-500">*</span>
                                </label>
                                <select name="initial_status" id="initial_status" required class="form-input">
                                    <option value="import_request">Import Request</option>
                                    <option value="auction_won">Auction Won</option>
                                    <option value="shipped">Shipped</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Additional Information -->
                    <div class="bg-white rounded-xl p-4 border border-gray-200 shadow-sm">
                        <h4 class="text-lg font-semibold text-harrier-dark mb-4 flex items-center">
                            <i class="fas fa-info-circle mr-2 text-purple-600"></i>
                            Additional Details
                        </h4>
                        <div class="form-group">
                            <label for="admin_notes" class="form-label">
                                <i class="fas fa-sticky-note mr-1 text-purple-500"></i>
                                Admin Notes
                            </label>
                            <textarea name="admin_notes" id="admin_notes" rows="3" 
                                      class="form-input" 
                                      placeholder="Add any relevant notes about this import order..."></textarea>
                        </div>
                    </div>
                </form>
            </div>
            
            <!-- Modal Footer -->
            <div class="bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-4 rounded-b-2xl border-t border-gray-200">
                <div class="flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-3">
                    <!-- Cancel Button -->
                    <button type="button" 
                            class="enhanced-btn enhanced-btn-cancel"
                            @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)">
                        <div class="btn-content">
                            <div class="btn-icon-wrapper">
                                <i class="btn-icon fas fa-times"></i>
                            </div>
                            <span class="btn-text">Cancel</span>
                        </div>
                        <div class="btn-ripple"></div>
                    </button>
                    
                    <!-- Submit Button -->
                    <button type="submit" 
                            form="add-import-order-form"
                            class="enhanced-btn enhanced-btn-submit"
                            x-bind:disabled="isSubmitting"
                            x-bind:class="{ 'opacity-75 cursor-not-allowed': isSubmitting }">
                        <div class="btn-content">
                            <div class="btn-icon-wrapper">
                                <i class="btn-icon fas fa-plus" x-show="!isSubmitting"></i>
                                <i class="btn-icon fas fa-spinner fa-spin" x-show="isSubmitting"></i>
                            </div>
                            <span class="btn-text" x-text="isSubmitting ? 'Creating...' : 'Create Order'"></span>
                        </div>
                        <div class="btn-ripple"></div>
                        <div class="btn-shine"></div>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    /* Enhanced Modal Styles */
    .modal-panel {
        backdrop-filter: blur(20px);
        background: rgba(255, 255, 255, 0.98);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    }

    /* Form Styling */
    .form-group {
        @apply space-y-2;
    }

    .form-label {
        @apply block text-sm font-semibold text-gray-700 flex items-center;
        font-family: 'Montserrat', sans-serif;
    }

    .form-input {
        @apply w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-harrier-red focus:border-transparent transition-all duration-300;
        font-family: 'Raleway', sans-serif;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
    }

    .form-input:focus {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(220, 38, 38, 0.15);
    }

    .form-input:hover {
        border-color: #D1D5DB;
        background: rgba(255, 255, 255, 0.95);
    }

    /* Enhanced Button Base Styles */
    .enhanced-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 12px 20px;
        font-weight: 600;
        font-size: 14px;
        line-height: 1.2;
        border-radius: 10px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        transform: translateY(0);
        font-family: 'Montserrat', sans-serif;
        min-width: 120px;
        max-width: 180px;
        height: 44px;
        border: 2px solid transparent;
        cursor: pointer;
        user-select: none;
        position: relative;
        overflow: visible;
        white-space: nowrap;
        box-sizing: border-box;
    }

    .enhanced-btn:hover {
        transform: translateY(-2px);
    }

    .enhanced-btn:active {
        transform: translateY(0);
    }

    /* Button Content Layout */
    .btn-content {
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        z-index: 2;
    }

    .btn-icon-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 1.25rem;
        height: 1.25rem;
        margin-right: 0.5rem;
        transition: all 0.3s ease;
    }

    .btn-icon {
        font-size: 1rem;
        transition: all 0.3s ease;
    }

    .btn-text {
        font-weight: 600;
        transition: all 0.3s ease;
    }

    /* Cancel Button Styles */
    .enhanced-btn-cancel {
        background: linear-gradient(135deg,
            rgba(107, 114, 128, 0.1) 0%,
            rgba(156, 163, 175, 0.1) 15%,
            rgba(209, 213, 219, 0.1) 35%,
            rgba(243, 244, 246, 0.1) 65%,
            rgba(249, 250, 251, 0.1) 85%,
            rgba(255, 255, 255, 0.1) 100%);
        background-size: 300% 100%;
        background-position: 0% 50%;
        color: #6B7280;
        border: 2px solid #E5E7EB;
        box-shadow:
            0 4px 12px rgba(107, 114, 128, 0.15),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }

    .enhanced-btn-cancel:hover {
        background-position: 100% 50%;
        border-color: #D1D5DB;
        color: #4B5563;
        box-shadow:
            0 8px 20px rgba(107, 114, 128, 0.25),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }

    /* Submit Button Styles */
    .enhanced-btn-submit {
        background: linear-gradient(135deg,
            var(--harrier-red) 0%,
            #EF4444 15%,
            var(--harrier-dark) 35%,
            #1F2937 65%,
            var(--harrier-blue) 85%,
            #3B82F6 100%);
        background-size: 300% 100%;
        background-position: 0% 50%;
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.1);
        box-shadow:
            0 8px 24px rgba(220, 38, 38, 0.4),
            0 4px 12px rgba(31, 41, 55, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.2),
            inset 0 -1px 0 rgba(0, 0, 0, 0.2);
        position: relative;
    }

    .enhanced-btn-submit:hover {
        background-position: 100% 50%;
        box-shadow:
            0 12px 32px rgba(220, 38, 38, 0.5),
            0 6px 16px rgba(31, 41, 55, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.3),
            inset 0 -1px 0 rgba(0, 0, 0, 0.3);
    }

    /* Ripple Effect */
    .btn-ripple {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        border-radius: inherit;
        overflow: hidden;
        pointer-events: none;
    }

    .enhanced-btn:active .btn-ripple::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: translate(-50%, -50%);
        animation: ripple 0.6s ease-out;
    }

    @keyframes ripple {
        to {
            width: 300px;
            height: 300px;
        }
    }

    /* Shine Effect for Submit Button */
    .btn-shine {
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s ease;
    }

    .enhanced-btn-submit:hover .btn-shine {
        left: 100%;
    }
</style>
