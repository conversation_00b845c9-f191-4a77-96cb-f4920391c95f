{% load static %}

<!-- View Import Request Modal -->
<div class="fixed inset-0 z-50 overflow-y-auto" 
     id="view-import-request-modal"
     x-data="{ show: false }"
     x-init="show = true"
     x-show="show"
     x-transition:enter="ease-out duration-300"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100"
     x-transition:leave="ease-in duration-200"
     x-transition:leave-start="opacity-100"
     x-transition:leave-end="opacity-0">
    
    <!-- Modal Backdrop -->
    <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity modal-backdrop"
         @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)"></div>
    
    <!-- Modal Container -->
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        
        <!-- Modal Panel -->
        <div class="inline-block align-bottom bg-white rounded-2xl text-left overflow-hidden shadow-2xl transform transition-all sm:my-8 sm:align-middle sm:max-w-5xl sm:w-full modal-panel"
             x-show="show"
             x-transition:enter="ease-out duration-400"
             x-transition:enter-start="opacity-0 translate-y-8 sm:translate-y-0 sm:scale-90"
             x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave="ease-in duration-300"
             x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave-end="opacity-0 translate-y-8 sm:translate-y-0 sm:scale-90">
            
            <!-- Modal Header -->
            <div class="bg-gradient-to-r
                {% if import_request.status == 'pending' %}from-yellow-500 to-orange-500
                {% elif import_request.status == 'on_quotation' %}from-orange-500 to-red-500
                {% elif import_request.status == 'processing' %}from-blue-500 to-indigo-500
                {% elif import_request.status == 'fee_paid' %}from-purple-500 to-pink-500

                {% elif import_request.status == 'completed' %}from-green-500 to-emerald-500
                {% elif import_request.status == 'cancelled' %}from-red-500 to-red-700
                {% else %}from-harrier-red to-harrier-dark{% endif %} px-6 py-5">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-14 h-14 bg-white bg-opacity-20 backdrop-blur-sm rounded-xl flex items-center justify-center mr-4 shadow-lg">
                            <i class="fas fa-eye text-white text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-white font-montserrat">
                                Import Request Details
                            </h3>
                            <p class="text-sm text-white text-opacity-90 font-raleway">Request #{{ import_request.id|stringformat:"05d" }} • {{ import_request.created_at|date:"M d, Y" }}</p>
                        </div>
                    </div>

                    <!-- Status Badge and Close Button -->
                    <div class="flex items-center space-x-4">
                        <div class="bg-white bg-opacity-20 backdrop-blur-sm rounded-xl px-4 py-2">
                            <span class="text-white font-semibold text-sm flex items-center">
                                <div class="w-3 h-3 bg-white rounded-full mr-2 animate-pulse"></div>
                                {{ import_request.get_status_display }}
                            </span>
                        </div>

                        <!-- Close Button -->
                        <button type="button"
                                class="text-white text-opacity-70 hover:text-white hover:bg-white hover:bg-opacity-20 rounded-lg p-2 transition-all duration-200"
                                @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)">
                            <span class="sr-only">Close</span>
                            <i class="fas fa-times text-lg"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Modal Body -->
            <div class="bg-gradient-to-br from-gray-50 to-white px-6 py-6 max-h-96 overflow-y-auto modal-body">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Customer Information -->
                    <div class="space-y-6">
                        <div class="info-card bg-gradient-to-br from-blue-50 to-indigo-50 border-l-4 border-blue-500">
                            <h4 class="info-card-title">
                                <i class="fas fa-user mr-2 text-blue-600"></i>
                                Customer Information
                            </h4>
                            <div class="space-y-4">
                                <div class="flex items-center p-3 bg-white bg-opacity-60 rounded-lg">
                                    <div class="w-14 h-14 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center text-white font-bold text-lg mr-4 shadow-lg">
                                        {{ import_request.customer.first_name.0|default:import_request.customer.username.0|upper }}{{ import_request.customer.last_name.0|default:''|upper }}
                                    </div>
                                    <div class="flex-1">
                                        <p class="font-semibold text-gray-900 text-lg">{{ import_request.customer.get_full_name|default:import_request.customer.username }}</p>
                                        <div class="flex items-center text-sm text-gray-600 mt-1">
                                            <i class="fas fa-envelope mr-2 text-blue-500"></i>
                                            {{ import_request.customer.email }}
                                        </div>
                                        {% if import_request.customer.phone %}
                                        <div class="flex items-center text-sm text-gray-600 mt-1">
                                            <i class="fas fa-phone mr-2 text-blue-500"></i>
                                            {{ import_request.customer.phone }}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Car Details -->
                        <div class="bg-blue-50 rounded-lg p-4">
                            <h4 class="text-lg font-semibold text-harrier-dark mb-4 flex items-center">
                                <i class="fas fa-car mr-2 text-blue-600"></i>
                                Vehicle Details
                            </h4>
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="text-sm font-medium text-gray-700">Brand</label>
                                    <p class="text-harrier-dark font-semibold">{{ import_request.brand }}</p>
                                </div>
                                <div>
                                    <label class="text-sm font-medium text-gray-700">Model</label>
                                    <p class="text-harrier-dark font-semibold">{{ import_request.model }}</p>
                                </div>
                                <div>
                                    <label class="text-sm font-medium text-gray-700">Year</label>
                                    <p class="text-harrier-dark font-semibold">{{ import_request.year }}</p>
                                </div>
                                <div>
                                    <label class="text-sm font-medium text-gray-700">Preferred Color</label>
                                    <p class="text-harrier-dark font-semibold">{{ import_request.preferred_color|default:'Not specified' }}</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Import Details -->
                        <div class="bg-green-50 rounded-lg p-4">
                            <h4 class="text-lg font-semibold text-harrier-dark mb-4 flex items-center">
                                <i class="fas fa-globe mr-2 text-green-600"></i>
                                Import Details
                            </h4>
                            <div class="grid grid-cols-1 gap-4">
                                <div>
                                    <label class="text-sm font-medium text-gray-700">Origin Country</label>
                                    <p class="text-harrier-dark font-semibold">{{ import_request.origin_country }}</p>
                                </div>
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="text-sm font-medium text-gray-700">Min Budget</label>
                                        <p class="text-harrier-dark font-semibold">KES {{ import_request.budget_min|floatformat:0 }}</p>
                                    </div>
                                    <div>
                                        <label class="text-sm font-medium text-gray-700">Max Budget</label>
                                        <p class="text-harrier-dark font-semibold">KES {{ import_request.budget_max|floatformat:0 }}</p>
                                    </div>
                                </div>
                                {% if import_request.estimated_cost %}
                                <div>
                                    <label class="text-sm font-medium text-gray-700">Estimated Cost</label>
                                    <p class="text-harrier-dark font-semibold">KES {{ import_request.estimated_cost|floatformat:0 }}</p>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <!-- Status and Tracking -->
                    <div class="space-y-6">
                        <!-- Status Information -->
                        <div class="bg-purple-50 rounded-lg p-4">
                            <h4 class="text-lg font-semibold text-harrier-dark mb-4 flex items-center">
                                <i class="fas fa-info-circle mr-2 text-purple-600"></i>
                                Status Information
                            </h4>
                            <div class="space-y-3">
                                <div>
                                    <label class="text-sm font-medium text-gray-700">Current Status</label>
                                    <p class="text-harrier-dark font-semibold">{{ import_request.get_status_display }}</p>
                                </div>
                                <div>
                                    <label class="text-sm font-medium text-gray-700">Created Date</label>
                                    <p class="text-harrier-dark font-semibold">{{ import_request.created_at|date:"M d, Y H:i" }}</p>
                                </div>
                                <div>
                                    <label class="text-sm font-medium text-gray-700">Last Updated</label>
                                    <p class="text-harrier-dark font-semibold">{{ import_request.updated_at|date:"M d, Y H:i" }}</p>
                                </div>
                                {% if import_request.tracking_number %}
                                <div>
                                    <label class="text-sm font-medium text-gray-700">Tracking Number</label>
                                    <p class="text-harrier-dark font-semibold font-mono">{{ import_request.tracking_number }}</p>
                                </div>
                                {% endif %}
                                {% if import_request.estimated_delivery %}
                                <div>
                                    <label class="text-sm font-medium text-gray-700">Estimated Delivery</label>
                                    <p class="text-harrier-dark font-semibold">{{ import_request.estimated_delivery|date:"M d, Y" }}</p>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <!-- Special Requirements -->
                        {% if import_request.special_requirements %}
                        <div class="bg-yellow-50 rounded-lg p-4">
                            <h4 class="text-lg font-semibold text-harrier-dark mb-4 flex items-center">
                                <i class="fas fa-star mr-2 text-yellow-600"></i>
                                Special Requirements
                            </h4>
                            <p class="text-gray-700 whitespace-pre-wrap">{{ import_request.special_requirements }}</p>
                        </div>
                        {% endif %}
                        
                        <!-- Admin Notes -->
                        {% if import_request.admin_notes %}
                        <div class="bg-red-50 rounded-lg p-4">
                            <h4 class="text-lg font-semibold text-harrier-dark mb-4 flex items-center">
                                <i class="fas fa-sticky-note mr-2 text-red-600"></i>
                                Admin Notes
                            </h4>
                            <p class="text-gray-700 whitespace-pre-wrap">{{ import_request.admin_notes }}</p>
                        </div>
                        {% endif %}
                        
                        <!-- Enhanced Progress Indicator -->
                        <div class="info-card bg-gradient-to-br from-purple-50 to-pink-50 border-l-4 border-purple-500">
                            <h4 class="info-card-title">
                                <i class="fas fa-chart-line mr-2 text-purple-600"></i>
                                Progress Timeline
                            </h4>

                            <!-- Progress Bar -->
                            <div class="mb-6">
                                <div class="w-full bg-gray-200 rounded-full h-4 shadow-inner">
                                    <div class="bg-gradient-to-r from-purple-500 to-pink-500 h-4 rounded-full transition-all duration-1000 ease-out relative overflow-hidden"
                                         style="width: {% if import_request.status == 'pending' %}12.5%{% elif import_request.status == 'on_quotation' %}25%{% elif import_request.status == 'processing' %}37.5%{% elif import_request.status == 'fee_paid' %}50%{% elif import_request.status == 'shipped' %}62.5%{% elif import_request.status == 'arrived' %}75%{% elif import_request.status == 'completed' %}100%{% elif import_request.status == 'cancelled' %}0%{% else %}0%{% endif %}">
                                        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30 animate-pulse"></div>
                                    </div>
                                </div>
                                <div class="text-sm font-semibold text-purple-700 mt-2 text-center">
                                    {% if import_request.status == 'pending' %}20%{% elif import_request.status == 'on_quotation' %}40%{% elif import_request.status == 'processing' %}60%{% elif import_request.status == 'fee_paid' %}80%{% elif import_request.status == 'completed' %}100%{% elif import_request.status == 'cancelled' %}0%{% else %}0%{% endif %} Complete
                                </div>
                            </div>

                            <!-- Status Steps -->
                            <div class="space-y-2">
                                {% for status_code, status_label in status_choices %}
                                <div class="flex items-center p-2 rounded-lg {% if status_code == import_request.status %}bg-purple-100 border border-purple-300{% else %}bg-white bg-opacity-50{% endif %}">
                                    <div class="w-3 h-3 rounded-full mr-3 {% if status_code == import_request.status %}bg-purple-500 animate-pulse{% elif status_code == 'pending' and import_request.status != 'pending' %}bg-green-500{% else %}bg-gray-300{% endif %}"></div>
                                    <span class="text-sm {% if status_code == import_request.status %}font-semibold text-purple-800{% else %}text-gray-600{% endif %}">
                                        {{ status_label }}
                                    </span>
                                    {% if status_code == import_request.status %}
                                    <span class="ml-auto text-xs bg-purple-500 text-white px-2 py-1 rounded-full">Current</span>
                                    {% endif %}
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Modal Footer -->
            <div class="bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-5 flex justify-between items-center">
                <div class="flex space-x-3">
                    <!-- Edit Button -->
                    <button type="button"
                            class="enhanced-btn enhanced-btn-secondary"
                            hx-get="{% url 'core:admin_import_request_edit_modal' import_request.id %}"
                            hx-target="body"
                            hx-swap="beforeend"
                            @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)">
                        <i class="fas fa-edit mr-2"></i>
                        <span>Edit Request</span>
                    </button>

                    <!-- Status Update Button -->
                    <button type="button"
                            class="enhanced-btn enhanced-btn-warning"
                            hx-get="{% url 'core:admin_import_request_status_modal' import_request.id %}"
                            hx-target="body"
                            hx-swap="beforeend"
                            @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)">
                        <i class="fas fa-sync mr-2"></i>
                        <span>Update Status</span>
                    </button>

                    <!-- Track Button (only enabled when completed) -->
                    {% if import_request.status == 'completed' %}
                    <button type="button"
                            class="enhanced-btn enhanced-btn-success"
                            hx-post="{% url 'core:admin_import_request_track' import_request.id %}"
                            hx-confirm="Move this request to tracking management?"
                            hx-target="#import-requests-table"
                            hx-swap="outerHTML"
                            @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)">
                        <i class="fas fa-route mr-2"></i>
                        <span>Start Tracking</span>
                    </button>
                    {% else %}
                    <button type="button"
                            class="enhanced-btn enhanced-btn-neutral"
                            disabled
                            title="Available when status is Completed">
                        <i class="fas fa-route mr-2"></i>
                        <span>Start Tracking</span>
                    </button>
                    {% endif %}
                </div>

                <div class="flex items-center space-x-3">
                    <div class="text-sm text-gray-600">
                        <i class="fas fa-clock mr-1"></i>
                        Last updated: {{ import_request.updated_at|date:"M d, Y H:i" }}
                    </div>
                    <button type="button"
                            class="enhanced-btn enhanced-btn-cancel"
                            @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)">
                        <i class="fas fa-times mr-2"></i>
                        <span>Close</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    /* Enhanced Modal Styles */
    .modal-panel {
        backdrop-filter: blur(20px);
        background: rgba(255, 255, 255, 0.98);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    }

    /* Information Cards */
    .info-card {
        @apply rounded-xl p-5 shadow-lg transition-all duration-300;
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.95);
    }

    .info-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
    }

    .info-card-title {
        @apply text-lg font-bold mb-4 flex items-center;
        font-family: 'Montserrat', sans-serif;
    }

    /* Enhanced Button Base Styles */
    .enhanced-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 12px 20px;
        font-weight: 600;
        font-size: 14px;
        line-height: 1.2;
        border-radius: 10px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        transform: translateY(0);
        font-family: 'Montserrat', sans-serif;
        min-width: 120px;
        max-width: 180px;
        height: 44px;
        border: 2px solid transparent;
        cursor: pointer;
        user-select: none;
        position: relative;
        overflow: visible;
        white-space: nowrap;
        box-sizing: border-box;
    }

    /* Simplified Cancel Button */
    .enhanced-btn-cancel {
        background: linear-gradient(135deg, #f9fafb, #f3f4f6);
        color: #374151;
        border: 2px solid #e5e7eb;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    }

    .enhanced-btn-cancel:hover {
        background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
        color: #1f2937;
        border-color: #d1d5db;
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
    }

    /* Secondary Button (Edit) */
    .enhanced-btn-secondary {
        background: linear-gradient(135deg, #2563eb, #3b82f6, #1d4ed8);
        background-size: 200% 100%;
        background-position: 0% 50%;
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 24px rgba(37, 99, 235, 0.4);
    }

    .enhanced-btn-secondary:hover {
        background-position: 100% 50%;
        transform: translateY(-3px);
        box-shadow: 0 12px 30px rgba(37, 99, 235, 0.5);
    }

    /* Success Button (Track) */
    .enhanced-btn-success {
        background: linear-gradient(135deg, #059669, #10b981, #047857);
        background-size: 200% 100%;
        background-position: 0% 50%;
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 24px rgba(5, 150, 105, 0.4);
    }

    .enhanced-btn-success:hover {
        background-position: 100% 50%;
        transform: translateY(-3px);
        box-shadow: 0 12px 30px rgba(5, 150, 105, 0.5);
    }

    /* Warning Button (Status Update) */
    .enhanced-btn-warning {
        background: linear-gradient(135deg, #d97706, #f59e0b, #b45309);
        background-size: 200% 100%;
        background-position: 0% 50%;
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 24px rgba(217, 119, 6, 0.4);
    }

    .enhanced-btn-warning:hover {
        background-position: 100% 50%;
        transform: translateY(-3px);
        box-shadow: 0 12px 30px rgba(217, 119, 6, 0.5);
    }

    /* Neutral Button (Disabled) */
    .enhanced-btn-neutral {
        background: linear-gradient(135deg, #f9fafb, #f3f4f6);
        color: #6b7280;
        border: 2px solid #e5e7eb;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    }

    .enhanced-btn-neutral:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none !important;
    }

    /* Status-specific animations */
    .animate-pulse {
        animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    }

    @keyframes pulse {
        0%, 100% {
            opacity: 1;
        }
        50% {
            opacity: .5;
        }
    }

    /* Mobile Responsiveness */
    @media (max-width: 768px) {
        .modal-panel {
            margin: 1rem;
            max-width: calc(100vw - 2rem);
        }

        .info-card {
            @apply p-3;
        }

        .enhanced-btn {
            padding: 0.75rem 1.5rem;
            font-size: 0.875rem;
            min-width: 120px;
        }

        .info-card-title {
            @apply text-base;
        }
    }

    /* Loading states */
    .htmx-request .enhanced-btn {
        opacity: 0.7;
        cursor: wait;
        pointer-events: none;
    }

    .htmx-request .enhanced-btn i {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    /* Accessibility */
    .enhanced-btn:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.4);
    }
</style>
