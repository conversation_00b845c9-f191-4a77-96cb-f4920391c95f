{% load static %}

<!-- Add Import Request Modal -->
<div class="fixed inset-0 z-50 overflow-y-auto"
     id="add-import-request-modal"
     x-data="{ show: false }"
     x-init="show = true"
     x-show="show"
     x-transition:enter="ease-out duration-300"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100"
     x-transition:leave="ease-in duration-200"
     x-transition:leave-start="opacity-100"
     x-transition:leave-end="opacity-0">

    <!-- Modal Backdrop -->
    <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity modal-backdrop"
         @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)"></div>
    
    <!-- Modal Container -->
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        
        <!-- Modal Panel -->
        <div class="inline-block align-bottom bg-white rounded-2xl text-left overflow-hidden shadow-2xl transform transition-all sm:my-8 sm:align-middle sm:max-w-3xl sm:w-full modal-panel"
             x-show="show"
             x-transition:enter="ease-out duration-400"
             x-transition:enter-start="opacity-0 translate-y-8 sm:translate-y-0 sm:scale-90"
             x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave="ease-in duration-300"
             x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave-end="opacity-0 translate-y-8 sm:translate-y-0 sm:scale-90">
            
            <!-- Modal Header -->
            <div class="bg-gradient-to-r from-harrier-red to-harrier-dark px-6 py-5 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-white bg-opacity-20 backdrop-blur-sm rounded-xl flex items-center justify-center mr-4 shadow-lg">
                            <i class="fas fa-plus text-white text-lg"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold text-white font-montserrat">
                                Add New Import Request
                            </h3>
                            <p class="text-sm text-white text-opacity-90 font-raleway">Create a new car import request for customer</p>
                        </div>
                    </div>

                    <!-- Close Button -->
                    <button type="button"
                            class="text-white text-opacity-70 hover:text-white hover:bg-white hover:bg-opacity-20 rounded-lg p-2 transition-all duration-200"
                            @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)">
                        <span class="sr-only">Close</span>
                        <i class="fas fa-times text-lg"></i>
                    </button>
                </div>
            </div>
            
            <!-- Modal Body -->
            <div class="bg-gradient-to-br from-gray-50 to-white px-6 py-6 max-h-96 overflow-y-auto modal-body">
                <form id="add-import-request-form"
                      hx-post="{% url 'core:admin_import_request_add' %}"
                      hx-target="#import-requests-table"
                      hx-swap="outerHTML"
                      hx-on::after-request="if(event.detail.successful) { show = false; setTimeout(() => $el.closest('.fixed').remove(), 200); }"
                      class="space-y-6"
                      x-data="{ isSubmitting: false }"
                      @submit="isSubmitting = true">
                    {% csrf_token %}
                    
                    <!-- Customer Selection -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="form-group">
                            <label for="customer" class="form-label">
                                <i class="fas fa-user mr-2 text-harrier-red"></i>
                                Customer <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <select name="customer" id="customer" required
                                        class="form-input pl-10">
                                    <option value="">Select Customer</option>
                                    {% for user in customers %}
                                    <option value="{{ user.id }}">{{ user.get_full_name|default:user.username }} ({{ user.email }})</option>
                                    {% endfor %}
                                </select>
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-chevron-down text-gray-400"></i>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="origin_country" class="form-label">
                                <i class="fas fa-globe mr-2 text-harrier-red"></i>
                                Origin Country <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <select name="origin_country" id="origin_country" required
                                        class="form-input pl-10">
                                    <option value="">Select Country</option>
                                    <option value="Japan">🇯🇵 Japan</option>
                                    <option value="UK">🇬🇧 United Kingdom</option>
                                    <option value="Germany">🇩🇪 Germany</option>
                                    <option value="USA">🇺🇸 United States</option>
                                    <option value="South Korea">🇰🇷 South Korea</option>
                                    <option value="Other">🌍 Other</option>
                                </select>
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-chevron-down text-gray-400"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Car Details -->
                    <div class="bg-white rounded-xl p-4 border border-gray-200 shadow-sm">
                        <h4 class="text-lg font-semibold text-harrier-dark mb-4 flex items-center">
                            <i class="fas fa-car mr-2 text-harrier-red"></i>
                            Vehicle Information
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div class="form-group">
                                <label for="brand" class="form-label">
                                    Brand <span class="text-red-500">*</span>
                                </label>
                                <input type="text" name="brand" id="brand" required
                                       class="form-input"
                                       placeholder="e.g., Toyota, Honda">
                            </div>

                            <div class="form-group">
                                <label for="model" class="form-label">
                                    Model <span class="text-red-500">*</span>
                                </label>
                                <input type="text" name="model" id="model" required
                                       class="form-input"
                                       placeholder="e.g., Camry, Civic">
                            </div>

                            <div class="form-group">
                                <label for="year" class="form-label">
                                    Year <span class="text-red-500">*</span>
                                </label>
                                <input type="number" name="year" id="year" required min="1990" max="2025"
                                       class="form-input"
                                       placeholder="2020">
                            </div>
                        </div>
                    </div>
                    
                    <!-- Budget Range -->
                    <div class="bg-white rounded-xl p-4 border border-gray-200 shadow-sm">
                        <h4 class="text-lg font-semibold text-harrier-dark mb-4 flex items-center">
                            <i class="fas fa-money-bill-wave mr-2 text-green-600"></i>
                            Budget Information
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="form-group">
                                <label for="budget_min" class="form-label">
                                    <i class="fas fa-arrow-down mr-1 text-green-500"></i>
                                    Minimum Budget (KES) <span class="text-red-500">*</span>
                                </label>
                                <div class="relative">
                                    <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">KES</span>
                                    <input type="number" name="budget_min" id="budget_min" required min="0" step="1000"
                                           class="form-input pl-12"
                                           placeholder="1,000,000">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="budget_max" class="form-label">
                                    <i class="fas fa-arrow-up mr-1 text-green-500"></i>
                                    Maximum Budget (KES) <span class="text-red-500">*</span>
                                </label>
                                <div class="relative">
                                    <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">KES</span>
                                    <input type="number" name="budget_max" id="budget_max" required min="0" step="1000"
                                           class="form-input pl-12"
                                           placeholder="2,000,000">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Additional Details -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="preferred_color" class="block text-sm font-medium text-gray-700 mb-2">
                                Preferred Color
                            </label>
                            <input type="text" name="preferred_color" id="preferred_color"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent transition-all duration-200"
                                   placeholder="e.g., White, Black">
                        </div>
                        
                        <div>
                            <label for="estimated_cost" class="block text-sm font-medium text-gray-700 mb-2">
                                Estimated Cost (KES)
                            </label>
                            <input type="number" name="estimated_cost" id="estimated_cost" min="0" step="1000"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent transition-all duration-200"
                                   placeholder="1500000">
                        </div>
                    </div>
                    
                    <!-- Special Requirements -->
                    <div>
                        <label for="special_requirements" class="block text-sm font-medium text-gray-700 mb-2">
                            Special Requirements
                        </label>
                        <textarea name="special_requirements" id="special_requirements" rows="3"
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent transition-all duration-200"
                                  placeholder="Any specific requirements or preferences..."></textarea>
                    </div>
                    
                    <!-- Admin Notes -->
                    <div>
                        <label for="admin_notes" class="block text-sm font-medium text-gray-700 mb-2">
                            Admin Notes
                        </label>
                        <textarea name="admin_notes" id="admin_notes" rows="2"
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent transition-all duration-200"
                                  placeholder="Internal notes for this request..."></textarea>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex flex-col sm:flex-row gap-4 justify-center mt-6 pt-6 border-t border-gray-200">
                        <!-- Cancel Button -->
                        <button type="button"
                                class="enhanced-btn enhanced-btn-cancel"
                                @click="show = false; setTimeout(() => $el.closest('.fixed').remove(), 200)">
                            <i class="fas fa-times mr-2"></i>
                            <span>Cancel</span>
                        </button>

                        <!-- Submit Button -->
                        <button type="submit"
                                class="enhanced-btn enhanced-btn-submit"
                                :disabled="isSubmitting">
                            <i class="fas fa-plus mr-2" x-show="!isSubmitting"></i>
                            <i class="fas fa-spinner fa-spin mr-2" x-show="isSubmitting"></i>
                            <span x-text="isSubmitting ? 'Creating Request...' : 'Create Request'"></span>
                        </button>
                    </div>
                </form>
            </div>

            <!-- Enhanced Modal Footer -->
            <div class="bg-gradient-to-r from-gray-50 via-white to-gray-50 px-6 py-6 border-t border-gray-200">
                <!-- Info Section -->
                <div class="flex items-center justify-center mb-6">
                    <div class="flex items-center text-sm text-gray-600 bg-blue-50 px-4 py-2 rounded-lg border border-blue-200">
                        <div class="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-info text-white text-xs"></i>
                        </div>
                        <span class="font-medium font-raleway">All required fields must be completed before submission</span>
                    </div>
                </div>

                <!-- Additional Info -->
                <div class="mt-4 text-center">
                    <p class="text-xs text-gray-500 font-raleway">
                        Customer will be notified via email once the request is created
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    /* Enhanced Modal Styles */
    .modal-panel {
        backdrop-filter: blur(20px);
        background: rgba(255, 255, 255, 0.98);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    }

    /* Enhanced Form Styling */
    .form-group {
        @apply space-y-3 mb-6;
        position: relative;
    }

    .form-label {
        @apply block text-sm font-bold text-gray-800 flex items-center mb-2;
        font-family: 'Montserrat', sans-serif;
        letter-spacing: 0.025em;
    }

    .form-label i {
        margin-right: 8px;
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
    }

    .form-input {
        @apply w-full border-2 rounded-2xl transition-all duration-300 ease-out;
        font-family: 'Inter', 'Raleway', sans-serif;
        font-size: 15px;
        font-weight: 500;
        line-height: 1.5;
        padding: 16px 20px;

        /* Modern glassmorphism background */
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
        backdrop-filter: blur(12px);
        border: 2px solid rgba(226, 232, 240, 0.8);

        /* Subtle shadow */
        box-shadow:
            0 1px 3px rgba(0, 0, 0, 0.05),
            0 0 0 1px rgba(255, 255, 255, 0.05) inset;

        /* Text styling */
        color: #1e293b;
        letter-spacing: 0.01em;
    }

    .form-input:hover {
        border-color: rgba(220, 38, 38, 0.3);
        background: linear-gradient(135deg, rgba(255, 255, 255, 1) 0%, rgba(254, 242, 242, 0.95) 100%);
        box-shadow:
            0 2px 8px rgba(220, 38, 38, 0.08),
            0 0 0 1px rgba(255, 255, 255, 0.1) inset;
        transform: translateY(-1px);
    }

    .form-input:focus {
        outline: none;
        border-color: #dc2626;
        background: linear-gradient(135deg, rgba(255, 255, 255, 1) 0%, rgba(254, 242, 242, 0.98) 100%);
        box-shadow:
            0 4px 20px rgba(220, 38, 38, 0.15),
            0 0 0 4px rgba(220, 38, 38, 0.1),
            0 0 0 1px rgba(255, 255, 255, 0.2) inset;
        transform: translateY(-2px);
    }

    .form-input::placeholder {
        color: #94a3b8;
        font-weight: 400;
        opacity: 0.8;
    }

    /* Select specific styling */
    select.form-input {
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%23dc2626' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
        background-position: right 16px center;
        background-repeat: no-repeat;
        background-size: 16px 12px;
        padding-right: 48px;
        cursor: pointer;
    }

    /* Textarea specific styling */
    textarea.form-input {
        resize: vertical;
        min-height: 120px;
        line-height: 1.6;
        padding: 16px 20px;
    }

    /* Harrier Color System */
    :root {
        --harrier-red-50: #fef2f2;
        --harrier-red-100: #fee2e2;
        --harrier-red-200: #fecaca;
        --harrier-red-300: #fca5a5;
        --harrier-red-400: #f87171;
        --harrier-red-500: #ef4444;
        --harrier-red-600: #dc2626;
        --harrier-red-700: #b91c1c;
        --harrier-red-800: #991b1b;
        --harrier-red-900: #7f1d1d;

        --harrier-dark-50: #f9fafb;
        --harrier-dark-100: #f3f4f6;
        --harrier-dark-200: #e5e7eb;
        --harrier-dark-300: #d1d5db;
        --harrier-dark-400: #9ca3af;
        --harrier-dark-500: #6b7280;
        --harrier-dark-600: #4b5563;
        --harrier-dark-700: #374151;
        --harrier-dark-800: #1f2937;
        --harrier-dark-900: #111827;

        --harrier-blue-50: #eff6ff;
        --harrier-blue-100: #dbeafe;
        --harrier-blue-200: #bfdbfe;
        --harrier-blue-300: #93c5fd;
        --harrier-blue-400: #60a5fa;
        --harrier-blue-500: #3b82f6;
        --harrier-blue-600: #2563eb;
        --harrier-blue-700: #1d4ed8;
        --harrier-blue-800: #1e40af;
        --harrier-blue-900: #1e3a8a;
    }

    /* Enhanced Button Base Styles */
    .enhanced-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 12px 20px;
        font-weight: 600;
        font-size: 14px;
        line-height: 1.2;
        border-radius: 10px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        transform: translateY(0);
        font-family: 'Montserrat', sans-serif;
        min-width: 120px;
        max-width: 180px;
        height: 44px;
        border: 2px solid transparent;
        cursor: pointer;
        user-select: none;
        position: relative;
        overflow: visible;
        white-space: nowrap;
        box-sizing: border-box;
    }

    .enhanced-btn::before {
        content: '';
        position: absolute;
        inset: 0;
        border-radius: inherit;
        padding: 2px;
        background: linear-gradient(135deg, rgba(255,255,255,0.3), rgba(255,255,255,0.1), rgba(0,0,0,0.1));
        mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
        mask-composite: xor;
        -webkit-mask-composite: xor;
        pointer-events: none;
    }

    .enhanced-btn:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(var(--harrier-red-500), 0.3);
    }

    /* Simplified Button Content */
    .btn-content {
        position: relative;
        z-index: 100;
        display: flex;
        align-items: center;
        justify-content: center;
        color: inherit;
    }

    .btn-icon-wrapper {
        width: 1.5rem;
        height: 1.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 0.75rem;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
    }

    .btn-icon {
        font-size: 1rem;
        color: inherit;
        transition: all 0.3s ease;
    }

    .btn-text {
        font-weight: 600;
        color: inherit;
        transition: all 0.3s ease;
    }

    /* Simple Background Effects */
    .btn-texture {
        position: absolute;
        inset: 0;
        border-radius: inherit;
        opacity: 0.1;
        background: radial-gradient(circle at 30% 30%, rgba(255,255,255,0.3), transparent 70%);
        pointer-events: none;
        z-index: 1;
    }

    .btn-noise {
        /* Removed for simplicity */
        display: none;
    }

    /* Simplified Cancel Button */
    .enhanced-btn-cancel {
        background: linear-gradient(135deg, #f9fafb, #f3f4f6);
        color: #374151;
        border: 2px solid #e5e7eb;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    }

    .enhanced-btn-cancel:hover {
        background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
        color: #1f2937;
        border-color: #d1d5db;
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
    }

    .enhanced-btn-cancel:hover .btn-icon {
        transform: rotate(90deg);
    }

    .enhanced-btn-cancel:hover .btn-icon-wrapper {
        background: rgba(75, 85, 99, 0.1);
        transform: scale(1.05);
    }

    .enhanced-btn-cancel:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(156, 163, 175, 0.3);
    }

    .enhanced-btn-cancel:active {
        transform: translateY(0);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    /* Simplified Submit Button */
    .enhanced-btn-submit {
        background: linear-gradient(135deg, #dc2626, #ef4444, #b91c1c);
        background-size: 200% 100%;
        background-position: 0% 50%;
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 24px rgba(220, 38, 38, 0.4);
    }

    .enhanced-btn-submit:hover {
        background-position: 100% 50%;
        transform: translateY(-3px);
        box-shadow: 0 12px 30px rgba(220, 38, 38, 0.5);
    }

    .enhanced-btn-submit:hover .btn-icon {
        transform: scale(1.2) rotate(180deg);
    }

    .enhanced-btn-submit:hover .btn-icon-wrapper {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(1.1);
    }

    .enhanced-btn-submit:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(248, 113, 113, 0.4);
    }

    .enhanced-btn-submit:active {
        transform: translateY(-1px);
        box-shadow: 0 6px 16px rgba(220, 38, 38, 0.4);
    }

    .enhanced-btn-submit:disabled,
    .enhanced-btn-submit.btn-loading {
        opacity: 0.8;
        cursor: not-allowed;
        transform: none !important;
        background: linear-gradient(135deg, #b91c1c, #991b1b);
        box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3) !important;
    }

    .enhanced-btn-submit:disabled:hover,
    .enhanced-btn-submit.btn-loading:hover {
        transform: none !important;
        box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3) !important;
    }

    /* Save Button Styles (for consistency across modals) */
    .enhanced-btn-save {
        background: linear-gradient(135deg,
            var(--harrier-blue-600) 0%,
            var(--harrier-blue-500) 15%,
            var(--harrier-blue-700) 35%,
            var(--harrier-blue-800) 65%,
            var(--harrier-blue-700) 85%,
            var(--harrier-blue-600) 100%);
        background-size: 300% 100%;
        background-position: 0% 50%;
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.1);
        box-shadow:
            0 8px 24px rgba(59, 130, 246, 0.4),
            0 4px 12px rgba(30, 64, 175, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.2),
            inset 0 -1px 0 rgba(0, 0, 0, 0.2);
        position: relative;
    }

    .enhanced-btn-save::after {
        content: '';
        position: absolute;
        inset: 1px;
        border-radius: calc(1rem - 1px);
        background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.15) 0%,
            rgba(255, 255, 255, 0.05) 30%,
            transparent 50%,
            rgba(0, 0, 0, 0.05) 70%,
            rgba(0, 0, 0, 0.1) 100%);
        pointer-events: none;
        z-index: 1;
    }

    .enhanced-btn-save:hover {
        background-position: 100% 50%;
        transform: translateY(-4px);
        box-shadow:
            0 16px 40px rgba(59, 130, 246, 0.5),
            0 8px 20px rgba(30, 64, 175, 0.4),
            inset 0 1px 0 rgba(255, 255, 255, 0.3),
            inset 0 -1px 0 rgba(0, 0, 0, 0.3);
        border-color: rgba(255, 255, 255, 0.2);
    }

    .enhanced-btn-save:hover .btn-icon {
        transform: scale(1.3) rotate(360deg);
    }

    .enhanced-btn-save:hover .btn-icon-wrapper {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.3);
        transform: scale(1.1);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    }

    .enhanced-btn-save:focus {
        box-shadow:
            0 0 0 3px rgba(var(--harrier-blue-400), 0.4),
            0 8px 24px rgba(59, 130, 246, 0.4);
    }

    .enhanced-btn-save:active {
        transform: translateY(-2px);
        box-shadow:
            0 8px 16px rgba(59, 130, 246, 0.4),
            inset 0 2px 4px rgba(0, 0, 0, 0.2);
        background-position: 50% 50%;
    }

    .enhanced-btn-save:disabled,
    .enhanced-btn-save.btn-loading {
        opacity: 0.8;
        cursor: not-allowed;
        transform: none !important;
        box-shadow:
            0 4px 12px rgba(59, 130, 246, 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
        background-position: 0% 50% !important;
        background: linear-gradient(135deg,
            var(--harrier-blue-700) 0%,
            var(--harrier-blue-600) 50%,
            var(--harrier-blue-800) 100%);
    }

    /* Enhanced Ripple Effect with Fixed Z-Index */
    .btn-ripple {
        position: absolute;
        inset: 0;
        border-radius: inherit;
        background: radial-gradient(circle,
            rgba(255, 255, 255, 0.4) 0%,
            rgba(255, 255, 255, 0.2) 30%,
            transparent 70%);
        transform: scale(0);
        opacity: 0;
        transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        z-index: 10;
    }

    .enhanced-btn:active .btn-ripple {
        transform: scale(4);
        opacity: 1;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    /* Enhanced Shine Effect with Fixed Z-Index */
    .btn-shine {
        position: absolute;
        inset: 0;
        border-radius: inherit;
        background: linear-gradient(45deg,
            transparent 30%,
            rgba(255, 255, 255, 0.3) 50%,
            transparent 70%);
        transform: translateX(-100%);
        transition: transform 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        z-index: 20;
    }

    .enhanced-btn-submit:hover .btn-shine,
    .enhanced-btn-save:hover .btn-shine {
        transform: translateX(100%);
    }

    /* Pulse Glow Effect for Submit Button */
    .btn-glow {
        position: absolute;
        inset: -2px;
        border-radius: calc(1rem + 2px);
        background: linear-gradient(135deg,
            var(--harrier-red-400),
            var(--harrier-red-600),
            var(--harrier-dark-600));
        opacity: 0;
        filter: blur(8px);
        transition: opacity 0.4s ease;
        z-index: -1;
    }

    .enhanced-btn-submit:hover .btn-glow {
        opacity: 0.6;
        animation: pulse-glow 2s ease-in-out infinite;
    }

    @keyframes pulse-glow {
        0%, 100% {
            opacity: 0.6;
            transform: scale(1);
        }
        50% {
            opacity: 0.8;
            transform: scale(1.02);
        }
    }

    /* Loading State Animation */
    .btn-loading .btn-icon {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
    }

    /* Mobile Responsiveness */
    @media (max-width: 640px) {
        .enhanced-btn {
            @apply px-6 py-3 text-sm min-w-140;
            min-width: 140px;
        }

        .btn-icon-wrapper {
            @apply w-5 h-5 mr-2;
        }

        .btn-icon {
            @apply text-base;
        }
    }

    /* Accessibility Enhancements */
    @media (prefers-reduced-motion: reduce) {
        .enhanced-btn,
        .btn-content,
        .btn-icon-wrapper,
        .btn-icon,
        .btn-text,
        .btn-ripple,
        .btn-shine {
            transition: none !important;
            animation: none !important;
        }

        .enhanced-btn:hover {
            transform: none !important;
        }
    }

    /* High Contrast Mode */
    @media (prefers-contrast: high) {
        .enhanced-btn-cancel {
            @apply border-black text-black;
        }

        .enhanced-btn-submit {
            @apply bg-black text-white;
        }
    }

    /* Section Cards */
    .bg-white.rounded-xl {
        backdrop-filter: blur(10px);
        background: rgba(255, 255, 255, 0.95);
        border: 1px solid rgba(255, 255, 255, 0.3);
        transition: all 0.3s ease;
    }

    .bg-white.rounded-xl:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    /* Loading Animation */
    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.5; }
    }

    .htmx-request .form-input {
        animation: pulse 1.5s ease-in-out infinite;
    }

    /* Mobile Responsiveness */
    @media (max-width: 768px) {
        .modal-panel {
            margin: 1rem;
            max-width: calc(100vw - 2rem);
        }

        .form-input {
            @apply px-3 py-2 text-sm;
        }

        .modal-btn {
            @apply px-4 py-2 text-xs;
        }
    }

    /* Accessibility */
    .form-input:focus {
        outline: 2px solid rgba(220, 38, 38, 0.5);
        outline-offset: 2px;
    }

    .modal-btn:focus {
        outline: 2px solid rgba(220, 38, 38, 0.5);
        outline-offset: 2px;
    }
</style>
