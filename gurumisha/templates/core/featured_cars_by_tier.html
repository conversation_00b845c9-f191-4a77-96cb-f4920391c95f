{% extends 'base.html' %}
{% load static %}
{% load promotion_tags %}

{% block title %}{{ page_title }} - Gurumisha Motors{% endblock %}
{% block meta_description %}{{ meta_description }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/car-listing-enhancements.css' %}">
<style>
.tier-badge-large {
    background: linear-gradient(135deg, var(--tier-color-1), var(--tier-color-2));
    transition: all 0.3s ease;
}

.tier-badge-large:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.tier-platinum { --tier-color-1: #9333ea; --tier-color-2: #7c3aed; }
.tier-gold { --tier-color-1: #eab308; --tier-color-2: #ca8a04; }
.tier-silver { --tier-color-1: #6b7280; --tier-color-2: #4b5563; }
.tier-bronze { --tier-color-1: #d97706; --tier-color-2: #b45309; }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="bg-gradient-to-r from-gray-900 to-gray-800 text-white py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl md:text-5xl font-bold mb-4">
                <i class="fas fa-crown mr-3 text-yellow-400"></i>{{ page_title }}
            </h1>
            <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                {% if current_tier %}
                    Discover our premium {{ current_tier }} tier featured vehicles with exceptional quality and performance.
                {% else %}
                    Browse our complete collection of featured vehicles across all premium tiers.
                {% endif %}
            </p>
        </div>
    </div>
</section>

<!-- Tier Navigation -->
<section class="bg-white py-8 border-b">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-wrap justify-center gap-4 mb-8">
            <a href="{% url 'core:featured_cars_by_tier' %}" 
               class="tier-badge-large px-6 py-3 rounded-xl text-white font-bold transition-all {% if not current_tier %}ring-4 ring-blue-300{% endif %}"
               style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">
                <i class="fas fa-star mr-2"></i>All Featured
                <span class="ml-2 bg-white bg-opacity-20 px-2 py-1 rounded-full text-xs">
                    {{ tier_stats.bronze.count|add:tier_stats.silver.count|add:tier_stats.gold.count|add:tier_stats.platinum.count }}
                </span>
            </a>
            
            {% for tier in valid_tiers %}
                <a href="{% url 'core:featured_cars_by_tier' tier %}" 
                   class="tier-badge-large tier-{{ tier }} px-6 py-3 rounded-xl text-white font-bold transition-all {% if current_tier == tier %}ring-4 ring-yellow-300{% endif %}">
                    {% if tier == 'platinum' %}
                        <i class="fas fa-gem mr-2"></i>Platinum
                    {% elif tier == 'gold' %}
                        <i class="fas fa-crown mr-2"></i>Gold
                    {% elif tier == 'silver' %}
                        <i class="fas fa-medal mr-2"></i>Silver
                    {% elif tier == 'bronze' %}
                        <i class="fas fa-medal mr-2"></i>Bronze
                    {% endif %}
                    <span class="ml-2 bg-white bg-opacity-20 px-2 py-1 rounded-full text-xs">
                        {{ tier_stats|get_item:tier.count|default:0 }}
                    </span>
                </a>
            {% endfor %}
        </div>
        
        <!-- Tier Statistics -->
        {% if current_tier %}
            <div class="bg-gray-50 rounded-xl p-6">
                <h3 class="text-lg font-bold text-gray-900 mb-4">{{ current_tier|title }} Tier Statistics</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-blue-600">{{ tier_stats|get_item:current_tier.count|default:0 }}</div>
                        <div class="text-sm text-gray-600">Total Cars</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-yellow-600">{{ tier_stats|get_item:current_tier.avg_rating|floatformat:1|default:"0.0" }}</div>
                        <div class="text-sm text-gray-600">Average Rating</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-green-600">KSh {{ tier_stats|get_item:current_tier.avg_price|floatformat:0|default:"0" }}</div>
                        <div class="text-sm text-gray-600">Average Price</div>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
</section>

<!-- Featured Cars Grid -->
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {% if cars %}
            <!-- Filter Options -->
            <div class="mb-8 flex flex-wrap items-center justify-between">
                <div class="flex items-center space-x-4">
                    <span class="text-gray-700 font-medium">Sort by:</span>
                    <select id="sortSelect" class="border border-gray-300 rounded-lg px-4 py-2" onchange="updateSort()">
                        <option value="tier">Tier Priority</option>
                        <option value="rating">Highest Rated</option>
                        <option value="price_low">Price: Low to High</option>
                        <option value="price_high">Price: High to Low</option>
                        <option value="newest">Recently Added</option>
                    </select>
                </div>
                
                <div class="text-gray-600">
                    Showing {{ cars.start_index }}-{{ cars.end_index }} of {{ cars.paginator.count }} cars
                </div>
            </div>
            
            <!-- Cars Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
                {% for car in cars %}
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group">
                        <div class="relative overflow-hidden">
                            {% if car.main_image %}
                                <img src="{{ car.main_image.url }}" alt="{{ car.title }}" 
                                     class="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-500">
                            {% else %}
                                <div class="w-full h-48 bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                                    <i class="fas fa-car text-gray-400 text-4xl"></i>
                                </div>
                            {% endif %}
                            
                            <!-- Promotion Badges -->
                            {% promotion_badge car %}
                        </div>
                        
                        <div class="p-6">
                            <!-- Car Title -->
                            <h3 class="text-lg font-bold text-gray-900 mb-2 group-hover:text-red-600 transition-colors">
                                <a href="{% url 'core:car_detail' car.pk %}">{{ car.title }}</a>
                            </h3>
                            
                            <!-- Car Details -->
                            <div class="text-gray-600 mb-3">
                                <p class="font-medium">{{ car.brand.name }} {{ car.model.name }}</p>
                                <p class="text-sm">{{ car.year }} • {{ car.mileage|floatformat:0 }} km</p>
                            </div>
                            
                            <!-- Star Rating -->
                            {% if car.calculated_rating > 0 %}
                                <div class="mb-3">
                                    {% star_rating_display car.calculated_rating show_number=True size="text-sm" %}
                                </div>
                            {% endif %}
                            
                            <!-- Pricing -->
                            <div class="mb-4">
                                <span class="text-xl font-bold text-gray-900">KSh {{ car.price|floatformat:0 }}</span>
                            </div>
                            
                            <!-- Action Buttons -->
                            <div class="flex space-x-3">
                                <a href="{% url 'core:car_detail' car.pk %}" 
                                   class="flex-1 bg-red-600 text-white text-center py-2 px-4 rounded-lg hover:bg-red-700 transition-colors font-medium">
                                    View Details
                                </a>
                                <button class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                                    <i class="fas fa-heart"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
            
            <!-- Pagination -->
            {% if cars.has_other_pages %}
                <div class="mt-12 flex justify-center">
                    <nav class="flex items-center space-x-2">
                        {% if cars.has_previous %}
                            <a href="?page={{ cars.previous_page_number }}" 
                               class="px-3 py-2 text-gray-500 hover:text-gray-700">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        {% endif %}
                        
                        {% for num in cars.paginator.page_range %}
                            {% if cars.number == num %}
                                <span class="px-3 py-2 bg-red-600 text-white rounded">{{ num }}</span>
                            {% else %}
                                <a href="?page={{ num }}" 
                                   class="px-3 py-2 text-gray-500 hover:text-gray-700">{{ num }}</a>
                            {% endif %}
                        {% endfor %}
                        
                        {% if cars.has_next %}
                            <a href="?page={{ cars.next_page_number }}" 
                               class="px-3 py-2 text-gray-500 hover:text-gray-700">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        {% endif %}
                    </nav>
                </div>
            {% endif %}
            
        {% else %}
            <!-- No Cars Found -->
            <div class="text-center py-16">
                <i class="fas fa-crown text-gray-300 text-6xl mb-4"></i>
                <h3 class="text-2xl font-bold text-gray-900 mb-2">No Featured Cars Found</h3>
                {% if current_tier %}
                    <p class="text-gray-600 mb-4">No {{ current_tier }} tier featured cars are currently available.</p>
                    <a href="{% url 'core:featured_cars_by_tier' %}" class="text-red-600 hover:text-red-700 font-medium">
                        <i class="fas fa-star mr-1"></i>View all featured cars
                    </a>
                {% else %}
                    <p class="text-gray-600 mb-4">No featured cars are currently available.</p>
                    <a href="{% url 'core:car_list' %}" class="text-red-600 hover:text-red-700 font-medium">
                        <i class="fas fa-car mr-1"></i>Browse all cars
                    </a>
                {% endif %}
            </div>
        {% endif %}
    </div>
</section>

<!-- Call to Action -->
<section class="bg-gray-900 text-white py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl font-bold mb-4">Want Your Car Featured?</h2>
        <p class="text-xl text-gray-300 mb-8">Join our premium vendor program and get your vehicles featured</p>
        <a href="{% url 'core:vendor_signup' %}" class="bg-red-600 text-white px-8 py-3 rounded-lg hover:bg-red-700 transition-colors font-medium">
            Become a Vendor
        </a>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
function updateSort() {
    const sortValue = document.getElementById('sortSelect').value;
    const url = new URL(window.location);
    url.searchParams.set('sort', sortValue);
    window.location.href = url.toString();
}

// Set current sort value
document.addEventListener('DOMContentLoaded', function() {
    const urlParams = new URLSearchParams(window.location.search);
    const currentSort = urlParams.get('sort') || 'tier';
    document.getElementById('sortSelect').value = currentSort;
});
</script>
{% endblock %}
