<!-- Responsive Table Component -->
<!-- Usage: {% include 'components/responsive_table.html' with table_data=data table_headers=headers table_id='my-table' %} -->

<div class="dashboard-card">
    <!-- Desktop Table View -->
    <div class="hidden md:block overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200" id="{{ table_id|default:'responsive-table' }}">
            <thead class="bg-gray-50">
                <tr>
                    {% for header in table_headers %}
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            {{ header.label }}
                        </th>
                    {% endfor %}
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for row in table_data %}
                    <tr class="hover:bg-gray-50">
                        {% for cell in row %}
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ cell|safe }}
                            </td>
                        {% endfor %}
                    </tr>
                {% empty %}
                    <tr>
                        <td colspan="{{ table_headers|length }}" class="px-6 py-8 text-center text-gray-500">
                            {{ empty_message|default:"No data available" }}
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Mobile Card View -->
    <div class="md:hidden">
        {% for row in table_data %}
            <div class="border-b border-gray-200 p-4 last:border-b-0">
                {% for cell, header in row|zip:table_headers %}
                    <div class="flex justify-between items-start py-2">
                        <span class="text-sm font-medium text-gray-600">{{ header.label }}:</span>
                        <span class="text-sm text-gray-900 text-right ml-4">{{ cell|safe }}</span>
                    </div>
                {% endfor %}
                
                <!-- Mobile Actions -->
                {% if mobile_actions %}
                    <div class="flex flex-wrap gap-2 mt-3 pt-3 border-t border-gray-100">
                        {% for action in mobile_actions %}
                            <button type="button" 
                                    class="px-3 py-1 text-xs rounded {{ action.class|default:'bg-harrier-red text-white hover:bg-harrier-dark' }}"
                                    onclick="{{ action.onclick }}">
                                {% if action.icon %}<i class="{{ action.icon }} mr-1"></i>{% endif %}
                                {{ action.label }}
                            </button>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
        {% empty %}
            <div class="p-8 text-center text-gray-500">
                <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-inbox text-2xl text-gray-400"></i>
                </div>
                <p>{{ empty_message|default:"No data available" }}</p>
            </div>
        {% endfor %}
    </div>

    <!-- Pagination for Mobile -->
    {% if show_pagination and table_data.has_other_pages %}
        <div class="px-4 py-3 border-t border-gray-200 md:hidden">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-700">
                    Page {{ table_data.number }} of {{ table_data.paginator.num_pages }}
                </div>
                <div class="flex space-x-2">
                    {% if table_data.has_previous %}
                        <a href="?page={{ table_data.previous_page_number }}" 
                           class="px-3 py-2 text-sm bg-white border border-gray-300 rounded hover:bg-gray-50">
                            Previous
                        </a>
                    {% endif %}
                    {% if table_data.has_next %}
                        <a href="?page={{ table_data.next_page_number }}" 
                           class="px-3 py-2 text-sm bg-white border border-gray-300 rounded hover:bg-gray-50">
                            Next
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>
    {% endif %}
</div>

<!-- Mobile-specific JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle mobile table interactions
    const mobileCards = document.querySelectorAll('.md\\:hidden > div');
    
    mobileCards.forEach(card => {
        // Add touch feedback
        card.addEventListener('touchstart', function() {
            this.style.backgroundColor = '#f9fafb';
        });
        
        card.addEventListener('touchend', function() {
            setTimeout(() => {
                this.style.backgroundColor = '';
            }, 150);
        });
    });
    
    // Handle swipe gestures for pagination
    let startX = 0;
    let startY = 0;
    
    document.addEventListener('touchstart', function(e) {
        startX = e.touches[0].clientX;
        startY = e.touches[0].clientY;
    });
    
    document.addEventListener('touchend', function(e) {
        if (!startX || !startY) return;
        
        const endX = e.changedTouches[0].clientX;
        const endY = e.changedTouches[0].clientY;
        
        const diffX = startX - endX;
        const diffY = startY - endY;
        
        // Only handle horizontal swipes
        if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
            const pagination = document.querySelector('.pagination');
            if (!pagination) return;
            
            if (diffX > 0) {
                // Swipe left - next page
                const nextLink = pagination.querySelector('a[href*="page="]');
                if (nextLink && nextLink.textContent.includes('Next')) {
                    nextLink.click();
                }
            } else {
                // Swipe right - previous page
                const prevLink = pagination.querySelector('a[href*="page="]');
                if (prevLink && prevLink.textContent.includes('Previous')) {
                    prevLink.click();
                }
            }
        }
        
        startX = 0;
        startY = 0;
    });
});
</script>

<style>
/* Mobile table enhancements */
@media (max-width: 768px) {
    .responsive-table-card {
        margin-bottom: 0.5rem;
        border-radius: 0.5rem;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        transition: all 0.2s ease;
    }
    
    .responsive-table-card:active {
        transform: scale(0.98);
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    }
    
    .mobile-table-row {
        padding: 1rem;
        border-bottom: 1px solid #e5e7eb;
    }
    
    .mobile-table-row:last-child {
        border-bottom: none;
    }
    
    .mobile-field {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 0.5rem 0;
        border-bottom: 1px solid #f3f4f6;
    }
    
    .mobile-field:last-child {
        border-bottom: none;
    }
    
    .mobile-field-label {
        font-weight: 500;
        color: #6b7280;
        font-size: 0.875rem;
        flex-shrink: 0;
        margin-right: 1rem;
    }
    
    .mobile-field-value {
        text-align: right;
        color: #1f2937;
        font-size: 0.875rem;
        word-break: break-word;
    }
    
    .mobile-actions {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #f3f4f6;
    }
    
    .mobile-action-btn {
        flex: 1;
        min-width: 0;
        padding: 0.5rem 0.75rem;
        font-size: 0.75rem;
        border-radius: 0.375rem;
        text-align: center;
        transition: all 0.2s ease;
    }
    
    .mobile-action-btn:active {
        transform: scale(0.95);
    }
}

/* Loading state for mobile tables */
.mobile-table-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem;
    color: #6b7280;
}

.mobile-table-loading::after {
    content: '';
    width: 1rem;
    height: 1rem;
    border: 2px solid #e5e7eb;
    border-top-color: #ed6663;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 0.5rem;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Pull-to-refresh indicator */
.pull-to-refresh {
    position: relative;
    overflow: hidden;
}

.pull-to-refresh::before {
    content: '';
    position: absolute;
    top: -50px;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 30px;
    border: 2px solid #e5e7eb;
    border-top-color: #ed6663;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    opacity: 0;
    transition: all 0.3s ease;
}

.pull-to-refresh.pulling::before {
    top: 10px;
    opacity: 1;
}
</style>
