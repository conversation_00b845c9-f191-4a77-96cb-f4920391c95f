<!-- Reusable Form Field Component -->
{% load static %}

<div class="{% if field_class %}{{ field_class }}{% endif %}">
    <label for="{{ field.id_for_label }}" class="block text-sm font-semibold text-harrier-dark mb-2">
        {{ label }}
        {% if required %}<span class="text-harrier-red">*</span>{% endif %}
    </label>
    
    {% if field_type == 'textarea' %}
        <textarea 
            name="{{ field.name }}" 
            id="{{ field.id_for_label }}"
            {% if required %}required{% endif %}
            {% if placeholder %}placeholder="{{ placeholder }}"{% endif %}
            {% if rows %}rows="{{ rows }}"{% endif %}
            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent transition-all duration-200"
        >{{ field.value|default:'' }}</textarea>
    {% elif field_type == 'select' %}
        <select 
            name="{{ field.name }}" 
            id="{{ field.id_for_label }}"
            {% if required %}required{% endif %}
            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent transition-all duration-200"
        >
            {% if placeholder %}<option value="">{{ placeholder }}</option>{% endif %}
            {% for option in options %}
                <option value="{{ option.value }}" {% if option.value == field.value %}selected{% endif %}>
                    {{ option.label }}
                </option>
            {% endfor %}
        </select>
    {% elif field_type == 'checkbox' %}
        <div class="flex items-center">
            <input 
                type="checkbox" 
                name="{{ field.name }}" 
                id="{{ field.id_for_label }}"
                {% if field.value %}checked{% endif %}
                class="w-4 h-4 text-harrier-red bg-gray-100 border-gray-300 rounded focus:ring-harrier-red focus:ring-2"
            >
            {% if checkbox_label %}
                <label for="{{ field.id_for_label }}" class="ml-2 text-sm text-gray-600">
                    {{ checkbox_label }}
                </label>
            {% endif %}
        </div>
    {% elif field_type == 'file' %}
        <input 
            type="file" 
            name="{{ field.name }}" 
            id="{{ field.id_for_label }}"
            {% if required %}required{% endif %}
            {% if accept %}accept="{{ accept }}"{% endif %}
            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent transition-all duration-200"
        >
    {% else %}
        <input 
            type="{{ field_type|default:'text' }}" 
            name="{{ field.name }}" 
            id="{{ field.id_for_label }}"
            value="{{ field.value|default:'' }}"
            {% if required %}required{% endif %}
            {% if placeholder %}placeholder="{{ placeholder }}"{% endif %}
            {% if min %}min="{{ min }}"{% endif %}
            {% if max %}max="{{ max }}"{% endif %}
            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-harrier-red focus:border-transparent transition-all duration-200"
        >
    {% endif %}
    
    {% if help_text %}
        <p class="mt-1 text-sm text-gray-500">{{ help_text }}</p>
    {% endif %}
    
    {% if field.errors %}
        <div class="mt-1 text-sm text-red-600">
            {{ field.errors.0 }}
        </div>
    {% endif %}
</div>
