{% load static %}

<!-- Lazy Loading Content Component with HTMX -->
<!-- Usage: {% include 'components/lazy_content.html' with url='dashboard/lazy-stats/' skeleton_type='stats' trigger='revealed' %} -->

<div class="lazy-content-container" 
     hx-get="{{ url }}"
     hx-trigger="{{ trigger|default:'revealed' }}"
     hx-swap="outerHTML"
     hx-indicator="#loading-{{ skeleton_type|default:'card' }}"
     data-skeleton="{{ skeleton_type|default:'card' }}">
    
    <!-- Loading Skeleton based on type -->
    {% if skeleton_type == 'stats' %}
        {% include 'components/loading_skeletons.html' with skeleton_id='skeleton-stats' %}
    {% elif skeleton_type == 'table' %}
        {% include 'components/loading_skeletons.html' with skeleton_id='skeleton-table' %}
    {% elif skeleton_type == 'image-grid' %}
        {% include 'components/loading_skeletons.html' with skeleton_id='skeleton-image-grid' %}
    {% elif skeleton_type == 'list' %}
        {% include 'components/loading_skeletons.html' with skeleton_id='skeleton-list' %}
    {% elif skeleton_type == 'product-card' %}
        {% include 'components/loading_skeletons.html' with skeleton_id='skeleton-product-card' %}
    {% else %}
        {% include 'components/loading_skeletons.html' with skeleton_id='skeleton-card' %}
    {% endif %}
    
    <!-- Loading indicator -->
    <div id="loading-{{ skeleton_type|default:'card' }}" class="htmx-indicator">
        {% include 'components/loading_skeletons.html' with skeleton_id='loading-spinner' %}
    </div>
</div>

<!-- Enhanced HTMX Lazy Loading Script -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Enhanced intersection observer for HTMX lazy loading
    const lazyContentObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const container = entry.target;
                
                // Add loading state
                container.classList.add('loading');
                
                // Trigger HTMX load
                htmx.trigger(container, 'revealed');
                
                // Stop observing this element
                observer.unobserve(container);
            }
        });
    }, {
        rootMargin: '100px 0px', // Start loading 100px before coming into view
        threshold: 0.1
    });
    
    // Observe all lazy content containers
    document.querySelectorAll('.lazy-content-container').forEach(container => {
        lazyContentObserver.observe(container);
    });
    
    // HTMX event listeners for better UX
    document.body.addEventListener('htmx:beforeRequest', function(event) {
        const target = event.target;
        if (target.classList.contains('lazy-content-container')) {
            // Show loading state
            target.classList.add('loading');
            
            // Add subtle loading animation
            target.style.opacity = '0.7';
            target.style.transition = 'opacity 0.3s ease';
        }
    });
    
    document.body.addEventListener('htmx:afterRequest', function(event) {
        const target = event.target;
        if (target.classList.contains('lazy-content-container')) {
            // Remove loading state
            target.classList.remove('loading');
            target.style.opacity = '1';
            
            // Add fade-in animation for new content
            if (event.detail.successful) {
                target.style.animation = 'fadeInUp 0.5s ease-out';
            }
        }
    });
    
    document.body.addEventListener('htmx:responseError', function(event) {
        const target = event.target;
        if (target.classList.contains('lazy-content-container')) {
            // Handle error state
            target.innerHTML = `
                <div class="dashboard-card p-6 text-center">
                    <div class="text-gray-500 mb-2">
                        <svg class="w-8 h-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z">
                            </path>
                        </svg>
                    </div>
                    <p class="text-sm text-gray-600">Failed to load content</p>
                    <button class="mt-2 text-harrier-red hover:text-harrier-dark text-sm font-medium"
                            onclick="location.reload()">
                        Try Again
                    </button>
                </div>
            `;
            target.classList.remove('loading');
            target.style.opacity = '1';
        }
    });
});

// Utility function to manually trigger lazy loading
function triggerLazyLoad(selector) {
    document.querySelectorAll(selector).forEach(element => {
        if (element.classList.contains('lazy-content-container')) {
            htmx.trigger(element, 'revealed');
        }
    });
}

// Progressive loading for dashboard sections
function loadDashboardSections() {
    const sections = [
        '.lazy-stats',
        '.lazy-recent-activity', 
        '.lazy-charts',
        '.lazy-tables'
    ];
    
    sections.forEach((selector, index) => {
        setTimeout(() => {
            triggerLazyLoad(selector);
        }, index * 200); // Stagger loading by 200ms
    });
}
</script>

<!-- Lazy Content Styles -->
<style>
    .lazy-content-container {
        transition: opacity 0.3s ease;
    }
    
    .lazy-content-container.loading {
        pointer-events: none;
    }
    
    /* Fade in animation for loaded content */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    /* HTMX indicator styles */
    .htmx-indicator {
        display: none;
    }
    
    .htmx-request .htmx-indicator {
        display: block;
    }
    
    .htmx-request.htmx-indicator {
        display: block;
    }
    
    /* Loading state overlay */
    .lazy-content-container.loading::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.8);
        z-index: 10;
        pointer-events: none;
    }
    
    /* Smooth transitions for content swapping */
    .lazy-content-container > * {
        transition: opacity 0.3s ease, transform 0.3s ease;
    }
    
    /* Error state styling */
    .lazy-content-error {
        background-color: #fef2f2;
        border: 1px solid #fecaca;
        color: #dc2626;
    }
</style>
