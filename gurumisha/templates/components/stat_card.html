<!-- Stat Card Component -->
<!-- Usage: {% include 'components/stat_card.html' with value="123" label="Total Users" icon="fas fa-users" color="harrier-red" %} -->

<div class="stat-card text-center {% if classes %}{{ classes }}{% endif %}">
    {% if icon %}
        <div class="w-12 h-12 mx-auto mb-4 bg-{{ color|default:'harrier-red' }}-100 rounded-lg flex items-center justify-center">
            <i class="{{ icon }} text-{{ color|default:'harrier-red' }}-600 text-xl"></i>
        </div>
    {% endif %}
    
    <div class="stat-value {% if color %}text-{{ color }}-600{% endif %}">{{ value|default:0 }}</div>
    <div class="stat-label">{{ label }}</div>
    
    {% if description %}
        <p class="text-sm text-gray-600 mt-2">{{ description }}</p>
    {% endif %}
    
    {% if change %}
        <div class="mt-2">
            <span class="text-sm {% if change > 0 %}text-green-600{% elif change < 0 %}text-red-600{% else %}text-gray-600{% endif %}">
                {% if change > 0 %}+{% endif %}{{ change }}%
            </span>
        </div>
    {% endif %}
</div>
