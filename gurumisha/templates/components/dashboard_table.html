<!-- Dashboard Table Component -->
<!-- Usage: {% include 'components/dashboard_table.html' with headers=headers rows=rows title="Table Title" %} -->

<div class="dashboard-card">
    {% if title %}
        <div class="p-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-heading font-bold text-harrier-dark">{{ title }}</h3>
                {% if action_url and action_text %}
                    <a href="{{ action_url }}" class="text-sm text-harrier-red hover:text-harrier-dark font-medium">{{ action_text }}</a>
                {% endif %}
            </div>
        </div>
    {% endif %}
    
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            {% if headers %}
                <thead class="bg-gray-50">
                    <tr>
                        {% for header in headers %}
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                {{ header }}
                            </th>
                        {% endfor %}
                    </tr>
                </thead>
            {% endif %}
            
            <tbody class="bg-white divide-y divide-gray-200">
                {% if rows %}
                    {% for row in rows %}
                        <tr class="hover:bg-gray-50">
                            {% for cell in row %}
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ cell|safe }}
                                </td>
                            {% endfor %}
                        </tr>
                    {% endfor %}
                {% else %}
                    <tr>
                        <td colspan="{{ headers|length|default:1 }}" class="px-6 py-8 text-center text-gray-500">
                            {% if empty_message %}
                                {{ empty_message }}
                            {% else %}
                                No data available.
                            {% endif %}
                        </td>
                    </tr>
                {% endif %}
            </tbody>
        </table>
    </div>
    
    {% if pagination %}
        <div class="px-6 py-3 border-t border-gray-200">
            <!-- Pagination controls would go here -->
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-700">
                    Showing {{ pagination.start }} to {{ pagination.end }} of {{ pagination.total }} results
                </div>
                <div class="flex space-x-2">
                    {% if pagination.has_previous %}
                        <a href="{{ pagination.previous_url }}" class="px-3 py-1 text-sm bg-white border border-gray-300 rounded hover:bg-gray-50">Previous</a>
                    {% endif %}
                    {% if pagination.has_next %}
                        <a href="{{ pagination.next_url }}" class="px-3 py-1 text-sm bg-white border border-gray-300 rounded hover:bg-gray-50">Next</a>
                    {% endif %}
                </div>
            </div>
        </div>
    {% endif %}
</div>
