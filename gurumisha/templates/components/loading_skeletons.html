{% load static %}

<!-- Loading Skeleton Components -->

<!-- Card Skeleton -->
<div class="skeleton-card" id="skeleton-card">
    <div class="dashboard-card animate-pulse">
        <div class="p-6">
            <!-- Header skeleton -->
            <div class="flex items-center justify-between mb-4">
                <div class="h-6 bg-gray-300 rounded w-1/3"></div>
                <div class="h-4 bg-gray-300 rounded w-16"></div>
            </div>
            
            <!-- Content skeleton -->
            <div class="space-y-3">
                <div class="h-4 bg-gray-300 rounded w-full"></div>
                <div class="h-4 bg-gray-300 rounded w-5/6"></div>
                <div class="h-4 bg-gray-300 rounded w-4/6"></div>
            </div>
            
            <!-- Action skeleton -->
            <div class="mt-4 flex justify-end">
                <div class="h-8 bg-gray-300 rounded w-20"></div>
            </div>
        </div>
    </div>
</div>

<!-- Table Skeleton -->
<div class="skeleton-table" id="skeleton-table">
    <div class="dashboard-card animate-pulse">
        <div class="p-6 border-b border-gray-200">
            <div class="h-6 bg-gray-300 rounded w-1/4"></div>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3">
                            <div class="h-4 bg-gray-300 rounded w-20"></div>
                        </th>
                        <th class="px-6 py-3">
                            <div class="h-4 bg-gray-300 rounded w-24"></div>
                        </th>
                        <th class="px-6 py-3">
                            <div class="h-4 bg-gray-300 rounded w-16"></div>
                        </th>
                        <th class="px-6 py-3">
                            <div class="h-4 bg-gray-300 rounded w-20"></div>
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for i in "12345" %}
                    <tr>
                        <td class="px-6 py-4">
                            <div class="h-4 bg-gray-300 rounded w-32"></div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="h-4 bg-gray-300 rounded w-24"></div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="h-4 bg-gray-300 rounded w-16"></div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="h-4 bg-gray-300 rounded w-20"></div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Image Grid Skeleton -->
<div class="skeleton-image-grid" id="skeleton-image-grid">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {% for i in "123456" %}
        <div class="dashboard-card animate-pulse">
            <!-- Image skeleton -->
            <div class="h-48 bg-gray-300 rounded-t-lg"></div>
            
            <!-- Content skeleton -->
            <div class="p-4">
                <div class="h-5 bg-gray-300 rounded w-3/4 mb-2"></div>
                <div class="h-4 bg-gray-300 rounded w-1/2 mb-3"></div>
                
                <!-- Stats skeleton -->
                <div class="flex justify-between items-center">
                    <div class="h-4 bg-gray-300 rounded w-16"></div>
                    <div class="h-4 bg-gray-300 rounded w-20"></div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<!-- Stats Cards Skeleton -->
<div class="skeleton-stats" id="skeleton-stats">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {% for i in "1234" %}
        <div class="dashboard-card p-6 animate-pulse">
            <div class="flex items-center">
                <div class="flex-shrink-0 w-12 h-12 bg-gray-300 rounded-lg"></div>
                <div class="ml-4 flex-1">
                    <div class="h-5 bg-gray-300 rounded w-3/4 mb-2"></div>
                    <div class="h-4 bg-gray-300 rounded w-1/2"></div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<!-- List Item Skeleton -->
<div class="skeleton-list" id="skeleton-list">
    <div class="space-y-4">
        {% for i in "12345" %}
        <div class="border border-gray-200 rounded-lg p-4 animate-pulse">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gray-300 rounded-lg mr-4"></div>
                    <div>
                        <div class="h-5 bg-gray-300 rounded w-32 mb-2"></div>
                        <div class="h-4 bg-gray-300 rounded w-24"></div>
                    </div>
                </div>
                <div class="flex space-x-2">
                    <div class="h-8 bg-gray-300 rounded w-16"></div>
                    <div class="h-8 bg-gray-300 rounded w-16"></div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<!-- Product Card Skeleton with Image -->
<div class="skeleton-product-card" id="skeleton-product-card">
    <div class="dashboard-card animate-pulse">
        <div class="relative">
            <!-- Image skeleton -->
            <div class="h-64 bg-gray-300 rounded-t-lg relative overflow-hidden">
                <!-- Shimmer effect -->
                <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30 transform -skew-x-12 animate-shimmer"></div>
            </div>
            
            <!-- Badge skeleton -->
            <div class="absolute top-4 left-4 h-6 bg-gray-300 rounded-full w-16"></div>
        </div>
        
        <div class="p-6">
            <!-- Title skeleton -->
            <div class="h-6 bg-gray-300 rounded w-3/4 mb-3"></div>
            
            <!-- Description skeleton -->
            <div class="space-y-2 mb-4">
                <div class="h-4 bg-gray-300 rounded w-full"></div>
                <div class="h-4 bg-gray-300 rounded w-5/6"></div>
            </div>
            
            <!-- Price and details skeleton -->
            <div class="flex justify-between items-center mb-4">
                <div class="h-6 bg-gray-300 rounded w-24"></div>
                <div class="h-4 bg-gray-300 rounded w-20"></div>
            </div>
            
            <!-- Action buttons skeleton -->
            <div class="flex space-x-2">
                <div class="h-10 bg-gray-300 rounded flex-1"></div>
                <div class="h-10 bg-gray-300 rounded w-10"></div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Spinner -->
<div class="loading-spinner" id="loading-spinner">
    <div class="flex items-center justify-center py-8">
        <div class="relative">
            <!-- Outer ring -->
            <div class="w-12 h-12 border-4 border-gray-200 rounded-full"></div>
            <!-- Inner spinning ring -->
            <div class="absolute top-0 left-0 w-12 h-12 border-4 border-transparent border-t-harrier-red rounded-full animate-spin"></div>
        </div>
        <span class="ml-3 text-gray-600 font-medium">Loading...</span>
    </div>
</div>

<!-- Inline Loading Indicator -->
<div class="inline-loading" id="inline-loading">
    <div class="flex items-center justify-center space-x-2 py-2">
        <div class="w-2 h-2 bg-harrier-red rounded-full animate-bounce" style="animation-delay: 0ms"></div>
        <div class="w-2 h-2 bg-harrier-red rounded-full animate-bounce" style="animation-delay: 150ms"></div>
        <div class="w-2 h-2 bg-harrier-red rounded-full animate-bounce" style="animation-delay: 300ms"></div>
    </div>
</div>

<!-- Custom Skeleton Styles -->
<style>
    /* Shimmer animation for image skeletons */
    @keyframes shimmer {
        0% {
            transform: translateX(-100%) skewX(-12deg);
        }
        100% {
            transform: translateX(200%) skewX(-12deg);
        }
    }
    
    .animate-shimmer {
        animation: shimmer 2s infinite;
    }
    
    /* Pulse animation override for better skeleton effect */
    .animate-pulse {
        animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    }
    
    @keyframes pulse {
        0%, 100% {
            opacity: 1;
        }
        50% {
            opacity: 0.7;
        }
    }
    
    /* Skeleton specific styles */
    .skeleton-card,
    .skeleton-table,
    .skeleton-image-grid,
    .skeleton-stats,
    .skeleton-list,
    .skeleton-product-card {
        transition: opacity 0.3s ease-in-out;
    }
    
    /* Hide skeleton when content loads */
    .skeleton-hidden {
        opacity: 0;
        pointer-events: none;
    }
</style>
