/* Car Listing Page Enhancements */

/* Enhanced Mobile Responsiveness */
@media (max-width: 768px) {
    /* Hero Section Mobile Optimizations */
    .hero-content h1 {
        font-size: 2.5rem !important;
        line-height: 1.2;
    }
    
    .hero-content p {
        font-size: 1.125rem !important;
    }
    
    /* Filter Sidebar Mobile */
    #filter-container {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease-in-out;
    }
    
    #filter-container.show {
        max-height: 2000px;
    }
    
    /* Car Grid Mobile */
    #cars-grid {
        grid-template-columns: 1fr !important;
        gap: 1rem;
    }
    
    /* Brand Section Mobile */
    .brand-grid {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    /* Extra small screens */
    .hero-content h1 {
        font-size: 2rem !important;
    }
    
    .hero-content .bg-white\/10 {
        padding: 1rem !important;
    }
    
    .hero-content form {
        flex-direction: column !important;
    }
    
    .hero-content button {
        margin-top: 0.5rem;
    }
}

/* Accessibility Enhancements */

/* Focus States */
button:focus,
input:focus,
select:focus,
a:focus {
    outline: 2px solid #DC2626;
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .bg-gradient-to-r {
        background: #DC2626 !important;
    }
    
    .text-gradient {
        color: #DC2626 !important;
        background: none !important;
        -webkit-text-fill-color: unset !important;
    }
    
    .shadow-lg {
        border: 2px solid #000;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    
    .group-hover\:scale-110 {
        transform: none !important;
    }
    
    .hover\:-translate-y-1:hover {
        transform: none !important;
    }
}

/* Screen Reader Only Content */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Enhanced Loading States */
.htmx-indicator {
    display: none;
}

.htmx-request .htmx-indicator {
    display: block;
}

.htmx-request #cars-grid {
    opacity: 0.5;
    pointer-events: none;
}

/* Smooth Transitions */
.filter-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced Hover States for Touch Devices */
@media (hover: none) {
    .group:hover .group-hover\:opacity-100 {
        opacity: 1;
    }
    
    .group:hover .group-hover\:translate-y-0 {
        transform: translateY(0);
    }
}

/* Custom Scrollbar for Filter Sidebar */
#filter-container::-webkit-scrollbar {
    width: 6px;
}

#filter-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

#filter-container::-webkit-scrollbar-thumb {
    background: #DC2626;
    border-radius: 3px;
}

#filter-container::-webkit-scrollbar-thumb:hover {
    background: #B91C1C;
}

/* Enhanced Card Animations */
.car-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.car-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Loading Skeleton Animation */
@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

/* Enhanced Typography */
.heading-enhanced {
    font-family: 'Saira Condensed', sans-serif;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    line-height: 1.2;
}

/* Price Display Enhancement */
.price-display {
    font-family: 'Saira Condensed', sans-serif;
    font-weight: 700;
    color: #DC2626;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Filter Badge Styles */
.filter-badge {
    background: linear-gradient(135deg, #DC2626, #EF4444);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Enhanced Button Styles */
.btn-enhanced {
    background: linear-gradient(135deg, #DC2626, #EF4444);
    color: white;
    font-weight: 700;
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    border: none;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.btn-enhanced:hover {
    background: linear-gradient(135deg, #B91C1C, #DC2626);
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.btn-enhanced:active {
    transform: translateY(0);
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .bg-white {
        background-color: #1f2937 !important;
        color: #f9fafb !important;
    }
    
    .text-gray-600 {
        color: #d1d5db !important;
    }
    
    .border-gray-300 {
        border-color: #4b5563 !important;
    }
}

/* Pill Section Styles */
.pill-button {
    display: inline-flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    background: white;
    border: 2px solid #e5e7eb;
    border-radius: 9999px;
    color: #6b7280;
    font-weight: 600;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.pill-button:hover {
    border-color: #DC2626;
    color: #DC2626;
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.pill-button.pill-active {
    background: linear-gradient(135deg, #DC2626, #EF4444);
    border-color: #DC2626;
    color: white;
    box-shadow: 0 10px 15px -3px rgba(220, 38, 38, 0.3);
}

.pill-count {
    background: rgba(255, 255, 255, 0.2);
    color: inherit;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 700;
    margin-left: 0.5rem;
    min-width: 1.5rem;
    text-align: center;
}

.pill-active .pill-count {
    background: rgba(255, 255, 255, 0.3);
    color: white;
}

.pill-button:not(.pill-active) .pill-count {
    background: #f3f4f6;
    color: #6b7280;
}

/* Pill Button Animation */
.pill-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.pill-button:hover::before {
    left: 100%;
}

/* Responsive Pill Buttons */
@media (max-width: 640px) {
    .pill-button {
        padding: 0.5rem 1rem;
        font-size: 0.75rem;
        flex: 1;
        justify-content: center;
        min-width: 0;
    }

    .pill-button span:not(.pill-count) {
        display: none;
    }

    .pill-button i {
        margin-right: 0 !important;
    }

    .pill-count {
        margin-left: 0.25rem;
        font-size: 0.625rem;
        padding: 0.125rem 0.375rem;
    }
}

/* Pill Loading State */
.pill-loading {
    opacity: 0.6;
    pointer-events: none;
}

.pill-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 1rem;
    height: 1rem;
    margin: -0.5rem 0 0 -0.5rem;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }

    .car-card {
        break-inside: avoid;
        box-shadow: none !important;
        border: 1px solid #ccc;
    }

    .bg-gradient-to-r {
        background: #DC2626 !important;
        -webkit-print-color-adjust: exact;
    }

    .pill-button {
        display: none !important;
    }
}
