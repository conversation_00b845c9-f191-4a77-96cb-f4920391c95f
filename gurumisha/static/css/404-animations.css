/* 404 Page Animations and Effects */

/* Import Modern Fonts */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800;900&family=Inter:wght@300;400;500;600;700;800&family=Raleway:wght@300;400;500;600;700;800&display=swap');

/* Base Animation Classes */
.animate-fade-in-up {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.animate-fade-in-up.animate-in {
    opacity: 1;
    transform: translateY(0);
}

.animate-slide-in-up {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.animate-slide-in-up.animate-in {
    opacity: 1;
    transform: translateY(0);
}

/* Animation Delays */
.animation-delay-200 { animation-delay: 0.2s; transition-delay: 0.2s; }
.animation-delay-400 { animation-delay: 0.4s; transition-delay: 0.4s; }
.animation-delay-600 { animation-delay: 0.6s; transition-delay: 0.6s; }
.animation-delay-800 { animation-delay: 0.8s; transition-delay: 0.8s; }
.animation-delay-1000 { animation-delay: 1.0s; transition-delay: 1.0s; }
.animation-delay-100 { animation-delay: 0.1s; transition-delay: 0.1s; }
.animation-delay-200 { animation-delay: 0.2s; transition-delay: 0.2s; }
.animation-delay-300 { animation-delay: 0.3s; transition-delay: 0.3s; }
.animation-delay-400 { animation-delay: 0.4s; transition-delay: 0.4s; }

/* Custom Keyframe Animations */
@keyframes pulse-slow {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes bounce-slow {
    0%, 20%, 53%, 80%, 100% { transform: translate3d(0, 0, 0); }
    40%, 43% { transform: translate3d(0, -15px, 0); }
    70% { transform: translate3d(0, -7px, 0); }
    90% { transform: translate3d(0, -2px, 0); }
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    25% { transform: translateY(-10px) rotate(1deg); }
    50% { transform: translateY(-5px) rotate(-1deg); }
    75% { transform: translateY(-15px) rotate(0.5deg); }
}

@keyframes float-reverse {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    25% { transform: translateY(-15px) rotate(-1deg); }
    50% { transform: translateY(-8px) rotate(1deg); }
    75% { transform: translateY(-12px) rotate(-0.5deg); }
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* Apply Animations */
.animate-pulse-slow {
    animation: pulse-slow 3s ease-in-out infinite;
}

.animate-bounce-slow {
    animation: bounce-slow 2s ease-in-out infinite;
}

/* Floating Car Animations */
.floating-car {
    position: absolute;
    animation: float 6s ease-in-out infinite;
}

.floating-car-1 {
    top: 10%;
    left: 5%;
    animation: float 8s ease-in-out infinite;
}

.floating-car-2 {
    top: 20%;
    right: 8%;
    animation: float-reverse 10s ease-in-out infinite;
}

.floating-car-3 {
    bottom: 25%;
    left: 10%;
    animation: float 12s ease-in-out infinite;
}

.floating-car-4 {
    bottom: 15%;
    right: 15%;
    animation: float-reverse 9s ease-in-out infinite;
}

/* Shimmer Loading Effect */
.shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

/* Hover Micro-interactions */
.hover-lift {
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.hover-scale {
    transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.hover-scale:hover {
    transform: scale(1.05);
}

/* Button Hover Effects */
.btn-hover-effect {
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.btn-hover-effect::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-hover-effect:hover::before {
    left: 100%;
}

/* Card Hover Effects */
.card-hover {
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border: 1px solid transparent;
}

.card-hover:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    border-color: #DC2626;
}

/* Image Hover Effects */
.image-hover-zoom {
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.image-hover-zoom img {
    transition: transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.image-hover-zoom:hover img {
    transform: scale(1.1);
}

/* Loading Spinner */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #DC2626;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Animations */
@media (max-width: 768px) {
    .animate-fade-in-up,
    .animate-slide-in-up {
        transform: translateY(20px);
    }
    
    .floating-car {
        display: none; /* Hide floating cars on mobile for performance */
    }
    
    .animate-bounce-slow {
        animation-duration: 1.5s;
    }
}

/* Accessibility - Respect prefers-reduced-motion */
@media (prefers-reduced-motion: reduce) {
    .animate-fade-in-up,
    .animate-slide-in-up,
    .animate-pulse-slow,
    .animate-bounce-slow,
    .floating-car {
        animation: none;
        transition: none;
    }
    
    .animate-fade-in-up,
    .animate-slide-in-up {
        opacity: 1;
        transform: none;
    }
}

/* Focus States for Accessibility */
.focus-ring:focus {
    outline: 2px solid #DC2626;
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .floating-car {
        opacity: 0.5 !important;
    }
    
    .animate-pulse-slow {
        animation: none;
    }
}
