/* Global Animations and Effects for Guru<PERSON>ha Motors */

/* Modern Typography System */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800;900&family=Raleway:wght@300;400;500;600;700;800&family=Inter:wght@300;400;500;600;700;800&display=swap');

/* Root Variables for Consistent Animations */
:root {
    --animation-duration-fast: 0.2s;
    --animation-duration-normal: 0.3s;
    --animation-duration-slow: 0.6s;
    --animation-easing-smooth: cubic-bezier(0.25, 0.46, 0.45, 0.94);
    --animation-easing-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --animation-easing-elegant: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Base Typography Classes */
.font-montserrat { font-family: 'Montserrat', 'Inter', sans-serif !important; }
.font-raleway { font-family: 'Raleway', 'Inter', sans-serif !important; }
.font-inter { font-family: 'Inter', system-ui, sans-serif !important; }

/* Global Smooth Transitions */
* {
    transition: color var(--animation-duration-normal) var(--animation-easing-smooth),
                background-color var(--animation-duration-normal) var(--animation-easing-smooth),
                border-color var(--animation-duration-normal) var(--animation-easing-smooth),
                box-shadow var(--animation-duration-normal) var(--animation-easing-smooth),
                transform var(--animation-duration-normal) var(--animation-easing-smooth);
}

/* Entrance Animations */
.animate-entrance {
    opacity: 0;
    transform: translateY(30px);
    transition: all var(--animation-duration-slow) var(--animation-easing-smooth);
}

.animate-entrance.in-view {
    opacity: 1;
    transform: translateY(0);
}

.animate-slide-in-left {
    opacity: 0;
    transform: translateX(-50px);
    transition: all var(--animation-duration-slow) var(--animation-easing-smooth);
}

.animate-slide-in-left.in-view {
    opacity: 1;
    transform: translateX(0);
}

.animate-slide-in-right {
    opacity: 0;
    transform: translateX(50px);
    transition: all var(--animation-duration-slow) var(--animation-easing-smooth);
}

.animate-slide-in-right.in-view {
    opacity: 1;
    transform: translateX(0);
}

.animate-scale-in {
    opacity: 0;
    transform: scale(0.8);
    transition: all var(--animation-duration-slow) var(--animation-easing-bounce);
}

.animate-scale-in.in-view {
    opacity: 1;
    transform: scale(1);
}

/* Staggered Animation Delays */
.animate-delay-100 { transition-delay: 0.1s; }
.animate-delay-200 { transition-delay: 0.2s; }
.animate-delay-300 { transition-delay: 0.3s; }
.animate-delay-400 { transition-delay: 0.4s; }
.animate-delay-500 { transition-delay: 0.5s; }
.animate-delay-600 { transition-delay: 0.6s; }

/* Hover Micro-interactions */
.hover-lift {
    transition: transform var(--animation-duration-normal) var(--animation-easing-smooth),
                box-shadow var(--animation-duration-normal) var(--animation-easing-smooth);
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.hover-scale {
    transition: transform var(--animation-duration-normal) var(--animation-easing-smooth);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-scale-sm:hover {
    transform: scale(1.02);
}

.hover-glow {
    transition: box-shadow var(--animation-duration-normal) var(--animation-easing-smooth);
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(220, 38, 38, 0.3);
}

/* Button Animations */
.btn-animate {
    position: relative;
    overflow: hidden;
    transition: all var(--animation-duration-normal) var(--animation-easing-smooth);
}

.btn-animate::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s var(--animation-easing-smooth);
}

.btn-animate:hover::before {
    left: 100%;
}

.btn-animate:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* Card Animations */
.card-animate {
    transition: all var(--animation-duration-normal) var(--animation-easing-smooth);
    border: 1px solid transparent;
}

.card-animate:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    border-color: #DC2626;
}

/* Image Hover Effects */
.image-zoom {
    overflow: hidden;
}

.image-zoom img {
    transition: transform var(--animation-duration-slow) var(--animation-easing-smooth);
}

.image-zoom:hover img {
    transform: scale(1.1);
}

.image-overlay {
    position: relative;
    overflow: hidden;
}

.image-overlay::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(220, 38, 38, 0.8), rgba(30, 58, 138, 0.8));
    opacity: 0;
    transition: opacity var(--animation-duration-normal) var(--animation-easing-smooth);
}

.image-overlay:hover::after {
    opacity: 1;
}

/* Loading Animations */
.loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

.loading-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #DC2626;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Navigation Animations */
.nav-item {
    position: relative;
    transition: all var(--animation-duration-normal) var(--animation-easing-smooth);
}

.nav-item::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    width: 0;
    height: 2px;
    background: #DC2626;
    transition: all var(--animation-duration-normal) var(--animation-easing-smooth);
    transform: translateX(-50%);
}

.nav-item:hover::after,
.nav-item.active::after {
    width: 100%;
}

/* Form Animations */
.form-input {
    transition: all var(--animation-duration-normal) var(--animation-easing-smooth);
}

.form-input:focus {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(220, 38, 38, 0.1);
}

/* Scroll Animations */
.scroll-reveal {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.8s var(--animation-easing-smooth);
}

.scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

/* Mobile Optimizations */
@media (max-width: 768px) {
    .hover-lift:hover,
    .hover-scale:hover,
    .hover-scale-sm:hover {
        transform: none;
    }
    
    .card-animate:hover {
        transform: translateY(-4px);
    }
    
    .btn-animate:hover {
        transform: translateY(-1px);
    }
}

/* Accessibility - Respect prefers-reduced-motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
    
    .animate-entrance,
    .animate-slide-in-left,
    .animate-slide-in-right,
    .animate-scale-in,
    .scroll-reveal {
        opacity: 1;
        transform: none;
    }
}

/* High Performance Mode */
.performance-mode * {
    animation: none !important;
    transition: none !important;
}

/* Focus States for Accessibility */
.focus-ring:focus {
    outline: 2px solid #DC2626;
    outline-offset: 2px;
    box-shadow: 0 0 0 4px rgba(220, 38, 38, 0.1);
}

/* Print Styles */
@media print {
    .animate-entrance,
    .animate-slide-in-left,
    .animate-slide-in-right,
    .animate-scale-in,
    .scroll-reveal {
        opacity: 1 !important;
        transform: none !important;
    }
}
