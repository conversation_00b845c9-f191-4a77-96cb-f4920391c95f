/* Profile Forms Styling - Harrier Design System */

/* Form Input Base Styles */
.form-input {
    @apply w-full px-4 py-3 border border-gray-300 rounded-xl 
           focus:ring-2 focus:ring-harrier-red focus:border-harrier-red 
           bg-white shadow-sm transition-all duration-200 font-raleway
           placeholder-gray-400 text-gray-900;
}

.form-input:hover {
    @apply border-gray-400 shadow-md;
}

.form-input:focus {
    @apply outline-none ring-2 ring-harrier-red/20 border-harrier-red 
           shadow-lg transform scale-[1.02];
}

.form-input:disabled {
    @apply bg-gray-100 text-gray-500 cursor-not-allowed;
}

/* Form Textarea Styles */
.form-input[type="textarea"], 
textarea.form-input {
    @apply resize-none;
}

/* Form Select Styles */
select.form-input {
    @apply cursor-pointer bg-white;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.75rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
}

/* Form Checkbox Styles */
.form-checkbox {
    @apply w-5 h-5 text-harrier-red bg-white border-2 border-gray-300 
           rounded-md focus:ring-2 focus:ring-harrier-red/20 
           focus:border-harrier-red transition-all duration-200
           cursor-pointer;
}

.form-checkbox:checked {
    @apply bg-harrier-red border-harrier-red;
}

.form-checkbox:hover {
    @apply border-harrier-red/60;
}

/* Form Radio Styles */
.form-radio {
    @apply w-5 h-5 text-harrier-red bg-white border-2 border-gray-300 
           focus:ring-2 focus:ring-harrier-red/20 focus:border-harrier-red 
           transition-all duration-200 cursor-pointer;
}

.form-radio:checked {
    @apply bg-harrier-red border-harrier-red;
}

/* Form Label Styles */
.form-label {
    @apply block text-sm font-bold text-harrier-dark font-montserrat mb-2;
}

.form-label.required::after {
    content: " *";
    @apply text-harrier-red;
}

/* Form Group Styles */
.form-group {
    @apply space-y-2 mb-6;
}

.form-group-inline {
    @apply flex items-center space-x-4 mb-4;
}

/* Form Section Styles */
.form-section {
    @apply bg-white/60 rounded-xl p-6 border border-gray-100 mb-6;
}

.form-section-header {
    @apply flex items-center mb-6;
}

.form-section-icon {
    @apply w-8 h-8 rounded-lg flex items-center justify-center mr-3;
}

.form-section-title {
    @apply text-lg font-bold text-harrier-dark font-montserrat;
}

.form-section-subtitle {
    @apply text-sm text-gray-600 font-raleway;
}

/* File Upload Styles */
.file-upload-area {
    @apply border-2 border-dashed border-gray-300 rounded-xl p-6 
           text-center hover:border-harrier-red transition-colors duration-200
           cursor-pointer bg-gray-50/50;
}

.file-upload-area.dragover {
    @apply border-harrier-red bg-harrier-red/5;
}

.file-upload-icon {
    @apply w-12 h-12 mx-auto mb-4 text-gray-400;
}

.file-upload-text {
    @apply text-sm text-gray-600 font-raleway;
}

.file-upload-button {
    @apply inline-flex items-center px-4 py-2 bg-gradient-to-r 
           from-blue-500 to-blue-600 text-white rounded-lg text-sm 
           font-medium hover:from-blue-600 hover:to-blue-700 
           transition-all duration-200 transform hover:scale-105 mt-3;
}

/* Profile Picture Upload */
.profile-picture-container {
    @apply relative inline-block;
}

.profile-picture-overlay {
    @apply absolute inset-0 bg-black/50 rounded-xl flex items-center 
           justify-center opacity-0 hover:opacity-100 transition-opacity 
           duration-200 cursor-pointer;
}

.profile-picture-edit-icon {
    @apply text-white text-lg;
}

/* Form Validation Styles */
.form-error {
    @apply text-red-600 text-sm font-medium mt-1 flex items-center;
}

.form-error-icon {
    @apply w-4 h-4 mr-1;
}

.form-success {
    @apply text-green-600 text-sm font-medium mt-1 flex items-center;
}

.form-success-icon {
    @apply w-4 h-4 mr-1;
}

/* Form Button Styles */
.form-button-primary {
    @apply inline-flex items-center px-8 py-3 bg-gradient-to-r 
           from-harrier-red to-harrier-red-dark text-white rounded-xl 
           font-bold hover:from-harrier-red-dark hover:to-harrier-red 
           transform hover:scale-105 transition-all duration-200 
           shadow-lg hover:shadow-xl font-montserrat;
}

.form-button-secondary {
    @apply px-6 py-3 bg-white border border-gray-300 text-gray-700 
           rounded-xl font-medium hover:bg-gray-50 transition-all 
           duration-200 transform hover:scale-105;
}

.form-button-danger {
    @apply px-6 py-3 bg-gradient-to-r from-red-500 to-red-600 
           text-white rounded-xl font-medium hover:from-red-600 
           hover:to-red-700 transition-all duration-200 transform 
           hover:scale-105;
}

/* Character Counter */
.character-counter {
    @apply text-xs text-gray-500 mt-1 text-right;
}

.character-counter.warning {
    @apply text-yellow-600;
}

.character-counter.error {
    @apply text-red-600;
}

.character-counter.success {
    @apply text-green-600;
}

/* Form Progress Indicator */
.form-progress {
    @apply w-full bg-gray-200 rounded-full h-2 mb-6;
}

.form-progress-bar {
    @apply bg-gradient-to-r from-harrier-red to-harrier-red-dark 
           h-2 rounded-full transition-all duration-300;
}

/* Mobile-First Responsive Design */
@media (max-width: 640px) {
    /* Mobile Form Inputs */
    .form-input {
        @apply px-3 py-3 text-base rounded-lg;
        min-height: 44px; /* Touch-friendly minimum */
    }

    .form-section {
        @apply p-4 mx-2 rounded-lg;
    }

    .form-section-header {
        @apply flex-col items-start space-y-2;
    }

    .form-section-icon {
        @apply w-6 h-6 mr-0 mb-2;
    }

    /* Mobile Buttons */
    .form-button-primary,
    .form-button-secondary,
    .form-button-danger {
        @apply w-full justify-center py-4 text-base;
        min-height: 48px;
    }

    /* Mobile Profile Header */
    .profile-picture-container img,
    .profile-picture-container div {
        @apply h-20 w-20;
    }

    /* Mobile Navigation Tabs */
    .profile-tab {
        @apply px-3 py-3 text-sm;
        min-height: 44px;
    }

    /* Mobile File Upload */
    .file-upload-area {
        @apply p-4;
    }

    .file-upload-icon {
        @apply w-8 h-8;
    }

    /* Mobile Form Groups */
    .form-group-inline {
        @apply flex-col items-start space-y-2 space-x-0;
    }

    .form-group-inline .form-checkbox {
        @apply mb-2;
    }

    /* Mobile Grid Adjustments */
    .grid.grid-cols-2 {
        @apply grid-cols-1;
    }

    .grid.grid-cols-3 {
        @apply grid-cols-1;
    }

    .grid.grid-cols-4 {
        @apply grid-cols-2;
    }
}

@media (max-width: 768px) {
    /* Tablet Adjustments */
    .form-input {
        @apply px-3 py-2 text-base;
    }

    .form-section {
        @apply p-5;
    }

    .form-button-primary,
    .form-button-secondary {
        @apply w-full justify-center;
    }

    /* Profile Header Adjustments */
    .profile-header-stats {
        @apply grid-cols-2 gap-2;
    }

    /* Settings Page Mobile */
    .settings-sidebar {
        @apply order-last mt-8;
    }
}

/* Touch-Friendly Interactions */
@media (hover: none) and (pointer: coarse) {
    /* Touch devices */
    .form-input:focus {
        @apply ring-4 ring-harrier-red/30;
    }

    .form-button-primary:active,
    .form-button-secondary:active {
        @apply scale-95;
    }

    .profile-tab:active {
        @apply bg-gray-100;
    }

    /* Larger touch targets */
    .profile-picture-overlay {
        @apply inset-0;
        min-height: 44px;
        min-width: 44px;
    }

    /* Touch-friendly checkboxes */
    .form-checkbox {
        @apply w-6 h-6;
    }
}

/* Swipe Gesture Support */
.swipe-container {
    touch-action: pan-x;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.swipe-container::-webkit-scrollbar {
    display: none;
}

/* Mobile Tab Navigation */
@media (max-width: 640px) {
    .profile-tabs-container {
        @apply overflow-x-auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    .profile-tabs-container::-webkit-scrollbar {
        display: none;
    }

    .profile-tabs-nav {
        @apply flex-nowrap min-w-max px-4;
    }

    .profile-tab {
        @apply whitespace-nowrap flex-shrink-0;
        min-width: 120px;
    }
}

/* Mobile Modal Adjustments */
@media (max-width: 640px) {
    .modal-content {
        @apply mx-2 my-4 max-h-screen overflow-y-auto;
    }

    .modal-header {
        @apply sticky top-0 bg-white z-10 border-b;
    }

    .modal-body {
        @apply px-4 py-6;
    }

    .modal-footer {
        @apply sticky bottom-0 bg-white border-t p-4;
    }
}

/* Mobile Analytics Charts */
@media (max-width: 640px) {
    .analytics-chart-container {
        @apply h-64;
    }

    .analytics-stats-grid {
        @apply grid-cols-1 gap-4;
    }

    .analytics-card {
        @apply p-4;
    }
}

/* Mobile Profile Completion */
@media (max-width: 640px) {
    .profile-completion-card {
        @apply p-4;
    }

    .completion-progress {
        @apply h-3;
    }

    .completion-items {
        @apply space-y-3;
    }
}

/* Mobile Business Hours */
@media (max-width: 640px) {
    .business-hours-day {
        @apply flex-col space-y-3 space-x-0;
    }

    .business-hours-toggle {
        @apply mb-3;
    }

    .business-hours-times {
        @apply grid-cols-1 gap-3;
    }
}

/* Mobile Vendor Profile */
@media (max-width: 640px) {
    .vendor-header-content {
        @apply flex-col space-y-4 space-x-0;
    }

    .vendor-logo {
        @apply h-16 w-16;
    }

    .vendor-stats {
        @apply grid-cols-3 gap-2 text-center;
    }

    .vendor-actions {
        @apply flex-col space-y-2 space-x-0 w-full;
    }

    .vendor-action-button {
        @apply w-full justify-center;
    }
}

/* Accessibility Improvements for Mobile */
@media (max-width: 640px) {
    /* Focus indicators */
    .form-input:focus,
    .form-button-primary:focus,
    .form-button-secondary:focus {
        @apply ring-4 ring-offset-2;
    }

    /* High contrast mode support */
    @media (prefers-contrast: high) {
        .form-input {
            @apply border-2 border-black;
        }

        .form-button-primary {
            @apply bg-black text-white border-2 border-black;
        }
    }

    /* Reduced motion support */
    @media (prefers-reduced-motion: reduce) {
        .form-fade-in,
        .form-slide-in,
        .form-loading::after {
            animation: none;
        }

        .form-input:focus {
            transition: none;
        }
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .form-input {
        @apply bg-gray-800 border-gray-600 text-white placeholder-gray-400;
    }
    
    .form-input:focus {
        @apply border-harrier-red bg-gray-800;
    }
    
    .form-section {
        @apply bg-gray-800/60 border-gray-700;
    }
    
    .form-label {
        @apply text-gray-200;
    }
}

/* Animation Classes */
.form-fade-in {
    animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.form-slide-in {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Loading States */
.form-loading {
    @apply relative overflow-hidden;
}

.form-loading::after {
    content: '';
    @apply absolute inset-0 bg-gradient-to-r from-transparent 
           via-white/20 to-transparent;
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* HTMX Specific Styles */
.htmx-indicator {
    @apply hidden;
}

.htmx-request .htmx-indicator {
    @apply block;
}

.htmx-field-indicator {
    @apply transition-all duration-200;
}

.htmx-field-indicator.show {
    @apply block;
}

/* HTMX Loading States */
.htmx-request .form-input {
    @apply opacity-75 pointer-events-none;
}

.htmx-request .form-button-primary {
    @apply opacity-75 pointer-events-none;
}

/* HTMX Success Animation */
.htmx-success {
    animation: successPulse 0.6s ease-out;
}

@keyframes successPulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
    }
    50% {
        transform: scale(1.02);
        box-shadow: 0 0 0 10px rgba(34, 197, 94, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
    }
}

/* HTMX Error Animation */
.htmx-error {
    animation: errorShake 0.6s ease-out;
}

@keyframes errorShake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-5px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(5px);
    }
}

/* Auto-save Indicator */
.auto-save-indicator {
    @apply fixed top-4 right-4 bg-white border border-gray-200 rounded-lg
           shadow-lg px-4 py-2 flex items-center space-x-2 z-50
           transform translate-x-full transition-transform duration-300;
}

.auto-save-indicator.show {
    @apply translate-x-0;
}

.auto-save-indicator.saving {
    @apply border-blue-200 bg-blue-50;
}

.auto-save-indicator.saved {
    @apply border-green-200 bg-green-50;
}

.auto-save-indicator.error {
    @apply border-red-200 bg-red-50;
}

/* Real-time Validation */
.field-validating {
    @apply border-blue-300 bg-blue-50/50;
}

.field-valid {
    @apply border-green-300 bg-green-50/50;
}

.field-invalid {
    @apply border-red-300 bg-red-50/50;
}

/* HTMX Swapping Animation */
.htmx-swapping {
    @apply opacity-0 transform scale-95;
}

.htmx-settling {
    @apply opacity-100 transform scale-100 transition-all duration-300;
}

/* Progressive Enhancement */
.no-htmx .htmx-only {
    @apply hidden;
}

.htmx .no-htmx-only {
    @apply hidden;
}
