@tailwind base;
@tailwind components;
@tailwind utilities;



@import './layouts/container.css';
@import './layouts/sidebar.css';
@import './layouts/header.css';
@import './theme/default-colors.css';
@import './override/reboot.css';

@layer components {
  .container {
    @apply max-w-[1200px];
  }

  .landingpage .container {
    @apply max-w-[1320px];
  }

  body {
    @apply text-sm overflow-x-hidden dark:bg-dark;
  }

  .dropdown {
    @apply shadow-md bg-white dark:bg-dark relative rounded-sm dark:shadow-dark-md;
  }

  .card-title {
    @apply text-lg font-semibold text-dark dark:text-white
  }

  .card-subtitle {
    @apply text-sm dark:text-darklink font-medium
  }

  body {
    @apply text-link dark:text-darklink;
  }

  /*heading text color*/
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply text-dark dark:text-white font-semibold;
  }


  .border-ld {
    @apply border-border dark:border-darkborder;
  }

  .bg-hover {
    @apply hover:bg-lightgray
  }

  .form-control input {
    @apply border border-border dark:border-darkborder bg-transparent dark:bg-transparent w-full text-sm;
  }
  .form-control.active-error input {
    @apply border !border-error dark:!border-error bg-transparent dark:bg-transparent w-full text-sm text-error;
  }
  .form-control.search input {
    @apply border border-border dark:border-darkborder bg-transparent dark:bg-transparent w-full text-sm !ps-10;
  }

  .form-control-chat input {
    @apply rounded-md border-0 bg-transparent dark:bg-transparent w-full text-sm;
  }

  .form-control-chat input:focus {
    @apply !border-0 bg-transparent dark:bg-transparent w-full text-sm ring-transparent dark:ring-transparent;
  }

  .form-control-rounded input {
    @apply rounded-full border border-border dark:border-darkborder bg-transparent dark:bg-transparent w-full text-sm;
  }

  .form-control-rounded input:focus {
    @apply border-primary dark:border-primary outline-none shadow-none ring-offset-0 ring-transparent;
  }

  .form-control-textarea {
    @apply rounded-full border border-border dark:border-darkborder bg-transparent dark:bg-transparent w-full text-sm p-4;
  }

  .form-control-textarea:focus {
    @apply border-primary dark:border-primary outline-none shadow-none ring-offset-0 ring-transparent;
  }

  .form-control input:focus {
    @apply border-primary dark:border-primary outline-none shadow-none ring-offset-0 ring-transparent;
  }

  .form-control-input {
    @apply rounded-md border border-border dark:border-darkborder bg-transparent dark:bg-transparent w-full text-sm;
  }

  .form-control-input:focus {
    @apply border-primary dark:border-primary outline-none shadow-none ring-offset-0 ring-transparent;
  }

  .form-rounded-md input {
    @apply rounded-md border border-border dark:border-darkborder bg-transparent dark:bg-transparent w-full text-sm;
  }

  .form-rounded-md input:focus {
    @apply border-primary dark:border-primary outline-none shadow-none ring-offset-0 ring-transparent;
  }

  .form-rounded input {
    @apply rounded-full
  }


  .input-center input {
    @apply text-center;
  }


  input::placeholder {
    @apply text-darklink dark:text-darklink
  }

  .select-option select {
    @apply bg-muted border-0 text-darklink dark:text-white/80 py-2 ps-4 pe-9 w-auto focus:border-0 focus:ring-0 font-medium;
  }

  .select-md select {
    @apply border-ld bg-transparent dark:bg-dark w-full text-sm rounded-md focus:border-primary dark:focus:border-primary focus:ring-0;
  }
  .select-md.product select {
    @apply border-ld bg-transparent dark:bg-dark w-full text-sm rounded-md focus:border-primary dark:focus:border-primary focus:ring-0 !py-1.5;
  }

  .select-rounded select {
    @apply border-ld bg-transparent dark:bg-dark w-full text-sm rounded-full focus:border-primary dark:focus:border-primary focus:ring-0;
  }

  .select-md-transparent select {
    @apply border-ld bg-transparent dark:bg-gray-800/70 w-full text-sm rounded-md focus:border-primary dark:focus:border-primary focus:ring-0;
  }

  .select-rounded-transparent select {
    @apply border-ld bg-transparent dark:bg-gray-800/70 w-full text-sm rounded-full focus:border-primary dark:focus:border-primary focus:ring-0;
  }

  .checkbox {
    @apply h-[18px] w-[18px] border border-bordergray dark:border-darkborder bg-transparent focus:ring-0 focus:ring-offset-0
  }

  .text-primary-ld {
    @apply hover:text-primary dark:hover:text-primary;
  }
  .custom-product-modal h3{
    @apply text-lg font-semibold;
  }


  /* Button Hover Color Set */
  button.bg-primary {
    @apply hover:bg-primaryemphasis
  }

  button.bg-secondary {
    @apply hover:bg-secondaryemphasis
  }

  button.bg-info {
    @apply hover:bg-infoemphasis
  }

  button.bg-error {
    @apply hover:bg-erroremphasis
  }

  button.bg-success {
    @apply hover:bg-successemphasis
  }

  button.bg-warning {
    @apply hover:bg-warningemphasis
  }
}