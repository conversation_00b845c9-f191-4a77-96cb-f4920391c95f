
import CardBox from "@/app/components/shared/CardBox";
import React from "react";
import Link from "next/link";
import AuthForgotPassword from "../authforms/AuthForgotPassword";
import { But<PERSON> } from "flowbite-react";
import type { Metadata } from "next";
import FullLogo from "@/app/(DashboardLayout)/layout/shared/logo/FullLogo";
export const metadata: Metadata = {
  title: "Boxed Forgot Password Auth",
  description: "Generated by create next app",
};


const BoxedForgotpwd = () => {
  return (
    <>
      <div className="relative overflow-hidden h-screen bg-lightprimary dark:bg-darkprimary">
        <div className="flex h-full justify-center items-center px-4">
          <CardBox className="md:w-[450px] w-full border-none">
          <div className="mx-auto mb-6">
              <FullLogo />
            </div>
            <p className="text-darklink text-sm text-center my-4">Please enter the email address associated with your account and We will email you a link to reset your password.</p>
            <AuthForgotPassword />
            <Button
                  color={"lightprimary"}
                  as={Link}
                  href="/auth/login"
                  className="rounded-md w-full mt-3"
                >
                  Back to Login
                </Button>
          </CardBox>
        </div>
      </div>
    </>
  );
};

export default BoxedForgotpwd;
