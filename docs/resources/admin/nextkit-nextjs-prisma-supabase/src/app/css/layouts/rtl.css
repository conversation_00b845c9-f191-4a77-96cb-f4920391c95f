html[dir="rtl"] .page-wrapper {
    margin-right: 260px;
    margin-left: 0;
}

html[dir="rtl"] .menu-sidebar {
    left: unset;
    right: 0px;
}


html[dir="rtl"][data-layout='horizontal'] .page-wrapper {
    margin-right: 0 !important
}

html[dir="rtl"][data-sidebar-type="mini-sidebar"] .brand-logo {
    overflow: hidden;
}
html[dir="rtl"][data-sidebar-type="mini-sidebar"] .page-wrapper {
    margin-left: unset !important;
    margin-right: 80px !important;
}

@media (max-width: 1199px) {
    html[dir="rtl"] [data-sidebar-type="mini-sidebar"] .menu-sidebar {
        left: unset;
        right: -260px
    }

    html[dir="rtl"] .page-wrapper {
        margin-right: 0 !important;
    }

    html[dir="rtl"][data-sidebar-type="mini-sidebar"] .page-wrapper {
        margin-left: unset !important;
        margin-right: 0px !important;
    }
}

html[dir="rtl"] select:not([size]) {
    padding-left: 35px;
}



html[dir="rtl"] .megamenu {
    transform: translate(-95%, 40px) !important;
}

html[dir="rtl"] .translate {
    transform: translate(0px, 40px) !important;
}

html[dir="rtl"] .translate-lng{
    transform: translate(0px, 40px) !important;
}

html[dir="rtl"] table tr th,
html[dir="rtl"] table tr td {
    text-align: right;
}