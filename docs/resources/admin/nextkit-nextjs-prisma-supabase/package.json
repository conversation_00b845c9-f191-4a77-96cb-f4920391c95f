{"name": "nextkit-nextjs-prisma-supabase", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "postinstall": "prisma generate && flowbite-react patch"}, "dependencies": {"@casl/ability": "6.3.3", "@casl/react": "3.1.0", "@headlessui/react": "^2.1.2", "@iconify/react": "4.1.1", "@prisma/client": "^6.9.0", "@reduxjs/toolkit": "1.9.7", "@supabase/supabase-js": "^2.49.1", "@tabler/icons-react": "3.5.0", "@tailwindcss/postcss": "^4.1.6", "apexcharts": "^3.49.1", "axios": "^1.7.9", "axios-mock-adapter": "1.22.0", "chance": "1.1.11", "flowbite": "^3.1.2", "flowbite-react": "^0.11.7", "i18next": "^23.12.2", "lodash": "^4.17.21", "moment": "2.29.4", "next": "^15.1.0", "nextjs-toploader": "^3.7.15", "react": "^19.0.0", "react-apexcharts": "^1.7.0", "react-dom": "^19.0.0", "react-i18next": "^15.0.0", "react-icons": "^5.5.0", "react-toastify": "^11.0.5", "simplebar-react": "3.2.5", "tailwind-merge": "^3.3.0", "yup": "1.4.0"}, "devDependencies": {"@types/chance": "1.1.6", "@types/lodash": "^4.17.13", "@types/node": "20", "@types/react": "18", "@types/react-dom": "18", "@types/react-is": "18.3.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "prisma": "^6.9.0", "tailwindcss": "^4.1.6", "typescript": "5"}}