

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model product {
  id           Int   @id @default(autoincrement())
  name         String
  customer     String
  quantity     String
  status       String
  price        String
  imageUrl     String
  createDate   DateTime @default(now())
  updateDate   DateTime @updatedAt
}