/*-----------------------------------------------------------------------------

	-	Revolution Slider 4.1 Captions -

		Screen Stylesheet

version:   	1.4.5
/*************************
	-	CAPTIONS	-
**************************/

.home-slider5 #thmg-slideshow {
    padding: 0;
    margin:auto;
   
}

.home-slider5 #thmg-slideshow .content_slideshow {
    position: absolute;
    top: 0;
    left: 0px;
    right: 15px;
	left: 15px;
	top: 15%;
}

.home-slider5 .info {
    width: 100%;
    display: block;
    text-align: left;
    position: absolute;
    margin-top: 12%;
	font-family:'Saira Condensed', sans-serif;


}

.home-slider5 #thmg-slideshow .container {position:relative}
.home-slider5 #thmg-slideshow .slotholder {position:absolute}

.home-slider5 .rev_slider_wrapper {
    position: relative;
}
.home-slider5 .rev_slider {
    position: relative;
    overflow: visible;
}
.home-slider5 .rev_slider ul {
    margin: 0px;
    padding: 0px;
    list-style: none !important;
    list-style-type: none;
    background-position: 0px 0px;
}
.home-slider5 .rev_slider ul li,
.home-slider5 .rev_slider >ul >li,
.home-slider5 .rev_slider >ul >li:before {
    list-style: none !important;
    position: absolute;
    visibility: hidden;
    margin: 0px !important;
    padding: 0px !important;
    overflow-x: visible;
    overflow-y: visible;
    list-style-type: none !important;
    background-image: none;
    background-position: 0px 0px;
    text-indent: 0em;
}
.home-slider5 .tp-caption {
    z-index: 1;
}
.home-slider5 .fullwidthbanner-container {
    width: 100%;
    position: relative;
    padding: 0;
    overflow: hidden;
    margin: auto;
}
.home-slider5 .fullwidthbanner-container .fullwidthabanner {
    width: 100%;
    position: relative;
    margin: auto;
}
.tp-simpleresponsive .tp-caption {
 
	
}

.caption-inner {width:60%}

.home-slider5 .caption-inner.left {
    text-align: left;
	width:60%
}
.home-slider5 .caption-inner.right {
    text-align: left;
}
.tp-caption.ExtraLargeTitle {
	line-height:normal;
	font-weight: 300;
	color: #0082ba;
	text-decoration: none;
	background-color: transparent;
	padding: 0px 0px 0px 0px;
	border-radius: 0px 0px 0px 0px;
	font-size: 18px;
	text-align: left;
	letter-spacing: 0px;
	
}
.tp-caption.slide2 {
	color: #FFF;
}
.tp-caption.ExtraLargeTitle span {
	display: inline-block;
	margin: auto;
	letter-spacing:1px;
	font-size:32px;
	font-weight: normal;
	color:#6c6;
	text-transform:uppercase
}
.tp-caption.LargeTitle {
	font-weight: 700;
	color:#fff;
	text-decoration: none;
	background-color: transparent;
	padding: 5px 0px 10px 0px;
	font-size:105px;
	text-align: left;
	letter-spacing:1px;
	line-height: 54px;
	text-transform:uppercase
}
.tp-caption.LargeTitle span {
	color: #fff;
	line-height: 85px;
	padding-left: 0px;
	padding-right: 20px;
	border-radius: 5px;
	text-align: left;
	display: inline-block;
	font-weight:normal;
	font-size: 68px;
	letter-spacing:1px
}
.view-more {
	padding: 10px 35px;
	font-size: 12px;
	text-transform: uppercase;
	font-weight: bold;
	color: #EEE;
	border: 1px #EEE solid;
}
.buy-btn {
	padding: 15px 20px 12px 20px;
	font-size: 18px;
	text-transform: uppercase;
	font-weight: 400;
	color: #3e6274;
	letter-spacing: 1px!important;
	display: inline-block;
	border:none;
	margin-top: 45px;
	float: left;
	background: #d9eb3d ;
	border-radius:0px;
	border-bottom:3px #c6d535 solid
}
.buy-btn:hover {
	background: #fed700;
	color: #333;
	border: 1px #fed700 solid;
}
.buy-btn:hover:after {
	color: #333;
}
.buy-btn:after {
	content: '\f0da';
	font-family: FontAwesome;
	display: inline-block !important;
	margin-left: 10px;
	color: #333;
	font-size: 14px;
}
.tp-caption.Title {
	font-size:22px;
	line-height: 27px;
	font-weight: 300;
	text-transform: uppercase;
    color: #fff;
	text-decoration: none;
	background-color: transparent;
	padding: 20px 0px 0px 0px;
	text-align: left;
	margin: auto;
	letter-spacing: 1px;
	
}
.home-slider5 .tp-bullets {
    z-index: 1000;
    position: absolute;
    opacity: 1;
}
.home-slider5 .tp-bullets.hidebullets {
    opacity: 0;
}

.home-slider5 .tparrows {
    opacity: 1;
}
.home-slider5 .tparrows.hidearrows {
    /*-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
	-moz-opacity: 0;
	-khtml-opacity: 0;
	opacity: 0;*/
}
.home-slider5 .tp-leftarrow {
    z-index: 100;
    cursor: pointer;
    position: relative;
    width: 30px;
    height: 30px;
	background:#fff
}
.home-slider5 .tp-leftarrow:before {
    content: "\f104";
    font-family: 'FontAwesome';
font-size:24px;
    color:rgba(51,62,72,1.00);
}
.home-slider5 .tp-rightarrow:before {
    content: "\f105";
    font-family: 'FontAwesome';
    font-size: 24px;
    color:rgba(51,62,72,1.00);
}
.home-slider5 .tp-rightarrow {
    z-index: 100;
    cursor: pointer;
    position: relative;
    width: 30px;
    height: 30px;
	background:#fff
}
.home-slider5 .rev_slider_wrapper .tp-leftarrow.default {
    z-index: 100;
    cursor: pointer;
    left: -45px !important;
    opacity: 0px;
    visibility: hidden;
    transition: all 0.3s ease-in-out;
}
.home-slider5 .rev_slider_wrapper:hover .tp-leftarrow.default {
    z-index: 100;
    cursor: pointer;
    left: 15px !important;
    opacity: 1px;
    visibility: visible;
    transition: all 0.3s ease-in-out;
}
.home-slider5 .rev_slider_wrapper .tp-rightarrow.default {
    z-index: 100;
    cursor: pointer;
    right: -45px !important;
    opacity: 0px;
    visibility: hidden;
    transition: all 0.3s ease-in-out;
}
.home-slider5 .rev_slider_wrapper:hover .tp-rightarrow.default {
    z-index: 100;
    cursor: pointer;
    right: 15px !important;
    opacity: 1px;
    visibility: visible;
    transition: all 0.3s ease-in-out;
}
.home-slider5 .tp-bullets.tp-thumbs {
    /*z-index: 1000; */
    
    position: absolute;
    padding: 3px;
    background-color: #fff;
    width: 500px;
    height: 50px;
    /* THE DIMENSIONS OF THE THUMB CONTAINER */
    
    margin-top: -50px;
    vertical-align: top;
    display: none;
}
.home-slider5 .fullwidthbanner-container .tp-thumbs {
    padding: 3px;
    margin: auto -160px !important;
}
.home-slider5 .tp-bullets.tp-thumbs .tp-mask {
    width: 500px;
    height: 50px;
    /* THE DIMENSIONS OF THE THUMB CONTAINER */
    
    overflow: hidden;
    position: relative;
}
.home-slider5 .tp-bullets.tp-thumbs .tp-mask .tp-thumbcontainer {
    width: 500px;
    position: absolute;
}
.home-slider5 .tp-bullets.tp-thumbs .bullet {
    width: 100px;
    height: 50px;
    /* THE DIMENSION OF A SINGLE THUMB */
    
    cursor: pointer;
    overflow: hidden;
    background: none;
    margin: 0;
    float: left;
}
.home-slider5 .tp-bullets.tp-thumbs .bullet:hover,
.tp-bullets.tp-thumbs .bullet.selected {
    opacity: 1;
}
.home-slider5 .tp-simpleresponsive ul {
    list-style: none;
    padding: 0;
    margin: 0;
}
.home-slider5 .tp-simpleresponsive >ul li {
    list-style: none;
    position: absolute;
    visibility: hidden;
}
/*  CAPTION SLIDELINK   **/

.home-slider5 .tp-leftarrow.default,
.home-slider5 .tp-rightarrow.default {
    font-family: FontAwesome;
    font-size: 11px;
    font-weight: normal;
    height: 50px;
    width: 50px;
    text-align: center;
    line-height: 50px;

}
.home-slider5 .tp-leftarrow.default:hover,
.home-slider5 .tp-rightarrow.default:hover {
    background-color: #d9eb3d;
}
.home-slider5 .forcefullwidth_wrapper_tp_banner,
.fullwidthbanner-container {
    max-width: 100%;
    left: 0 !important;
    margin: auto !important;
    overflow: hidden;
}

@media only screen and (min-width: 320px) and (max-width: 479px) {
    .home-slider5 .tp-button {
        padding: 2px 5px 2px;
        line-height: 20px !important;
        font-size: 10px !important;
    }
	
	.home-slider5 #thmg-slideshow .content_slideshow {left:10%; top:0}
	.home-slider5 .info {margin-top:10%}
	.tp-caption.ExtraLargeTitle span {font-size:16px!important}
	.tp-caption.LargeTitle {font-size:26px!important; line-height:1.1em!important; margin-top:10px!important}
	.tp-caption.LargeTitle span {font-size:26px!important;}
	.tp-caption.Title {display:none}
	.buy-btn {font-size:13px!important; padding:10px!important; display:none}
	
}
@media only screen and (min-width: 480px) and (max-width: 767px) {
   	.home-slider5 #thmg-slideshow .content_slideshow {left:5%; top:0}
	.home-slider5 .info {margin-top:15%; left:20px}
	.tp-caption.ExtraLargeTitle span {font-size:16px!important}
	.tp-caption.LargeTitle {font-size:30px!important; line-height:1.1em!important; margin-top:10px!important}
	.tp-caption.LargeTitle span {font-size:36px!important;}
	.tp-caption.Title  {font-size:21px!important}
	.buy-btn {font-size:13px!important; padding:10px!important; display:none}
}
@media only screen and (min-width: 768px) and (max-width: 992px) {
    .home-slider5 .side-home-banner {
        text-align: center;
        display: inline-block;
        width: 100%;
    }
}