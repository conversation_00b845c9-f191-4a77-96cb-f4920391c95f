/*
* 	Owl Carousel Owl Demo Theme 
*	v1.3.3
*/

.owl-theme .owl-controls {
	margin-top: 10px;
	text-align: center;
}
/* Styling Next and Prev buttons */

.owl-theme .owl-controls .owl-buttons div {
	color: #FFF;
	display: inline-block;
	zoom: 1;
 *display: inline;/*IE7 life-saver */
	padding: 5px 10px;
	font-size: 12px;
	-webkit-border-radius: 30px;
	-moz-border-radius: 30px;
	border-radius: 30px;
	background: #fff;
	filter: Alpha(Opacity=50);/*IE7 fix*/
	opacity: 1;
}
/* Clickable class fix problem with hover on touch devices */
/* Use it for non-touch hover action */
.owl-theme .owl-controls.clickable .owl-buttons div:hover {
	filter: Alpha(Opacity=100);/*IE7 fix*/
	opacity: 1;
	text-decoration: none;
}
/* Styling Pagination*/

.owl-theme .owl-controls .owl-page {
	display: inline-block;
	zoom: 1;
 *display: inline;/*IE7 life-saver */
}
.owl-theme .owl-controls .owl-page span {
	display: block;
	width:16px;
	height: 16px;
	margin: 5px 0px;
	filter: Alpha(Opacity=50);/*IE7 fix*/
	-webkit-border-radius: 20px;
	-moz-border-radius: 20px;
	border-radius: 20px;
	background: #ddd;
	border:2px #fff solid
}
.owl-theme .owl-controls .owl-page.active span, .owl-theme .owl-controls.clickable .owl-page:hover span {
	filter: Alpha(Opacity=100);/*IE7 fix*/
	opacity: 1;
	background: #0088cc;
}
/* If PaginationNumbers is true */

.owl-theme .owl-controls .owl-page span.owl-numbers {
	height: auto;
	width: auto;
	color: #FFF;
	padding: 2px 10px;
	font-size: 12px;
	-webkit-border-radius: 30px;
	-moz-border-radius: 30px;
	border-radius: 30px;
}
/* preloading images */
.owl-item.loading {
	min-height: 150px;
	background: url(AjaxLoader.html) no-repeat center center
}


.owl-pagination{  top: -36px;
  text-align: center;
  position: absolute;
  right: 0px;
  background:#fff;
  padding-left:10px
  }
